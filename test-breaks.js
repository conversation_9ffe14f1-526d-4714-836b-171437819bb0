// Simple test script to verify breaks functionality
const { upsertSlots, getScheduleSlots } = require('./src/service/serviceProvider/scheduling');

async function testBreaks() {
  try {
    console.log('Testing breaks functionality...');
    
    // Test data with breaks
    const testSlot = {
      date: '2024-01-15',
      day: 'Monday',
      fromTime: '09:00',
      toTime: '17:00',
      serviceProviderId: 1,
      businessLocationId: 1,
      breaks: [
        {
          startTime: '12:00',
          endTime: '13:00'
        },
        {
          startTime: '15:00',
          endTime: '15:30'
        }
      ]
    };

    console.log('Creating slot with breaks...');
    await upsertSlots([testSlot]);
    
    console.log('Retrieving slots...');
    const slots = await getScheduleSlots(1, 1, true, '2024-01');
    
    console.log('Retrieved slots:', JSON.stringify(slots, null, 2));
    
    // Find our test slot
    const createdSlot = slots.find(s => s.date === '2024-01-15');
    if (createdSlot && createdSlot.breaks) {
      console.log('✅ Success! Slot created with breaks:', createdSlot.breaks.length);
    } else {
      console.log('❌ Failed! No breaks found');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testBreaks();
}

module.exports = { testBreaks };
