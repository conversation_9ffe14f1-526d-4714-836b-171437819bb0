{"email": "The :attr must be a valid email address", "required": "The :attr field is required", "user_id": "The :attr field is required", "in": "The selected :attr is invalid", "interger": ":attr must be digits", "required_if": "The :attr field is required when :other is :value", "_keyword": "keyword", "keyword_id": "Id", "page": "page", "keyword_addressid": "Address id", "keyword_user_id": "User id", "keyword_name": "Name", "keyword_firstname": "Name", "keyword_product_name": "Product Name", "keyword_email": "Email", "keyword_password": "Password", "keyword_confirm_password": "Confirm password is required", "keyword_latitude": "Latitude", "keyword_longitude": "Longitude", "keyword_phone": "Phone", "keyword_country_code": "Country code", "keyword_city": "City", "keyword_state": "State", "keyword_address": "Address", "keyword_address_type": "Address Type", "keyword_area": "Area", "keyword_street": "Street", "keyword_house_no": "Flat No", "keyword_landmark": "Landmark", "keyword_postal_code": "Postal code", "keyword_otpcode": "OTP Code", "keyword_devicetype": "Device type", "keyword_devicetoken": "Device token", "keyword_old_password": "Old password", "keyword_new_password": "New password", "keyword_product_id": "Product id", "keyword_rating": "Rating", "keyword_profile_image": "Profile picture", "keyword_gender": "Gender", "keyword_dob": "Date of birth", "keyword_productid": "Product id", "keyword_categoryid": "Category id", "keyword_subcategoryid": "Sub category id", "keyword_price_range": "Price range", "keyword_price": "Price", "keyword_status": "Status", "keyword_word": "Search word", "keyword_service_id": "Service Id", "keyword_page": "page", "keyword_categoryname": "Category name", "keyword_quantity": "Quantity", "keyword_quantity_id": "Product quantity id", "keyword_size": "Size", "keyword_promocode": "Promo code", "keyword_paymentmode": "Payment mode", "keyword_canceldescription": "Cancel description", "keyword_reason": "Cancel reason", "keyword_order_id": "Order id", "keyword_cardid": "Card id", "kkeyword_retailerid": "Retailer id", "keyword_forgot_otp": "Forgot otp", "keyword_description": "Description", "keyword_subject": "Subject", "keyword_cardholdername": "Card holder name", "keyword_bank_name": "Bank name", "keyword_account_holder_name": "Account holder name", "keyword_account_number": "Account number", "keyword_routing_number": "Routing number", "keyword_publish": "Publish", "keyword_variant_list": "<PERSON><PERSON><PERSON>", "keyword_ssn": "SSN", "keyword_cvv": "CVV", "keyword_expirymonth": "Expiry month", "keyword_expiryyear": "Expiry year", "keyword_cardnumber": "Card number", "keyword_shippingcharge": "Shipping charge", "keyword_discount": "Discount", "keyword_totalamoount": "Total amount", "keyword_target": "Target", "keyword_show_home_target": "Show in home target", "keyword_amount": "Amount", "keyword_subtotal": "Sub total", "Rest_api_message": "Rest api error message", "keyword_notes": "Notes", "keyword_story_id": "Story id", "keyword_product_image": "Product Image", "keyword_images": "Post Image", "restapi_login_password_error": "Email id or password is wrong", "restapi_login_errormsg": "Please enter valid  email or phone number", "rest_keywords_invalid_emailorphone_pass_login": "Password does not match with email or phone number", "restapi_login_password_phoneerror": "Phone number or password is Wrong", "restapi_phone_alreadyexists_error": "Phone number already exists", "restapi_socialid_alreadyexists_error": "Social account already exists", "password_confirmpassword_not_match": "Password and confirm password not same", "restapi_email_alreadyexists_error": "This email is already taken. Please use another Email", "restapi_client_alreadyexists_error": "This Client already exist please create another client", "restapi_phonealreadyexists_error": "This phone number is already taken. Please use another phone number", "restapi_globals_error": "Something's gone wrong. Please try again.", "restapi_signup_sucess": "Thank you for registering with <PERSON><PERSON><PERSON>.", "restapi_logindisactive_error": "Your account is inactive. Please contact your administrator.", "restapi_loginotpverify_error": "Please verify your mobile number.", "restapi_login_sucess": "You are logged in.", "restapi_otpsent_success": "OTP resent successfully", "restapi_sentotp_error": "Oops! Something's gone wrong. The OTP could not be sent. Please try again", "restapi_verifyotp_success": "OTP verified successfully", "restapi_addadress_sucess": "Address added successfully", "restapi_updateadress_sucess": "Address updated successfully", "restapi_deleteadress_sucess": "Address deleted successfully", "restapi_verifyotp_error": "Invalid OTP", "restapi_loginemail_error": "Please enter valid email address", "restapi_personalinfo_error": "Please add personal information ", "restapi_phone_error": "Please enter valid phone number", "restapi_phonelogin_error": "Invalid phone number or password", "restapi_emaillogin_error": "Invalid email id or password", "restapi_mail_error": "Email not sent. Please try again", "restapi_forgotpass_error": "OTP Can Not sent to Mobile Or Phone", "restapi_forgotpassword_success": "Forgot password otp is sent to your registered email.", "restapi_forgotpassword_phonesuccess": "Forgot password otp is sent to your registered phone number", "restapi_editprofile_success": "Profile updated successfully", "restapi_logout_success": "You have successfully logged out.", "reatapi_user_old_new_password_same": "Old password and new password can not be same.", "restapi_user_change_password_fail": "Please enter correct old password", "restapi_user_change_password_success": "Password changed successfully.", "restapi_passwordreset_success": "Password reset successfully.", "restapi_user_notfound": "User detail not found", "restapi_review_success": "Review added successfully", "restapi_reviewnotadd": "Review Can not add", "restapi_location_success": "Location updated successfully", "restapi_user_detail": "User detail found successfully", "restapi_addpersonalinfo_success": "User detail added successfully", "restapi_productcateoryfound": "Product category found successfully", "restapi_cateorynotfound": "Product category not found", "restapi_product_deleted": "Product deleted successfully", "restapi_service_deleted": "Service deleted successfully", "restapi_service_detail_found": "Service detail found successfully", "restapi_service_detail_notfound": "Service detail not found", "restapi_product_notdeleted": "Product can not be deleted", "restapi_service_notdeleted": "Service can not be deleted", "restapi_product_notadd": "Product can not be added.Please try again later", "restapi_productsubcateoryfound": "Product sub category found successfully", "restapi_subcateoryfound": "Sub category found successfully", "restapi_subcateorynotfound": "Product sub category not found", "restapi_quantitynotfound": "Sub product not found", "restapi_productinactive": "Product is inactive", "restapi_productfound": "Products found successfully", "restapi_productnotfound": "Product not found", "restapi_favouriteadd": "Favourite item added successfully", "restapi_favouriteremove": "Favourite item remove successfully", "restapi_addtocart_success": "Item added to cart successfully", "restapi_removecart_success": "Item remove from cart successfully", "restapi_cartdetailfound": "Cart details found successfully", "restapi_cartdetailnotfound": "Cart details not found", "restapi_cancelorder_success": "Cancel order successfully", "restapi_cancelorder_details": "Cancel details found successfully", "restapi_orderplaced": "Your order has been successfully placed", "restapi_alreadyaddcard_error": "You already added this card", "restapi_addcard_success": "Card added successfully", "restapi_removecard_success": "Card deleted successfully", "restapi_carddetails_notfound": "Card details not found", "restapi_cardlist_notfound": "You have not added any card(s)", "restapi_cardlist_found": "Card List found", "restapi_contactus_success": "Contact us successfully", "restapi_homescreendata_found_success": "Home screen store data found successfully", "restapi_categorystorelistnot_found": "Store data not found", "restapi_bulkproduct_found": "Bulk product found successfully", "restapi_bulkproduct_notfound": "Bulk product not found", "restapi_address_found": "Address found successfully", "restapi_address_notfound": "Address not found. Please add new address", "restapi_favourite_found": "Favourite item found successfully", "restapi_favourite_notfound": "Favourite item not found", "restapi_favouritremove": "Favourite item remove successfully", "restapi_alreadyfavourite_product": "Product is already added to favourite", "resyapi_notafavourite_product": "This product is not in favourite", "restapi_cancelreason_detailfound": "<PERSON><PERSON> reason found successfully", "restapi_reportreason_detailfound": "Report reason found successfully", "restapi_cancelorder_notfound": "<PERSON><PERSON> reason not found", "restapi_promocode_isnotusable_yet": "Promo code is not started", "restapi_promocode_found": "Promo code found successfully", "restapi_promocode_notfound": "Promo code not found", "restapi_promocode_maxusagelimit_over": "Promo code maximum limit reached", "restapi_promocode_already_used": "Promo code is already used", "reatapi_promocode_success": "Promo code applied successfully", "restapi_promocode_invalid": "Invalid Promo code", "restapi_promocode_isnotforthisuser": "This promo code is only application on some specified users", "restapi_promocode_isnotforthisproduct": "This promo code is only application on some specified products", "restapi_promocode_isnotforthisservice": "This promo code is only application on some specified services", "restapi_promocode_isnotforthisserviceprovider": "This promo code is only application on some specified service providers", "restapi_brand_found": "Brand found successfully", "resstapi_dealsoftheday_success": "Deals of the day found successfully", "resstapi_dealsoftheday_notfound": "Deals of the day not found", "restapi_promocode_isexpired_now": "Promo code is expired", "restapi_product_review_notfound": "Review not found", "restapi_product_review_found": "Review found successfully", "restapi_brand_notfound": "Brand not found", "rest_keywords_customerapp_otpcode": "Thanks for registering at {app_name}, Your verification code is {otp_code}", "restapi_order_notfound": "Your order details not found", "restapi_order_placefail": "Order can not be placed please try after sometime", "restapi_interest_rate": "Interest rate found successfully", "restapi_invalid_refercode": "Invalid refer code", "restapi_valid_refercode": "Your refer code is valid", "restapi_referamount_notfound": "Refer amount not found", "restapi_referamount_found": "Refer amount found successfully", "restapi_filter_found": "Filter found successfully", "restapi_orderlist_found": "Your order list found successfully", "restapi_search_notfound": "Nothing match to your search", "rest_keywords_user_fb_not_found": "User social account not found", "restapi_country_found": "Country list found successfully", "restapi_coupon_found": "Coupon list found successfully", "restapi_coupon_notfound": "Coupon list can not found", "restapi_country_notfound": "Country list not found", "restapi_homescreen_found": "Home screen details found successfully", "restapi_story_notfound": "Story not found", "restapi_vendor_found": "Service provider details found successfully", "restapi_vendor_notfound": "Service provider details not found", "restapi_story_found": "Story found successfully", "restapi_collection_add": "Collection added successfully", "restapi_collection_update": "Collection updated successfully", "restapi_collection_delete": "Collection deleted successfully", "restapi_globals_collectionerror": "Collection not found... Please create collection", "restapi_collection_exist": "Collection already exist", "restapi_collection_found": "Collection found successfully", "restapi_offer_notfound": "Offer product not found", "restapi_offer_productfound": "Offer product found successfully", "restapi_collection_notfound": "Collection not found", "restapi_vendor_storeadd_success": "Store details added successfully", "restapi_vendor_bankadd": "Bank details added successfully", "restapi_time_success": "Store availability time added successfully", "restapi_time_fail": "Store availability time can not be added", "restapi_service_category_notfound": "Service list not found", "restapi_service_category_found": "Service category found successfully", "restapi_delivery_chargesuccess": "Delivery charge added successfully", "restapi_delivery_chargeupdate": "Delivery charge updated successfully", "restapi_delivery_chargefail": "Delivery charge can not add/update", "restapi_story_addsuccess": "Story added successfully", "restapi_story_editsuccess": "Story updated successfully", "restapi_storydetail_found": "Story detail found successfully", "restapi_storydetail_not_found": "Story detail not found", "restapi_story_addfail": "Story can not added", "restapi_story_deletesuccess": "Story deleted successfully", "restapi_story_deletefail": "Story can not be deleted", "restapi_story_image": "Story image is required", "restapi_unit_list": "Unit list found successfully", "restapi_color_list": "Color list found successfully", "restapi_unit_listnotfound": "Unit list not found", "restapi_color_listnotfound": "Color list not found", "restapi_charge_list": "Delivery Charge found successfully", "restapi_charge_listnotfound": "Delivery Charge list not found", "restapi_servicefound": "Service list found successfully", "restapi_subscription_found": "Subscription list found successfully", "restapi_servicenotfound": "Service list not found", "restapi_appointment_found": "Appointment found successfully", "restapi_appointment_not_found": "Appointment not found", "restapi_appointment_notfound": "Service list not found", "restapi_publish_success": "Product publish successfully", "restapi_product_add_success": "Product added successfully", "restapi_service_add_success": "Service added successfully", "restapi_service_edit_success": "Service edited successfully", "restapi_product_edit_success": "Product edited successfully", "restapi_publish_fail": "Product can not published.please try after sometime", "restapi_subscription_notfound": "Subscription list not found", "restapi_vendor_category_notfound": "Service provider category not found", "restapi_vendor_category_found": "Service provider category found successfully", "restapi_aadhar_alredy_use": "<PERSON><PERSON><PERSON><PERSON> card already exist.", "restapi_profile_approve": "Profile approval pending from admin", "restapi_profile_reject": "Profile reject by admin", "restapi_cashfree_status": "Payment status found successfully", "restapi_search_found": "Search vendor/product found successfully", "restapi_product_notedit": "Product can not be updated. Please try after sometime", "category_found_successfully": "Category found successfully", "restapi_status_failed": "Delivery status can not be change.Please try after sometime", "restapi_status_updated": "Delivery status updated successfully", "restapi_complete_profile_success": "Profile updated successfully", "restapi_bank_detail_success": "Bank detail added successfully", "restapi_client_add_success": "Client detail added successfully", "restapi_client_edit_success": "Client detail edited", "restapi_client_not_found": "Client list not found", "restapi_client_found": "Client list found successfully", "restapi_target_success": "Financial target added successfully", "restapi_post_success": "Post added successfully", "restapi_post_delete_success": "Post deleted successfully", "restapi_post_fail": "Post can not be added. Please try after sometime", "restapi_post_found": "Post found successfully", "restapi_post_notfound": "Post not found", "restapi_occational_promo_success": "Occasional promo added successfully", "restapi_occational_promo_not_found": "Occasional promo not found", "restapi_occational_promo_found": "Occasional promo found successfully", "restapi_occational_promo_delete": "Occasional promo deleted successfully", "restapi_slot_found": "Slot found successfully", "restapi_slot_notfound": "This service is not available on this date", "restapi_day_not_available": "This date is not available for booking", "restapi_order_found": "Your order details found successfully", "restapi_service_booked": "Service booked successfully", "rest_keywords_card_insert_success": "Card Details Add Successfully", "rest_keywords_card_insert_failed": "Card Details Not Add Successfully, Please try again", "restapi_location_not_found": "Booth location not found", "restapi_location_detail_not_found": "Location detail not found", "restapi_location_detail_found": "Location detail found successfully", "restapi_location_found": "Booth location found successfully", "rest_keywords_text_card_already_exists": "Card already exists, please add any other card", "text_card_already_exists": "Card already exists", "rest_keywords_text_card_add_fail": "Failed to add the card", "rest_keywords_card_list_success": "Card List Find Successfully", "restapi_notificaion_notfound": "Notification list not found", "restapi_notificaion_found": "Notification list found successfully", "rest_keywords_card_list_failed": "Card List Not Found, Please add card", "rest_keywords_followinglist_success": "Following list found", "rest_keywords_followinglist_notfound": "You are not following any service providers", "rest_keywords_followerslist_success": "Followers list found", "rest_keywords_followerslist_notfound": "You have no follower", "restapi_firstcompleterunningbooking_error": "You can't start two service at a time please complete your running booking", "restapi_firststartpastfutureservice_error": "You can't start past or future services", "restapi_blockslotnotavailable_error": "You have already appointment in this time duration.", "rest_keywords_service_history_not_found": "Service history not found", "rest_keywords_service_history_found": "Service history found successfully", "rest_keywords_product_history_not_found": "Product history not found", "rest_keywords_product_history_found": "Product history found successfully", "restapi_appointmentAccepted_success": "Booking request accepted successfully", "restapi_appointmentPaid_success": "Booking request paid successfully", "restapi_appointmentCompleted_success": "Booking request completed successfully", "restapi_appointmentIn Progress_success": "Booking request in progress successfully", "restapi_boothrentalreadyWaiting_error": "Booth Rent request is on a waiting status.", "restapi_boothrentalreadyAccepted_error": "Booth Rent request is on a confirm status.", "restapi_boothrentalreadyRejected_error": "Booth Rent request is rejected.", "restapi_boothrentalreadyProcessing_error": "Booth Rent request is started.", "restapi_boothrentalreadyCompleted_error": "Booth Rent request is completed.", "restapi_boothrentalreadyCancelled_error": "Booth Rent request is cancelled.", "restapi_boothrentAccepted_success": "Booth Rent Request has been accepted successfully", "restapi_boothrentRejected_success": "Booth Rent Request has been rejected successfully", "restapi_boothrentCancelled_success": "Booth Rent Request has been cancelled successfully", "restapi_boothbookinglist_found": "Booth booking list found", "restapi_boothbookinglist_notfound": "Booth booking list not found", "restapi_checkappversion_success": "Your App version is old, please update to latest version."}