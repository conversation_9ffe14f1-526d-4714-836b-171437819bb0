import 'module-alias/register'
import { pathsToModuleNameMapper } from "ts-jest";
import type {Config} from '@jest/types'

const config: Config.InitialOptions = {
  preset: "ts-jest",
  moduleDirectories: ["node_modules", "<rootDir>"],
  moduleNameMapper: pathsToModuleNameMapper(
    {
      "@/*": ["./"],
      "@/modules/*": ["./modules/*"],
      "@/src/*": ["./src/*"],
      "~/tests/*": ["./tests/*"],
    },
    {
      prefix: '<rootDir>'
    },
  ),
  testEnvironment: "node",
  verbose: true,
  transform: {
    "^.+\\.ts?$": "ts-jest",
  },
}

export default config
