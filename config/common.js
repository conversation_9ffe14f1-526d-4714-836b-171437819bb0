// var globals = require('../config/constant');
const { Global } = require('../src/constants')
const globals = Global

var Validate = require('Validator');
var datetime = require('node-datetime');
const cryptoLib = require('cryptlib');
const shakey = cryptoLib.getHashSha256(globals.key, 32);
var asyncLoop = require('node-async-loop');
var nodemailer = require('nodemailer');
const { t } = require('localizify');
var con = require("./database");
const asyncloop = require('node-async-loop');
const client = require('twilio')(globals.TWILIO_ACCOUNT_SID, globals.TWILIO_AUTH_TOKEN);
const get = require('lodash/get');
const { logger } = require("../src/utils");
const PushNotifications = require("node-pushnotifications");
const {
  getUserDeviceByToken,
  encrypt,
  decrypt,
  tryDecryptingRaw,
  USER_TYPE,
} = require('../src/service')
const { pick } = require("lodash");

function sendError(res, data = {}, statusCode = 401) {
  res.status(statusCode).json(encrypt(data))
}

var data = {
  validate_token: async function (req, res, next) {
    if (req.path.indexOf('v2') > -1
        || req.path.indexOf('api-docs') > -1
        || req.path.indexOf('.well-known') > -1
        || req.path.indexOf('/home/<USER>') > -1
    ) {
      logger.info({ message: `Skip old validation`, meta: { path: req.originalUrl } })
      return next()
    }

    logger.info({ message: 'Running old validation', meta: { path: req.originalUrl } })
    const apiKey = tryDecryptingRaw(req.headers['api-key'] ?? req.headers['API-KEY'], 'api key')
    if (apiKey !== globals.API_KEY) {
      return sendError(res, { code: -1, message: 'Invalid api key' })
    }

    const token = tryDecryptingRaw(req.headers['token'], 'token')
    const device = token ? await getUserDeviceByToken(token) : null
    req.user_id = device?.user_id ?? 0
    req.user_uuid = device?.user_uuid;
    req.user_type = device?.user_type ?? USER_TYPE.USER

    const skipTokenValidation = [
      'sign_up',
      'forgot_password',
      'complete_profile',
      'login',
      'send_otp',
      'verify_otp',
      'newpassword',
      'add_personalinfo',
      'country_list',
      'verify_profile',
      'add_bank_details',
      'checkappupdate',
      'createtransfers',
    ].includes(req.path.split('/')[3])
    if (!skipTokenValidation && !device) {
      return sendError(res, { code: -1, message: 'Invalid token' })
    }

    if (device) {
      logger.info({ message: `Context request -> ${ req.path }`, body: pick(device, 'user_id', 'user_type') })
    } else {
      logger.info({ message: `No context request -> ${ req.path }` })
    }

    return next()
  },
  decryption: function(data, callback) {
    try {
      const decryptedData = decrypt(data)
      callback(decryptedData)
    } catch (err) {
      logger.error(`Failed to decrept: ${err}`)
      callback("")
    }
  },

    encryption: function(data, callback) {
        try {
            const res = cryptoLib.encrypt(JSON.stringify(data), shakey, globals.iv);
            callback(res);
        } catch (err) {
          logger.error(`Failed encryption.`, err);
          callback('');
        }
    },

    check_validation: function(request, response, rules, message, keyword) {
        var v = Validate.make(request, rules, message, keyword);
        if (v.fails()) {
            const validate_error = v.getErrors() ?? {};
            logger.error('Failed validations.', validate_error);
            data.encryption(
              {
                code: '0',
                message: JSON.stringify(validate_error),
              },
              r => response.status(400).json(r),
            );

            return false;
        } else {
            return true;
        }
    },

    send_response: function(req, res, code, message, payload, status_code=200) {
        if (req?.headers["encryption"]?.toLowerCase() === "none")
            return res.status(status_code).json({ code, message, data: payload })

        data.encryption(
            { code, message, data: payload },
            r => {
            if (res.headersSent) {
                logger.error('Trying to send response that was already sent')
                return
            }

            res.status(status_code).json(r)
            },
        )
    },

    save_user_deviceinfo: function(user_id, updparam, callback) {
        //check user device information
        var query = con.query("SELECT * FROM tbl_user_deviceinfo WHERE user_id = '" + user_id + "' AND user_type = '" + updparam.user_type + "'", function(err, result) {
            if (!err && result[0] != undefined && result[0] != "") {
                var query = con.query("UPDATE tbl_user_deviceinfo SET ? WHERE id = '" + result[0].id + "'", updparam, function(err1, result, fields) {
                    if (!err1) {
                        callback(user_id, null);
                    } else {
                        callback(null, err);
                    }
                });
            } else {
                updparam.user_id = user_id;
                updparam.inserted_date = datetime.create().format('Y-m-d H:M:S');
                var query = con.query('INSERT tbl_user_deviceinfo SET ? ', updparam, function(err, result, fields) {
                    if (!err) {
                        callback(user_id);
                    } else {
                        callback(null, err);
                    }
                });
            }
        });
    },

    //save retailer info
    save_retailer_deviceinfo: function(retailer_id, updparam, callback) {
        //check user device information
        var query = con.query("SELECT * FROM tbl_user_deviceinfo WHERE retailer_id = '" + retailer_id + "'", function(err, result) {
            if (result[0] != undefined && result[0] != "") {
                var query = con.query("UPDATE tbl_user_deviceinfo SET ? WHERE retailer_id = '" + retailer_id + "'", updparam, function(err, result, fields) {
                    if (!err) {
                        callback(retailer_id, null);
                    } else {
                        callback(null, err);
                    }
                });
            } else {
                updparam.user_id = '0';
                updparam.retailer_id = retailer_id;
                updparam.inserted_date = datetime.create().format('Y-m-d H:M:S');
                var query = con.query('INSERT tbl_user_deviceinfo SET ? ', updparam, function(err, result, fields) {
                    if (!err) {
                        callback(retailer_id);
                    } else {
                        callback(null, err);
                    }
                });
            }
        });
    },


    sendSMS(phone, message, callback) {
        client.messages.create({
            body: message,
            from: globals.TWILIO_NUMBER,
            to: phone
        }).then(message => {
            callback(true);
        }).catch((error) => {
            callback(true, t('rest_keywords_something_went_wrong'), 0);
        });
    },

    Random_string: function(length, callback) {
        var text = "";
        var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

        for (var i = 0; i < length; i++)
            text += possible.charAt(Math.floor(Math.random() * possible.length));

        callback(text);
    },

    send_email: function(subject, to_email, message, callback) {
        var transporter = nodemailer.createTransport({
            host: globals.SMTPHOST,
            port: 587,
            secure: false,
            auth: {
                user: globals.SMTPUSER,
                pass: globals.SMTPPASS
            }
        }); // setup email data with unicode symbols
        var mailOptions = {
            from: globals.to_email, // sender address
            to: to_email, //to_email, // list of receivers
            subject: subject, // Subject line
            html: message
        };
        //send mail with defined transport object
        transporter.sendMail(mailOptions, (error, info) => {
            if (error) {
                console.log("send mail error",error);
                callback(false);
            } else {
                transporter.close();
                callback(true);
            }
        });
    },

    /**
     * insert notifications
     */
    insert_notification: function(data, response) {
        con.query("INSERT INTO tbl_notification SET ?", data, function(err, result) {
            if (!err) {
                response(true);
            } else {
                console.log(err)
                response(null);
            }
        })
    },

    /*
    * Function for get user notification counts
    */
    getusernotificationcounts:function(receiver_id,receiver_type,callback){
        con.query("SELECT count(id) as totalnotification FROM tbl_notification WHERE receiver_id = '"+receiver_id+"' AND receiver_type = '"+receiver_type+"' AND status = 'Unread' GROUP BY receiver_id",function(erorr,result){
            if(!erorr && result[0] != undefined){
                callback(result[0].totalnotification);
            } else {
                callback(0);
            }
        });
    },

    /*
    * Prepare notification for Service Provider
    */
  prepare_notification: (service_provider_id, push_params, callback) => {
    try {
      const query = `
          SELECT
              u.id,
              d.device_token,
              d.device_type
          FROM tbl_service_provider u
               JOIN tbl_user_deviceinfo d ON d.user_id = u.id
          WHERE u.is_deleted = ?
            AND u.id = ?
            AND d.user_type = ?
          LIMIT 1
      `
      con.query(query, ['0', service_provider_id, push_params.receiver_type], function (err, result) {
        if (err) {
          console.log(err);
          return callback(false);
        }

        if (result[0] == undefined || result[0].device_token === "" || result[0].device_token == undefined || result[0].device_token === 0) {
          console.log("Device token blank");
          return callback(false);
        }

        var insert_notifications = {
          isaction_id: (push_params.isaction_id != undefined && push_params.isaction_id != "") ? push_params.isaction_id : 0,
          sender_id: push_params.sender_id,
          sender_type: push_params.sender_type,
          receiver_id: service_provider_id,
          receiver_type: (push_params.type != undefined && push_params.type == 'booth_owner') ? 'booth_owner' : push_params.receiver_type,
          title: (push_params.title != undefined && push_params.title != "") ? push_params.title : (push_params.receiver_type == 'service_provider' ? 'BlookdBiz' : globals.APP_NAME),
          message: push_params.message,
          tag: push_params.tag,
        }
        data.insert_notification(insert_notifications, function (response) {
          if (response == null) {
            return callback(false)
          }

          data.getusernotificationcounts(service_provider_id, insert_notifications.receiver_type, function (notificationcount) {
            const isServiceProvider = get(push_params, 'receiver_type') === 'service_provider'
            if (get(push_params, 'title', '') === '')
              push_params.title = isServiceProvider ? 'BlookdBiz' : globals.APP_NAME
            push_params.body = push_params.message;
            var push_data = {
              topic: isServiceProvider ? globals.PROVIDER_BUNDLE_ID : globals.CUSTOMER_BUNDLE_ID,
              title: push_params.title,
              body: push_params.message,
              priority: 'high',
              badge: notificationcount,
              alert: {
                title: push_params.title,
                body: push_params.message,
              },
              custom: push_params
            };
            if (result[0].device_type == "I") {
              push_data['notification'] = {
                title: push_params.title,
                body: push_params.message,
              }
              push_data['sound'] = "default";
            }
            const registrationIds = [];
            registrationIds.push(result[0].device_token);
            return data.send_push(registrationIds, push_data, function (response1) {
              return callback(response1);
            });
          });
        });
      });
    } catch (e) {
      logger.erro('Failure during push notification', e)
      return callback(false)
    }
  },

    /*
    * Prepare notification for customer
    */
    prepare_customer_notification: function(user_id, push_params, callback) {
        var query = con.query(`SELECT u.id,d.device_token,d.device_type FROM tbl_user u JOIN tbl_user_deviceinfo  d ON d.user_id = u.id WHERE u.is_deleted = '0' AND u.id = ${user_id} AND d.user_type = '${push_params.type}' LIMIT 1`, function(err, result) {
            if (!err && result[0] != undefined && result[0].device_token != "" && result[0].device_token != undefined && result[0].device_token != 0) {
                var insert_notifications = {
                    isaction_id:(push_params.isaction_id != undefined && push_params.isaction_id != "") ? push_params.isaction_id : 0,
                    sender_id: push_params.sender_id,
                    sender_type: push_params.sender_type,
                    receiver_id: user_id,
                    receiver_type: push_params.receiver_type,
                    title:(push_params.title != undefined && push_params.title != "") ? push_params.title : (push_params.receiver_type == 'service_provider' ? 'BlookdBiz' : globals.APP_NAME),
                    message: push_params.message,
                    tag: push_params.tag,
                }
                data.insert_notification(insert_notifications, function(response) {
                    if (response != null) {
                        data.getusernotificationcounts(user_id,push_params.receiver_type,function(notificationcount){
                            push_params.title = (push_params.title != undefined && push_params.title != "") ? push_params.title : (push_params.receiver_type == 'service_provider' ? 'BlookdBiz' : globals.APP_NAME);
                            push_params.body = push_params.message;
                            var push_data = {
                                topic: globals.CUSTOMER_BUNDLE_ID,
                                title: push_params.title,
                                body: push_params.message,
                                priority: 'high',
                                badge:notificationcount,
                                alert: {
                                    title: push_params.title,
                                    body: push_params.message,
                                },
                                custom: push_params,
                            };
                            if(push_params.receiver_type == 'service_provider'){
                                push_data.topic = globals.PROVIDER_BUNDLE_ID;
                            }
                            if (result[0].device_type == "I") {
                                push_data['notification'] = {
                                    title: push_params.title,
                                    body: push_params.message,
                                }
                                push_data['sound'] = "default";
                            }
                            const registrationIds = [];
                            registrationIds.push(result[0].device_token);
                            data.send_push(registrationIds, push_data, function(response1) {
                                callback(response1);
                            });
                        });
                    } else {
                        callback(true);
                    }
                });
            } else {
                console.log("Device token blank");
                callback(true);
            }
        });
    },

    /*
    * Prepare notification for video call
    */
    prepare_chat_notification: function(user_id, push_params, callback) {
        var query = con.query(`SELECT u.id,d.device_token,d.device_type FROM  ${push_params.table_name} u JOIN tbl_user_deviceinfo  d ON d.user_id = u.id WHERE u.is_deleted = '0' AND u.id = ${user_id} AND d.user_type = '${push_params.type}' LIMIT 1`, function(err, result) {
            if (!err && result[0] != undefined && result[0].device_token != "" && result[0].device_token != undefined && result[0].device_token != 0) {
                var insert_notifications = {
                    isaction_id:(push_params.isaction_id != undefined && push_params.isaction_id != "") ? push_params.isaction_id : 0,
                    sender_id: push_params.sender_id,
                    sender_type: push_params.sender_type,
                    receiver_id: user_id,
                    receiver_type: push_params.receiver_type,
                    title:(push_params.title != undefined && push_params.title != "") ? push_params.title : (push_params.receiver_type == 'service_provider' ? 'BlookdBiz' : globals.APP_NAME),
                    message: push_params.message,
                    tag: push_params.tag,
                }
                data.insert_notification(insert_notifications, function(response) {
                    if (response != null) {
                        data.getusernotificationcounts(user_id,push_params.receiver_type,function(notificationcount){
                            push_params.title = (push_params.title != undefined && push_params.title != "") ? push_params.title : (push_params.receiver_type == 'service_provider' ? 'BlookdBiz' : globals.APP_NAME);
                            push_params.body = push_params.message;
                            var push_data = {
                                topic: globals.CUSTOMER_BUNDLE_ID,
                                title: push_params.title,
                                body: push_params.message,
                                badge:notificationcount,
                                alert: {
                                    title: push_params.title,
                                    body: push_params.message,
                                },
                                custom: push_params,
                            };
                            if(push_params.receiver_type == 'service_provider'){
                                push_data.topic = globals.PROVIDER_BUNDLE_ID;
                            }
                            if (result[0].device_type == "I") {
                                push_data['notification'] = {
                                    title: push_params.title,
                                    body: push_params.message,
                                }
                                push_data['sound'] = "default";
                            }
                            push_data['tag'] = push_params.tag;
                            const registrationIds = [];
                            registrationIds.push(result[0].device_token);
                            data.send_push(registrationIds, push_data, function(response1) {
                                callback(response1);
                            });
                        });
                    } else {
                        callback(true);
                    }
                });
            } else {
                console.log("Device token blank");
                callback(true);
            }
        });
    },

    //send push
  send_push: async (registrationIds, data, callback) => {
    try {
      const settings = {
        gcm: {
          id: globals.PUSHID,
        },
        apn: {
          token: {
            key: globals.APN_PUSH_KEY,
            keyId: globals.KEYID,
            teamId: globals.TEAMID,
          },
          production: true
        },
        isAlwaysUseFCM: false
      };
      const push = new PushNotifications(settings);
      await push.send(
        registrationIds,
        data,
        (err, result) => {
          if (err) {
            console.log(err);
            console.log('Send Push fails', result);
            callback(false);
          } else {
            callback(true);
          }
        });
    } catch (e) {
      callback(false)
    }
  },
    /**
     *
     * @param {card_number} card_number
     * @param {callback} callback
     * @description This methos use for fetch card type
     */
    getCardType(card_number, callback) {
        var card_type = "";
        // Visa
        var re = new RegExp("^4");
        if (card_number.match(re) != null) { card_type = "Visa"; }

        // Mastercard
        // Updated for Mastercard 2017 BINs expansion
        if (/^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$/.test(card_number)) {
            card_type = "Mastercard";
        }
        // AMEX
        re = new RegExp("^3[47]");
        if (card_number.match(re) != null) { card_type = "AMEX"; }

        // Discover
        re = new RegExp("^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)");
        if (card_number.match(re) != null) { card_type = "Discover"; }

        // Diners
        re = new RegExp("^36");
        if (card_number.match(re) != null) { card_type = "Diners"; }

        // Diners - Carte Blanche
        re = new RegExp("^30[0-5]");
        if (card_number.match(re) != null) { card_type = "Diners - Carte Blanche"; }

        // JCB
        re = new RegExp("^35(2[89]|[3-8][0-9])");
        if (card_number.match(re) != null) { card_type = "JCB"; }

        // Visa Electron
        re = new RegExp("^(4026|417500|4508|4844|491(3|7))");
        if (card_number.match(re) != null) { card_type = "Visa Electron"; }

        if (card_type != "") {
            callback(card_type);
        } else {
            callback("other");
        }
    },

    /**
     * Common insert function
     */
    single_insert_data: function(table_name, data, callback) {
        con.query("INSERT INTO " + table_name + " SET?", data, function(error, result, fields) {
            if (error) {
                console.log('single_insert_data err', error);
                return callback(false)
            } else {
                return callback(result)
            }
        });
    },
    /**
     * Common update function
     */
    update_data: function(table_name, condition, data, callback) {
        con.query("UPDATE " + table_name + " SET ? where id = " + condition + "", data, function(error, result, fields) {
            if (error) {
                console.log('update_data err', error);
                if (callback)
                  callback(false);
            } else {
                if (callback)
                  callback(true);
            }
        });
    },

    /**
     *
     * @param {table_name} table_name
     * @param {*} condition
     * @param {*} response
     */
    update_data_condition: function(table_name, condition, data, callback) {
        con.query("UPDATE " + table_name + " SET ? where " + condition + "", data, function(error, result, fields) {
            if (error) {
                console.log('update_data_condition err', error);
                callback(false);
            } else {
                callback(true);
            }
        });
    },

    /**
     * @param {*} request
     * @param {*} response
     * @description This function is used for select data
     */
    select_data: function(table_name, condition, response) {
        if (condition) {
            var condi = condition;
        } else {
            var condi = 'is_deleted = "0"';
        }
        var sql = con.query("select * from " + table_name + " where " + condi + "", function(err, result) {
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    response(result);
                } else {
                    response(false)
                }
            } else {
                console.log('select_data error' + err);
                response(false);
            }
        })
    },

    /**
     *
     * @param {*} request
     * @param {*} response
     * @description This function is used for delete data
     */
    delete_data: function(table_name, condition, responce) {
        var sql = con.query("DELETE from " + table_name + " where " + condition + "", function(err, result) {
            if (!err) {
                responce(true);
            } else {
                response(false);
            }
        })
    },

    /**
     * Get notificaion list
     */
    get_notification: function(request, response) {
        const page = 0;
        const limit = globals.PER_PAGE;
        const offset = page * globals.PER_PAGE;
        const query = `
            SELECT *,
                id AS notification_id, 
                IF(title = '', tag, title) AS title, 
                DATE_FORMAT(insertdate, '%Y-%m-%d %H:%i:%s') AS insertdate 
            FROM tbl_notification 
            WHERE receiver_type = '${request.receiver_type}' 
                AND receiver_id = ${request.user_id} 
            ORDER BY id DESC 
            LIMIT ${limit} 
            OFFSET ${offset}
        `
        var sql = con.query(query, function(err, result) {
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    data.getusernotificationcounts(request.user_id,request.receiver_type,function(notificationcount){
                        response({ total_notification: notificationcount, notification_list: result }, t('restapi_notificaion_found'), '1');
                    });
                } else {
                    response(null, t('restapi_notificaion_notfound'), '2');

                }
            } else {
                response(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * @param {request}  main category id
     * @param {response} response
     * @description  This function is used for get category
     */
    category_list: function(id, response) {
        var sql = con.query("select *,id as category_id from tbl_category where master_category_id = " + id + " AND is_deleted = '0' AND status = 'Active'", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    response(result, t('restapi_productcateoryfound'), '1');
                } else {
                    response(null, t('restapi_cateorynotfound'), '0');
                }
            } else {
                response(null, t('restapi_globals_error'), '0');
            }
        });
    },
    /**
     * @param {request}   sub category id
     * @param {response} response
     * @description  This function is used for get sub category
     */
    subcategory_list: function(id, response) {
        var sql = con.query("select *,id as sub_category_id from tbl_subcategory where category_id = " + id + " AND is_deleted = '0' AND status = 'Active'", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    response(result, t('restapi_productcateoryfound'), '1');
                } else {
                    response(null, t('restapi_cateorynotfound'), '0');
                }
            } else {
                response(null, t('restapi_globals_error'), '0');
            }
        });
    },

    /**
     * Get unit list
     */
    get_attribute: function(id, response) {
        var sql = con.query("select id,CONCAT(value,unit) as unit,status,is_deleted from tbl_product_attributes where is_deleted = '0' AND status = 'Active'", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    response(result, t('restapi_unit_found'), '1');
                } else {
                    response(null, t('restapi_uniynotfound'), '0');
                }
            } else {
                response(null, t('restapi_globals_error'), '0');
            }
        });
    },

    /**
     * Function for generate cashfree cftoken
     */
    generate_cashfree_token: function(order, mainresponse) {
        var request = require('request');
        var options = {
            'method': 'POST',
            'url': 'https://test.cashfree.com/api/v2/cftoken/order',
            'headers': {
                'Content-Type': 'application/json',
                'x-client-id': globals.APP_ID,
                'x-client-secret': globals.SECRET_KEY
            },
            body: JSON.stringify({
                "orderId": order.order_id,
                "orderAmount": order.amount,
                "orderCurrency": "INR"
            })
        };

        request(options, function(error, response) {
            if (error) throw new Error(error);
            var cftoken = JSON.parse(response.body);
            //console.log"body", JSON.parse(response.body));
            mainresponse(cftoken);
        });
    },

    /**
     * Generate unique id
     */
    generate_unique_code(table_name, callback) {
        // var code = randomstring.generate({ length: 15, charset: 'numeric' });
        var code = Math.floor(100000 + Math.random() * 900000)
        con.query("SELECT unique_id FROM " + table_name + " WHERE unique_id = '" + code + "'", function(err, result, fields) {
            if (!err) {
                if (result.length <= 0) {
                    callback(code);
                } else {
                    data.generate_unique_code(table_name, function(response) {});
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        });
    },

    /**
     *
     * @param {*} table_name
     * @param {*} response
     * @description This function is used for generate order id
     */

    generate_order_id(table_name, callback) {
        // var code = randomstring.generate({ length: 15, charset: 'numeric' });
        var code = "ORDER" + Math.floor(10000000000 + Math.random() * 90000000000);
        con.query("SELECT order_id FROM " + table_name + " WHERE order_id = '" + code + "'", function(err, result, fields) {
            if (!err) {
                if (result.length <= 0) {
                    callback(code);
                } else {
                    data.generate_order_id(table_name, function(response) {});
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        });
    },


    /**
     * Count total records in to the table
     */
    count_records: function(table_name, countid, condition, response) {
        if (condition) {
            var condi = condition;
        } else {
            var condi = 'is_deleted = "0"';
        }
        var sql = con.query("select " + countid + " from " + table_name + " where " + condi + "", function(err, result) {
            //console.log"query", "select count(id) as total_record from " + table_name + " where " + condi + "");
            //console.logresult);
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    // //console.log'result', result[0].total_record);
                    // process.exit();
                    response(result[0].total_record);
                } else {
                    //console.log'no data for count');
                    response(false)
                }
            } else {
                //console.log'error' + err);
                response(false);
            }
        })
    },

    // DEPR, probably
    check_availability: function(req, callback) {
        var now_time = '';
        // 9
        // //console.logtoday.getHours());
        if (today.getHours() <= '9' || today.getHours() <= 9) {
            now_time += 0 + today.getHours() + ':' + '00';
        } else {
            now_time += today.getHours() + ':' + '00';
        }
        // //console.lognow_time);
        var sql = con.query("SELECT * FROM tbl_time_slot WHERE from_time > '" + now_time + "'", function(err, result) {
            if (!err) {
                if (result.length > 0) {
                    // //console.logresult);
                    asyncLoop(result, function(item, next) {
                        con.query("SELECT ab.*,DATE_FORMAT(booking_date,'%Y-%m-%d') as appointment_date FROM tbl_appointment_booking ab WHERE ab.from_time='" + item.from_time + "' AND ab.to_time ='" + item.to_time + "' having appointment_date = '" + req.date + "'", function(err1, result1) {
                            if (!err1 && result1.length > 0) {
                                item.is_booked = 1;
                                next();
                            } else {
                                item.is_booked = 0;
                                next();
                            }
                        })
                    }, function(err2) {
                        if (result.length > 0) {
                            callback(result, lang['api_data_found'], 1);
                        } else {
                            callback(null, lang['api_no_data'], 2);
                        }
                    })
                } else {
                    callback(null, lang['api_no_data'], 2);
                }
            } else {
                //console.logerr)
                callback(null, lang['text_rest_mysql_query_error'], 0);
            }
        })
    },

    selct_data_with_field: function(table_name, condition, field, response) {
        if (condition) {
            var condi = condition;
        } else {
            var condi = 'is_deleted = "0"';
        }
        var sql = con.query("select " + field + " from " + table_name + " where " + condi + "", function(err, result) {
            //console.log"query", "select " + field + " from " + table_name + " where " + condi + "");
            //console.logresult);
            //console.logerr);
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    //console.log'result', result);
                    response(result);
                } else {
                    //console.log'no data');
                    response(false)
                }
            } else {
                //console.log'error' + err);
                response(false);
            }
        })
    },

    /**
     * Function for check email phone
     */
    check_email_phone: function(unique_key, table_name, callback) {
        if (unique_key.email != undefined && unique_key.email != "") {
            var len = Object.keys(unique_key).length;
            var i = 1;
            asyncloop(unique_key, function(item, next) {
                var query = con.query("SELECT * FROM " + table_name + " WHERE is_deleted = '0' AND " + item.key + " = ?", item.value, function(err, result, fields) {
                    if (!err) {
                        if (result == '') {
                            if (len == i) {
                                callback(true);
                                return;
                            }
                        } else {
                            var message = "";
                            if (item.key == 'email') {
                                message = t('restapi_email_alreadyexists_error');
                            }
                            if (item.key == 'phone') {
                                message = t('restapi_phone_alreadyexists_error');
                            }
                            // message = ((item.key == 'email') ? t('restapi_email_alreadyexists_error') : t('restapi_phone_alreadyexists_error'))
                            callback(false, message);
                            return;
                        }
                    } else {
                        callback(false + err);
                        return;
                    }
                    i++;
                    next();
                });
            }, function() {
                callback();
            });
        } else {
            callback(true);
        }
    },

    checkuser_unique: function(user_id, unique_key, table_name, callback) {
        var len = Object.keys(unique_key).length;
        var i = 1;
        asyncloop(unique_key, function(item, next) {
            var query = con.query("SELECT * FROM " + table_name + " WHERE id!='" + user_id + "' AND is_deleted='0' AND " + item.key + " = ?", item.value, function(err, result, fields) {
                if (!err) {
                    if (result == '') {
                        if (len == i) {
                            callback(true);
                            return;
                        }
                    } else {
                        message = ((item.key == 'email') ? t('restapi_email_alreadyexists_error') : t('restapi_phone_alreadyexists_error'))
                        callback(false, message);
                        return;
                    }
                } else {
                    callback(false, t('restapi_globals_error'));
                    return;
                }
                i++;
                next();
            });
        }, function() {
            callback();
        });
    },

    checkclientedit_unique:function(request,callback){
        con.query("SELECT * FROM tbl_service_provider_client WHERE id != '" + request.client_id + "' AND service_provider_id = '"+request.service_provider_id+"' AND is_deleted = '0' AND (country_code = '"+request.country_code+"' AND phone = '"+request.phone+"') AND email = '"+request.email+"'", function(err, result, fields) {
            if (!err && result[0] != undefined) {
                callback(false, 'restapi_client_alreadyexists_error');
            } else {
                callback(true);
            }
        });
    },

    //function to debit wallet amount from user/driver
    debit_wallet:function(tabel_name,where,field_name,amount,callback){
        var query = con.query("UPDATE `"+tabel_name+"` SET `"+field_name+"`=`"+field_name+"` - "+amount+" WHERE "+where,function(err,result,fields){
            con.query("SELECT "+field_name+" as balance FROM "+tabel_name+" WHERE "+where,function(err1,result1){
                if(!err1 && result1[0] != undefined){
                    callback(result1[0].balance);
                } else {
                    callback(0.00);
                }
            });
        });
    },

    //function to credit wallet amount from user/driver
    credit_wallet:function(tabel_name,where,field_name,amount,callback){
        var query = con.query("UPDATE `"+tabel_name+"` SET `"+field_name+"`=`"+field_name+"` + "+amount+" WHERE "+where,function(err,result,fields){
            con.query("SELECT "+field_name+" as balance FROM "+tabel_name+" WHERE "+where,function(err1,result1){
                if(!err1 && result1[0] != undefined){
                    callback(result1[0].balance);
                } else {
                    callback(0.00);
                }
            });
        });
    },
  get_app_setting: function (callback) {
    con.query("SELECT * FROM tbl_settings", function (err, result, fields) {
      if (err || result[0] == undefined) {
        callback(null);
      } else {
        var appsetting = {};
        var row = 0;
        asyncLoop(result, function (item, next) {
          appsetting[item.title] = item.value;
          row++;
          next();
        }, function () {
          callback(appsetting);
        });//end function
      }
    });
  },
}
module.exports = data;
