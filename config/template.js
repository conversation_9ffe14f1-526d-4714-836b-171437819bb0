const Global = require('./constant')
var template = {
  forgot_password: function (result, callback) {
    const template = `<!doctype html>
    <html>
      <head>
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>Forgot Password</title>
          <style type="text/css">
          p{
            margin:10px 0;
            padding:0;
          }
          table{
            border-collapse:collapse;
          }
          h1,h2,h3,h4,h5,h6{
            display:block;
            margin:0;
            padding:0;
          }
          img,a img{
            border:0;
            height:auto;
            outline:none;
            text-decoration:none;
          }
          body,#bodyTable,#bodyCell{
            height:100%;
            margin:0;
            padding:0;
            width:100%;
            background-color:#F2F2F2;
          }
          .mcnPreviewText{
            display:none !important;
          }
          #outlook a{
            padding:0;
          }
          img{
            -ms-interpolation-mode:bicubic;
          }
          table{
            mso-table-lspace:0pt;
            mso-table-rspace:0pt;
          }
          .ReadMsgBody{
            width:100%;
          }
          .ExternalClass{
            width:100%;
          }
          p,a,li,td,blockquote{
            mso-line-height-rule:exactly;
          }
          a[href^=tel],a[href^=sms]{
            color:inherit;
            cursor:default;
            text-decoration:none;
          }
          p,a,li,td,body,table,blockquote{
            -ms-text-size-adjust:100%;
            -webkit-text-size-adjust:100%;
          }
          .ExternalClass,.ExternalClass p,.ExternalClass td,.ExternalClass div,.ExternalClass span,.ExternalClass font{
            line-height:100%;
          }
          a[x-apple-data-detectors]{
            color:inherit !important;
            text-decoration:none !important;
            font-size:inherit !important;
            font-family:inherit !important;
            font-weight:inherit !important;
            line-height:inherit !important;
          }
          .templateContainer{
            max-width:600px !important;
          }
          a.mcnButton{
            display:block;
          }
          .mcnImage,.mcnRetinaImage{
            vertical-align:bottom;
          }
          .mcnTextContent{
            word-break:break-word;
          }
          .mcnTextContent img{
            height:auto !important;
          }
          .mcnDividerBlock{
            table-layout:fixed !important;
          }
          /*
          @tab Page
          @section Heading 1
          @style heading 1
          */
            h1{
              /*@editable*/color:#222222;
              /*@editable*/font-family:Helvetica;
              /*@editable*/font-size:40px;
              /*@editable*/font-style:normal;
              /*@editable*/font-weight:bold;
              /*@editable*/line-height:150%;
              /*@editable*/letter-spacing:normal;
              /*@editable*/text-align:center;
            }
          /*
          @tab Page
          @section Heading 2
          @style heading 2
          */
            h2{
              /*@editable*/color:#222222;
              /*@editable*/font-family:Helvetica;
              /*@editable*/font-size:34px;
              /*@editable*/font-style:normal;
              /*@editable*/font-weight:bold;
              /*@editable*/line-height:150%;
              /*@editable*/letter-spacing:normal;
              /*@editable*/text-align:left;
            }
          /*
          @tab Page
          @section Heading 3
          @style heading 3
          */
            h3{
              /*@editable*/color:#444444;
              /*@editable*/font-family:Helvetica;
              /*@editable*/font-size:22px;
              /*@editable*/font-style:normal;
              /*@editable*/font-weight:bold;
              /*@editable*/line-height:150%;
              /*@editable*/letter-spacing:normal;
              /*@editable*/text-align:left;
            }
          /*
          @tab Page
          @section Heading 4
          @style heading 4
          */
            h4{
              /*@editable*/color:#999999;
              /*@editable*/font-family:Georgia;
              /*@editable*/font-size:20px;
              /*@editable*/font-style:italic;
              /*@editable*/font-weight:normal;
              /*@editable*/line-height:125%;
              /*@editable*/letter-spacing:normal;
              /*@editable*/text-align:left;
            }
          /*
          @tab Header
          @section Header Container Style
          */
            #templateHeader{
              /*@editable*/background-color:#F2F2F2;
              /*@editable*/background-image:none;
              /*@editable*/background-repeat:no-repeat;
              /*@editable*/background-position:center;
              /*@editable*/background-size:cover;
              /*@editable*/border-top:0;
              /*@editable*/border-bottom:0;
              /*@editable*/padding-top:36px;
              /*@editable*/padding-bottom:0;
            }
          /*
          @tab Header
          @section Header Interior Style
          */
            .headerContainer{
              /*@editable*/background-color:#FFFFFF;
              /*@editable*/background-image:none;
              /*@editable*/background-repeat:no-repeat;
              /*@editable*/background-position:center;
              /*@editable*/background-size:cover;
              /*@editable*/border-top:0;
              /*@editable*/border-bottom:0;
              /*@editable*/padding-top:45px;
            }
          /*
          @tab Header
          @section Header Text
          */
            .headerContainer .mcnTextContent,.headerContainer .mcnTextContent p{
              /*@editable*/color:#808080;
              /*@editable*/font-family:Helvetica;
              /*@editable*/font-size:16px;
              /*@editable*/line-height:150%;
              /*@editable*/text-align:left;
            }
          /*
          @tab Header
          @section Header Link
          */
            .headerContainer .mcnTextContent a,.headerContainer .mcnTextContent p a{
              /*@editable*/color:#007E9E;
              /*@editable*/font-weight:normal;
              /*@editable*/text-decoration:underline;
            }
          /*
          @tab Body
          @section Body Container Style
          */
            #templateBody{
              /*@editable*/background-color:#F2F2F2;
              /*@editable*/background-image:none;
              /*@editable*/background-repeat:no-repeat;
              /*@editable*/background-position:center;
              /*@editable*/background-size:cover;
              /*@editable*/border-top:0;
              /*@editable*/border-bottom:0;
              /*@editable*/padding-top:0;
              /*@editable*/padding-bottom:0;
            }
          /*
          @tab Body
          @section Body Interior Style
          */
            .bodyContainer{
              /*@editable*/background-color:#FFFFFF;
              /*@editable*/background-image:none;
              /*@editable*/background-repeat:no-repeat;
              /*@editable*/background-position:center;
              /*@editable*/background-size:cover;
              /*@editable*/border-top:0;
              /*@editable*/border-bottom:0;
              /*@editable*/padding-top:0;
              /*@editable*/padding-bottom:20px;
            }
          /*
          @tab Body
          @section Body Text
          */
            .bodyContainer .mcnTextContent,.bodyContainer .mcnTextContent p{
              /*@editable*/color:#808080;
              /*@editable*/font-family:Helvetica;
              /*@editable*/font-size:16px;
              /*@editable*/line-height:150%;
              /*@editable*/text-align:left;
            }
          /*
          @tab Body
          @section Body Link
          */
            .bodyContainer .mcnTextContent a,.bodyContainer .mcnTextContent p a{
              /*@editable*/color:#007E9E;
              /*@editable*/font-weight:normal;
              /*@editable*/text-decoration:underline;
            }
          /*
          @tab Footer
          @section Footer Style
          */
            #templateFooter{
              /*@editable*/background-color:#F2F2F2;
              /*@editable*/background-image:none;
              /*@editable*/background-repeat:no-repeat;
              /*@editable*/background-position:center;
              /*@editable*/background-size:cover;
              /*@editable*/border-top:0;
              /*@editable*/border-bottom:0;
              /*@editable*/padding-top:0;
              /*@editable*/padding-bottom:36px;
            }
          /*
          @tab Footer
          @section Footer Interior Style
          */
            .footerContainer{
              /*@editable*/background-color:#333333;
              /*@editable*/background-image:none;
              /*@editable*/background-repeat:no-repeat;
              /*@editable*/background-position:center;
              /*@editable*/background-size:cover;
              /*@editable*/border-top:0;
              /*@editable*/border-bottom:0;
              /*@editable*/padding-top:45px;
              /*@editable*/padding-bottom:45px;
            }
          /*
          @tab Footer
          @section Footer Text
          */
            .footerContainer .mcnTextContent,.footerContainer .mcnTextContent p{
              /*@editable*/color:#FFFFFF;
              /*@editable*/font-family:Helvetica;
              /*@editable*/font-size:12px;
              /*@editable*/line-height:150%;
              /*@editable*/text-align:center;
            }
          /*
          @tab Footer
          @section Footer Link
          */
            .footerContainer .mcnTextContent a,.footerContainer .mcnTextContent p a{
              /*@editable*/color:#FFFFFF;
              /*@editable*/font-weight:normal;
              /*@editable*/text-decoration:underline;
            }
          @media only screen and (min-width:768px){
              .templateContainer{
                width:600px !important;
              }

          } @media only screen and (max-width: 480px){
              body,table,td,p,a,li,blockquote{
                -webkit-text-size-adjust:none !important;
              }

          } @media only screen and (max-width: 480px){
              body{
                width:100% !important;
                min-width:100% !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnRetinaImage{
                max-width:100% !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnImage{
                width:100% !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnCartContainer,.mcnCaptionTopContent,.mcnRecContentContainer,.mcnCaptionBottomContent,.mcnTextContentContainer,.mcnBoxedTextContentContainer,.mcnImageGroupContentContainer,.mcnCaptionLeftTextContentContainer,.mcnCaptionRightTextContentContainer,.mcnCaptionLeftImageContentContainer,.mcnCaptionRightImageContentContainer,.mcnImageCardLeftTextContentContainer,.mcnImageCardRightTextContentContainer,.mcnImageCardLeftImageContentContainer,.mcnImageCardRightImageContentContainer{
                max-width:100% !important;
                width:100% !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnBoxedTextContentContainer{
                min-width:100% !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnImageGroupContent{
                padding:9px !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnCaptionLeftContentOuter .mcnTextContent,.mcnCaptionRightContentOuter .mcnTextContent{
                padding-top:9px !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnImageCardTopImageContent,.mcnCaptionBottomContent:last-child .mcnCaptionBottomImageContent,.mcnCaptionBlockInner .mcnCaptionTopContent:last-child .mcnTextContent{
                padding-top:18px !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnImageCardBottomImageContent{
                padding-bottom:9px !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnImageGroupBlockInner{
                padding-top:0 !important;
                padding-bottom:0 !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnImageGroupBlockOuter{
                padding-top:9px !important;
                padding-bottom:9px !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnTextContent,.mcnBoxedTextContentColumn{
                padding-right:18px !important;
                padding-left:18px !important;
              }

          } @media only screen and (max-width: 480px){
              .mcnImageCardLeftImageContent,.mcnImageCardRightImageContent{
                padding-right:18px !important;
                padding-bottom:0 !important;
                padding-left:18px !important;
              }

          } @media only screen and (max-width: 480px){
              .mcpreview-image-uploader{
                display:none !important;
                width:100% !important;
              }

          } @media only screen and (max-width: 480px){
            /*
            @tab Mobile Styles
            @section Heading 1
            @tip Make the first-level headings larger in size for better readability on small screens.
            */
              h1{
                /*@editable*/font-size:30px !important;
                /*@editable*/line-height:125% !important;
              }

          } @media only screen and (max-width: 480px){
            /*
            @tab Mobile Styles
            @section Heading 2
            @tip Make the second-level headings larger in size for better readability on small screens.
            */
              h2{
                /*@editable*/font-size:26px !important;
                /*@editable*/line-height:125% !important;
              }

          } @media only screen and (max-width: 480px){
            /*
            @tab Mobile Styles
            @section Heading 3
            @tip Make the third-level headings larger in size for better readability on small screens.
            */
              h3{
                /*@editable*/font-size:20px !important;
                /*@editable*/line-height:150% !important;
              }

          } @media only screen and (max-width: 480px){
            /*
            @tab Mobile Styles
            @section Heading 4
            @tip Make the fourth-level headings larger in size for better readability on small screens.
            */
              h4{
                /*@editable*/font-size:18px !important;
                /*@editable*/line-height:150% !important;
              }

          } @media only screen and (max-width: 480px){
            /*
            @tab Mobile Styles
            @section Boxed Text
            @tip Make the boxed text larger in size for better readability on small screens. We recommend a font size of at least 16px.
            */
              .mcnBoxedTextContentContainer .mcnTextContent,.mcnBoxedTextContentContainer .mcnTextContent p{
                /*@editable*/font-size:14px !important;
                /*@editable*/line-height:150% !important;
              }

          } @media only screen and (max-width: 480px){
            /*
            @tab Mobile Styles
            @section Header Text
            @tip Make the header text larger in size for better readability on small screens.
            */
              .headerContainer .mcnTextContent,.headerContainer .mcnTextContent p{
                /*@editable*/font-size:16px !important;
                /*@editable*/line-height:150% !important;
              }

          } @media only screen and (max-width: 480px){
            /*
            @tab Mobile Styles
            @section Body Text
            @tip Make the body text larger in size for better readability on small screens. We recommend a font size of at least 16px.
            */
              .bodyContainer .mcnTextContent,.bodyContainer .mcnTextContent p{
                /*@editable*/font-size:16px !important;
                /*@editable*/line-height:150% !important;
              }

          } @media only screen and (max-width: 480px){
            /*
            @tab Mobile Styles
            @section Footer Text
            @tip Make the footer content text larger in size for better readability on small screens.
            */
              .footerContainer .mcnTextContent,.footerContainer .mcnTextContent p{
                /*@editable*/font-size:14px !important;
                /*@editable*/line-height:150% !important;
              }

          }
        </style>
      </head>
        <body>
          <center>
            <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable">
              <tbody>
                <tr>
                  <td align="center" valign="top" id="bodyCell">
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <tr>
                        <td align="center" valign="top" id="templateHeader">
                          <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer">
                            <tr>
                              <td valign="top" class="headerContainer">
                                <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnImageBlock" style="min-width:100%;">
                                  <tbody class="mcnImageBlockOuter">
                                    <tr>
                                      <td valign="top" style="padding:9px" class="mcnImageBlockInner">
                                        <table align="left" width="100%" border="0" cellpadding="0" cellspacing="0" class="mcnImageContentContainer" style="min-width:100%;">
                                          <tbody>
                                            <tr>
                                              <td class="mcnImageContent" valign="top" style="padding-right: 9px; padding-left: 9px; padding-top: 0; padding-bottom: 0; text-align:center;">
                                                <img align="center" alt="" src="${Global.LOGO}" style="padding-bottom: 0; display: inline !important; vertical-align: bottom;" class="mcnImage">
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr>
                <td align="center" valign="top" id="templateBody">
                  <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer">
                    <tr>
                      <td valign="top" class="bodyContainer">
                        <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width:100%;">
                          <tbody class="mcnTextBlockOuter">
                            <tr>
                              <td valign="top" class="mcnTextBlockInner" style="padding-top:9px;">
                                <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width:100%; min-width:100%;" width="100%" class="mcnTextContentContainer">
                                    <tbody>
                                      <tr>
                                        <td valign="top" class="mcnTextContent" style="padding-top:0; padding-right:18px; padding-bottom:9px; padding-left:18px;">
                                          <h3>Hello ${result.first_name} ${result.last_name},</h3>
                                          <p style="text-align: justify;">
                                            Forgot your password? Don't worry! Below is your reset password link. Please click on it and get the password reset. Do not share any confidential details with anyone.
                                          </p>
                                        </td>
                                      </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                        <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnButtonBlock" style="min-width:100%;">
                          <tbody class="mcnButtonBlockOuter">
                            <tr>
                              <td style="padding-top:0; padding-right:18px; padding-bottom:18px; padding-left:18px;" valign="top" align="center" class="mcnButtonBlockInner">
                                <table border="0" cellpadding="0" cellspacing="0" class="mcnButtonContentContainer" style="border-collapse: separate !important;border: 1px none;border-radius: 3px;background-color: #1a51ad;">
                                  <tbody>
                                    <tr>
                                      <td align="center" valign="middle" class="mcnButtonContent" style="font-family: Helvetica; font-size: 18px; padding: 18px;">
                                        <a class="mcnButton " title="Reset Password" href="${result.url}" target="_self" style="font-weight: bold;letter-spacing: -0.5px;line-height: 100%;text-align: center;text-decoration: none;color: #ffcf52;">Reset Password Now</a>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                        <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width:100%;">
                          <tbody class="mcnTextBlockOuter">
                            <tr>
                              <td valign="top" class="mcnTextBlockInner" style="padding-top:9px;">
                                <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width:100%; min-width:100%;" width="100%" class="mcnTextContentContainer">
                                  <tbody>
                                    <tr>
                                      <td valign="top" class="mcnTextContent" style="padding-top:0; padding-right:18px; padding-bottom:9px; padding-left:18px;">
                                        <p style="text-align: justify;">
                                          Or Copy and paste the link  below on your browser.
                                          <br/>
                                          <a href="${result.url}">${result.url}</a>
                                        </p>
                                        <p style="text-align: justify;">
                                          If you didn't request a password reset, please contact the administrator.
                                        </p>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                        <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width:100%;">
                          <tbody class="mcnTextBlockOuter">
                            <tr>
                              <td valign="top" class="mcnTextBlockInner" style="padding-top:9px;">
                                <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width:100%; min-width:100%;" width="100%" class="mcnTextContentContainer">
                                  <tbody>
                                    <tr>
                                      <td valign="top" class="mcnTextContent" style="padding-top:0; padding-right:18px; padding-bottom:9px; padding-left:18px;">
                                        Thank you,<br/>
                                        <b>${Global.APP_NAME} Team</b>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
        </center>
      </body>
    </html>
    `
    callback(template)
  },
  contact_us: function (result, callback) {
    const template = `<!doctype html>
      <html xmlns:fb="http://www.facebook.com/2008/fbml" xmlns:og="http://opengraph.org/schema/"> <head>

      <meta property="og:title" content="">
      <meta property="fb:page_id" content="43929265776">
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
      <meta name="referrer" content="origin">
              <!-- NAME: ANNOUNCE -->
              <!--[if gte mso 15]>
              <xml>
                  <o:OfficeDocumentSettings>
                  <o:AllowPNG/>
                  <o:PixelsPerInch>96</o:PixelsPerInch>
                  </o:OfficeDocumentSettings>
              </xml>
              <![endif]-->
              <meta charset="UTF-8">
              <meta http-equiv="X-UA-Compatible" content="IE=edge">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <title>Contact to ${Global.APP_NAME} App</title>

          <style type="text/css">
              p{
                  margin:10px 0;
                  padding:0;
              }
              table{
                  border-collapse:collapse;
              }
              h1,h2,h3,h4,h5,h6{
                  display:block;
                  margin:0;
                  padding:0;
              }
              img,a img{
                  border:0;
                  height:auto;
                  outline:none;
                  text-decoration:none;
              }
              body,#bodyTable,#bodyCell{
                  height:100%;
                  margin:0;
                  padding:0;
                  width:100%;
              }
              .mcnPreviewText{
                  display:none !important;
              }
              #outlook a{
                  padding:0;
              }
              img{
                  -ms-interpolation-mode:bicubic;
              }
              table{
                  mso-table-lspace:0pt;
                  mso-table-rspace:0pt;
              }
              .ReadMsgBody{
                  width:100%;
              }
              .ExternalClass{
                  width:100%;
              }
              p,a,li,td,blockquote{
                  mso-line-height-rule:exactly;
              }
              a[href^=tel],a[href^=sms]{
                  color:inherit;
                  cursor:default;
                  text-decoration:none;
              }
              p,a,li,td,body,table,blockquote{
                  -ms-text-size-adjust:100%;
                  -webkit-text-size-adjust:100%;
              }
              .ExternalClass,.ExternalClass p,.ExternalClass td,.ExternalClass div,.ExternalClass span,.ExternalClass font{
                  line-height:100%;
              }
              a[x-apple-data-detectors]{
                  color:inherit !important;
                  text-decoration:none !important;
                  font-size:inherit !important;
                  font-family:inherit !important;
                  font-weight:inherit !important;
                  line-height:inherit !important;
              }
              .templateContainer{
                  max-width:600px !important;
              }
              a.mcnButton{
                  display:block;
              }
              .mcnImage,.mcnRetinaImage{
                  vertical-align:bottom;
              }
              .mcnTextContent{
                  word-break:break-word;
              }
              .mcnTextContent img{
                  height:auto !important;
              }
              .mcnDividerBlock{
                  table-layout:fixed !important;
              }
              h1{
                  color:#222222;
                  font-family:Helvetica;
                  font-size:40px;
                  font-style:normal;
                  font-weight:bold;
                  line-height:150%;
                  letter-spacing:normal;
                  text-align:center;
              }
              h2{
                  color:#222222;
                  font-family:Helvetica;
                  font-size:34px;
                  font-style:normal;
                  font-weight:bold;
                  line-height:150%;
                  letter-spacing:normal;
                  text-align:left;
              }
              h3{
                  color:#444444;
                  font-family:Helvetica;
                  font-size:22px;
                  font-style:normal;
                  font-weight:bold;
                  line-height:150%;
                  letter-spacing:normal;
                  text-align:left;
              }
              h4{
                  color:#949494;
                  font-family:Georgia;
                  font-size:20px;
                  font-style:italic;
                  font-weight:normal;
                  line-height:125%;
                  letter-spacing:normal;
                  text-align:center;
              }
              #templateHeader{
                  background-color:#F7F7F7;
                  background-image:url("https://mcusercontent.com/92e86ff813fa11fe1ae78e56e/images/08dd3c81-40d3-4c50-8314-35bacf973f0b.png");
                  background-repeat:no-repeat;
                  background-position:center;
                  background-size:cover;
                  border-top:0;
                  border-bottom:0;
                  padding-top:54px;
                  padding-bottom:54px;
              }
              .headerContainer{
                  background-color:transparent;
                  background-image:none;
                  background-repeat:no-repeat;
                  background-position:center;
                  background-size:cover;
                  border-top:0;
                  border-bottom:0;
                  padding-top:0;
                  padding-bottom:0;
              }
              .headerContainer .mcnTextContent,.headerContainer .mcnTextContent p{
                  color:#757575;
                  font-family:Helvetica;
                  font-size:16px;
                  line-height:150%;
                  text-align:left;
              }
              .headerContainer .mcnTextContent a,.headerContainer .mcnTextContent p a{
                  color:#007C89;
                  font-weight:normal;
                  text-decoration:underline;
              }
              #templateBody{
                  background-color:#FFFFFF;
                  background-image:none;
                  background-repeat:no-repeat;
                  background-position:center;
                  background-size:cover;
                  border-top:0;
                  border-bottom:0;
                  padding-top:36px;
                  padding-bottom:54px;
              }
              .bodyContainer{
                  background-color:transparent;
                  background-image:none;
                  background-repeat:no-repeat;
                  background-position:center;
                  background-size:cover;
                  border-top:0;
                  border-bottom:0;
                  padding-top:0;
                  padding-bottom:0;
              }
              .bodyContainer .mcnTextContent,.bodyContainer .mcnTextContent p{
                  color:#757575;
                  font-family:Helvetica;
                  font-size:16px;
                  line-height:150%;
                  text-align:left;
              }
              .bodyContainer .mcnTextContent a,.bodyContainer .mcnTextContent p a{
                  color:#007C89;
                  font-weight:normal;
                  text-decoration:underline;
              }
              #templateFooter{
                  background-color:#333333;
                  background-image:url("https://mcusercontent.com/92e86ff813fa11fe1ae78e56e/images/08dd3c81-40d3-4c50-8314-35bacf973f0b.png");
                  background-repeat:no-repeat;
                  background-position:center;
                  background-size:cover;
                  border-top:0;
                  border-bottom:0;
                  padding-top:45px;
                  padding-bottom:63px;
              }
              .footerContainer{
                  background-color:transparent;
                  background-image:none;
                  background-repeat:no-repeat;
                  background-position:center;
                  background-size:cover;
                  border-top:0;
                  border-bottom:0;
                  padding-top:0;
                  padding-bottom:0;
              }
              .footerContainer .mcnTextContent,.footerContainer .mcnTextContent p{
                  color:#FFFFFF;
                  font-family:Helvetica;
                  font-size:12px;
                  line-height:150%;
                  text-align:center;
              }
              .footerContainer .mcnTextContent a,.footerContainer .mcnTextContent p a{
                  color:#FFFFFF;
                  font-weight:normal;
                  text-decoration:underline;
              }
          @media only screen and (min-width:768px){
              .templateContainer{
                  width:600px !important;
              }

      }   @media only screen and (max-width: 480px){
              body,table,td,p,a,li,blockquote{
                  -webkit-text-size-adjust:none !important;
              }

      }   @media only screen and (max-width: 480px){
              body{
                  width:100% !important;
                  min-width:100% !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnRetinaImage{
                  max-width:100% !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnImage{
                  width:100% !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnCartContainer,.mcnCaptionTopContent,.mcnRecContentContainer,.mcnCaptionBottomContent,.mcnTextContentContainer,.mcnBoxedTextContentContainer,.mcnImageGroupContentContainer,.mcnCaptionLeftTextContentContainer,.mcnCaptionRightTextContentContainer,.mcnCaptionLeftImageContentContainer,.mcnCaptionRightImageContentContainer,.mcnImageCardLeftTextContentContainer,.mcnImageCardRightTextContentContainer,.mcnImageCardLeftImageContentContainer,.mcnImageCardRightImageContentContainer{
                  max-width:100% !important;
                  width:100% !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnBoxedTextContentContainer{
                  min-width:100% !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnImageGroupContent{
                  padding:9px !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnCaptionLeftContentOuter .mcnTextContent,.mcnCaptionRightContentOuter .mcnTextContent{
                  padding-top:9px !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnImageCardTopImageContent,.mcnCaptionBottomContent:last-child .mcnCaptionBottomImageContent,.mcnCaptionBlockInner .mcnCaptionTopContent:last-child .mcnTextContent{
                  padding-top:18px !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnImageCardBottomImageContent{
                  padding-bottom:9px !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnImageGroupBlockInner{
                  padding-top:0 !important;
                  padding-bottom:0 !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnImageGroupBlockOuter{
                  padding-top:9px !important;
                  padding-bottom:9px !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnTextContent,.mcnBoxedTextContentColumn{
                  padding-right:18px !important;
                  padding-left:18px !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnImageCardLeftImageContent,.mcnImageCardRightImageContent{
                  padding-right:18px !important;
                  padding-bottom:0 !important;
                  padding-left:18px !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcpreview-image-uploader{
                  display:none !important;
                  width:100% !important;
              }

      }   @media only screen and (max-width: 480px){
              h1{
                  font-size:30px !important;
                  line-height:125% !important;
              }

      }   @media only screen and (max-width: 480px){
              h2{
                  font-size:26px !important;
                  line-height:125% !important;
              }

      }   @media only screen and (max-width: 480px){
              h3{
                  font-size:20px !important;
                  line-height:150% !important;
              }

      }   @media only screen and (max-width: 480px){
              h4{
                  font-size:18px !important;
                  line-height:150% !important;
              }

      }   @media only screen and (max-width: 480px){
              .mcnBoxedTextContentContainer .mcnTextContent,.mcnBoxedTextContentContainer .mcnTextContent p{
                  font-size:14px !important;
                  line-height:150% !important;
              }

      }   @media only screen and (max-width: 480px){
              .headerContainer .mcnTextContent,.headerContainer .mcnTextContent p{
                  font-size:16px !important;
                  line-height:150% !important;
              }

      }   @media only screen and (max-width: 480px){
              .bodyContainer .mcnTextContent,.bodyContainer .mcnTextContent p{
                  font-size:16px !important;
                  line-height:150% !important;
              }

      }   @media only screen and (max-width: 480px){
              .footerContainer .mcnTextContent,.footerContainer .mcnTextContent p{
                  font-size:14px !important;
                  line-height:150% !important;
              }

      }</style>                 <link rel="stylesheet" href="https://us17.campaign-archive.com/css/archivebar-desktop.css" mc:nocompile>  </head>
              <body style="height: 100%;margin: 0;padding: 0;width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
              <!---->
              <center>
                  <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;">
                      <tr>
                          <td align="center" valign="top" id="bodyCell" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;">
                              <!-- BEGIN TEMPLATE // -->
                              <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                  <tr>
                                      <td align="center" valign="top" id="templateHeader" data-template-container="" style="background:#F7F7F7 url(&quot;https://mcusercontent.com/92e86ff813fa11fe1ae78e56e/images/08dd3c81-40d3-4c50-8314-35bacf973f0b.png&quot;) no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #F7F7F7;background-image: url(https://mcusercontent.com/92e86ff813fa11fe1ae78e56e/images/08dd3c81-40d3-4c50-8314-35bacf973f0b.png);background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 54px;padding-bottom: 54px;">
                                          <!--[if (gte mso 9)|(IE)]>
                                          <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
                                          <tr>
                                          <td align="center" valign="top" width="600" style="width:600px;">
                                          <![endif]-->
                                          <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;max-width: 600px !important;">
                                              <tr>
                                                  <td valign="top" class="headerContainer" style="background:transparent none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: transparent;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0;padding-bottom: 0;"><table class="mcnImageBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" cellspacing="0" cellpadding="0" border="0">
          <tbody class="mcnImageBlockOuter">
                  <tr>
                      <td style="padding: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnImageBlockInner" valign="top">
                          <table class="mcnImageContentContainer" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" cellspacing="0" cellpadding="0" border="0" align="left">
                              <tbody><tr>
                                  <td class="mcnImageContent" style="padding-right: 9px;padding-left: 9px;padding-top: 0;padding-bottom: 0;text-align: center;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" valign="top">


                                              <img alt="" src="${Global.LOGO}" style="max-width: 186px;padding-bottom: 0;display: inline !important;vertical-align: bottom;border: 0;height: auto;outline: none;text-decoration: none;-ms-interpolation-mode: bicubic;" class="mcnImage" width="172.98000000000002" align="middle">


                                  </td>
                              </tr>
                          </tbody></table>
                      </td>
                  </tr>
          </tbody>
      </table></td>
                                              </tr>
                                          </table>
                                          <!--[if (gte mso 9)|(IE)]>
                                          </td>
                                          </tr>
                                          </table>
                                          <![endif]-->
                                      </td>
                                  </tr>
                                  <tr>
                                      <td align="center" valign="top" id="templateBody" data-template-container="" style="background:#FFFFFF none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #FFFFFF;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 36px;padding-bottom: 54px;">
                                          <!--[if (gte mso 9)|(IE)]>
                                          <table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
                                          <tr>
                                          <td align="center" valign="top" width="600" style="width:600px;">
                                          <![endif]-->
                                          <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;max-width: 600px !important;">
                                              <tr>
                                                  <td valign="top" class="bodyContainer" style="background:transparent none no-repeat center/cover;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: transparent;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 0;padding-bottom: 0;"><table class="mcnDividerBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;" width="100%" cellspacing="0" cellpadding="0" border="0">
          <tbody class="mcnDividerBlockOuter">
              <tr>
                  <td class="mcnDividerBlockInner" style="min-width: 100%;padding: 27px 18px 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                      <table class="mcnDividerContent" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" cellspacing="0" cellpadding="0" border="0">
                          <tbody><tr>
                              <td style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                  <span></span>
                              </td>
                          </tr>
                      </tbody></table>
      <!--
                      <td class="mcnDividerBlockInner" style="padding: 18px;">
                      <hr class="mcnDividerContent" style="border-bottom-color:none; border-left-color:none; border-right-color:none; border-bottom-width:0; border-left-width:0; border-right-width:0; margin-top:0; margin-right:0; margin-bottom:0; margin-left:0;" />
      -->
                  </td>
              </tr>
          </tbody>
      </table><table class="mcnDividerBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;table-layout: fixed !important;" width="100%" cellspacing="0" cellpadding="0" border="0">
          <tbody class="mcnDividerBlockOuter">
              <tr>
                  <td class="mcnDividerBlockInner" style="min-width: 100%;padding: 9px 18px 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                      <table class="mcnDividerContent" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" cellspacing="0" cellpadding="0" border="0">
                          <tbody><tr>
                              <td style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                  <span></span>
                              </td>
                          </tr>
                      </tbody></table>
      <!--
                      <td class="mcnDividerBlockInner" style="padding: 18px;">
                      <hr class="mcnDividerContent" style="border-bottom-color:none; border-left-color:none; border-right-color:none; border-bottom-width:0; border-left-width:0; border-right-width:0; margin-top:0; margin-right:0; margin-bottom:0; margin-left:0;" />
      -->
                  </td>
              </tr>
          </tbody>
      </table><table class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" cellspacing="0" cellpadding="0" border="0">
          <tbody class="mcnTextBlockOuter">
              <tr>
                  <td class="mcnTextBlockInner" style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" valign="top">
                      <!--[if mso]>
                      <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                      <tr>
                      <![endif]-->

                      <!--[if mso]>
                      <td valign="top" width="600" style="width:600px;">
                      <![endif]-->
                      <table style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer" width="100%" cellspacing="0" cellpadding="0" border="0" align="left">
                          <tbody><tr>

                              <td class="mcnTextContent" style="padding-top: 0;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #757575;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;" valign="top">
      &nbsp;

      <div>

      <p style="font-size: 15px !important;margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;line-height: 150%;text-align: left;"><span style="font-size:14px">Email: ${result.email}</span></p>

      <p style="font-size: 15px !important;margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;line-height: 150%;text-align: left;"><span style="font-size:14px">Subject: ${result.subject}</span></p>
      </div>

      <div>
      <p style="font-size: 15px !important;margin: 10px 0;padding: 0;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #757575;font-family: Helvetica;line-height: 150%;text-align: left;"><span style="font-size:14px">Description: ${result.description}</span></p>

      <h3 style="text-align: left;display: block;margin: 0;padding: 0;color: #444444;font-family: Helvetica;font-size: 22px;font-style: normal;font-weight: bold;line-height: 150%;letter-spacing: normal;"><span style="font-size:18px">Thank You,</span></h3>

      <div style="text-align: left;"><span style="font-size:15px">${Global.APP_NAME}</span></div>
      </div>

                              </td>
                          </tr>
                      </tbody></table>
                      <!--[if mso]>
                      </td>
                      <![endif]-->

                      <!--[if mso]>
                      </tr>
                      </table>
                      <![endif]-->
                  </td>
              </tr>
          </tbody>
      </table>
              </center>
          </body>
          </html>`
    callback(template)
  },
  send_otp: function (result, callback) {
    const template = `


      <!DOCTYPE html >
      <head>
        <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>Email otp</title>

        <style type="text/css">
      @media only screen and (max-width: 480px) {
          body,
          table,
          td,
          p,
          a,
          li,
          blockquote {
              -webkit-text-size-adjust: none !important
          }
          body {
              width: 100% !important;
              min-width: 100% !important
          }
          td[id=bodyCell] {
              padding: 10px !important
          }
          table.kmMobileHide {
              display: none !important
          }
          table[class=kmTextContentContainer] {
              width: 100% !important
          }
          table[class=kmBoxedTextContentContainer] {
              width: 100% !important
          }
          td[class=kmImageContent] {
              padding-left: 0 !important;
              padding-right: 0 !important
          }
          img[class=kmImage],
          img.kmImage {
              width: 100% !important
          }
          td.kmMobileStretch {
              padding-left: 0 !important;
              padding-right: 0 !important
          }
          table[class=kmSplitContentLeftContentContainer],
          table.kmSplitContentLeftContentContainer,
          table[class=kmSplitContentRightContentContainer],
          table.kmSplitContentRightContentContainer,
          table[class=kmColumnContainer],
          td[class=kmVerticalButtonBarContentOuter] table[class=kmButtonBarContent],
          td[class=kmVerticalButtonCollectionContentOuter] table[class=kmButtonCollectionContent],
          table[class=kmVerticalButton],
          table[class=kmVerticalButtonContent] {
              width: 100% !important
          }
          td[class=kmButtonCollectionInner] {
              padding-left: 9px !important;
              padding-right: 9px !important;
              padding-top: 9px !important;
              padding-bottom: 0 !important;
              background-color: transparent !important
          }
          td[class=kmVerticalButtonIconContent],
          td[class=kmVerticalButtonTextContent],
          td[class=kmVerticalButtonContentOuter] {
              padding-left: 0 !important;
              padding-right: 0 !important;
              padding-bottom: 9px !important
          }
          table[class=kmSplitContentLeftContentContainer] td[class=kmTextContent],
          table[class=kmSplitContentRightContentContainer] td[class=kmTextContent],
          table[class=kmColumnContainer] td[class=kmTextContent],
          table[class=kmSplitContentLeftContentContainer] td[class=kmImageContent],
          table[class=kmSplitContentRightContentContainer] td[class=kmImageContent],
          table.kmSplitContentLeftContentContainer td.kmImageContent,
          table.kmSplitContentRightContentContainer td.kmImageContent {
              padding-top: 9px !important
          }
          td[class="rowContainer kmFloatLeft"],
          td.rowContainer.kmFloatLeft,
          td[class="rowContainer kmFloatLeft firstColumn"],
          td.rowContainer.kmFloatLeft.firstColumn,
          td[class="rowContainer kmFloatLeft lastColumn"],
          td.rowContainer.kmFloatLeft.lastColumn {
              float: left;
              clear: both;
              width: 100% !important
          }
          table[class=templateContainer],
          table[class="templateContainer brandingContainer"],
          div[class=templateContainer],
          div[class="templateContainer brandingContainer"],
          table[class=templateRow] {
              max-width: 600px !important;
              width: 100% !important
          }
          h1 {
              font-size: 24px !important;
              line-height: 130% !important
          }
          h2 {
              font-size: 20px !important;
              line-height: 130% !important
          }
          h3 {
              font-size: 18px !important;
              line-height: 130% !important
          }
          h4 {
              font-size: 16px !important;
              line-height: 130% !important
          }
          td[class=kmTextContent] {
              font-size: 14px !important;
              line-height: 130% !important
          }
          td[class=kmTextBlockInner] td[class=kmTextContent] {
              padding-right: 18px !important;
              padding-left: 18px !important
          }
          table[class="kmTableBlock kmTableMobile"] td[class=kmTableBlockInner] {
              padding-left: 9px !important;
              padding-right: 9px !important
          }
          table[class="kmTableBlock kmTableMobile"] td[class=kmTableBlockInner] [class=kmTextContent] {
              font-size: 14px !important;
              line-height: 130% !important;
              padding-left: 4px !important;
              padding-right: 4px !important
          }
      }
        </style>
        </head>
        <body style="margin:0;padding:0;background-color:#FFF">
          <center>
            <table align="center" border="0" cellpadding="0" cellspacing="0" id="bodyTable" width="100%" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;padding:0;background-color:#FFF;height:100%;margin:0;width:100%">
              <tbody>
                <tr>
                  <td align="center" id="bodyCell" valign="top" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;padding-top:50px;padding-left:20px;padding-bottom:20px;padding-right:20px;border-top:0;height:100%;margin:0;width:100%">
                    <!--[if !mso]><!-->
                    <div class="templateContainer" style="border:1px none #aaa;background-color:#FFF;display: table; width:600px">
                      <div class="templateContainerInner" style="padding:0">
                        <!--<![endif]-->
      <!--[if mso]>
                    <table border="0" cellpadding="0" cellspacing="0" class="templateContainer"  width="600" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;">
                    <tbody>
                      <tr>
                        <td class="templateContainerInner" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;">
                          <![endif]-->
                          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0">
                            <tr>
                              <td align="center" valign="top" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0">
                                <table border="0" cellpadding="0" cellspacing="0" class="templateRow" width="100%" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0">
                                  <tbody>
                                    <tr>
                                      <td class="rowContainer kmFloatLeft" valign="top" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0">
                                        <table border="0" cellpadding="0" cellspacing="0" class="kmTextBlock" width="100%" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0">
                                          <tbody class="kmTextBlockOuter">
                                            <tr>
                                              <td class="kmTextBlockInner" valign="top" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;">
                                                <table align="left" border="0" cellpadding="0" cellspacing="0" class="kmTextContentContainer" width="100%" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0">
                                                  <tbody>
                                                    <tr>
                                                      <td class="kmTextContent" valign="top" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;color:#000;font-family:Helvetica, Arial;font-size:14px;line-height:150%;text-align:left;padding-top:9px;padding-bottom:9px;padding-left:18px;padding-right:18px;">
                                                      </td>
                                                    </tr>
                                                  </tbody>
                                                </table>
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                        <table border="0" cellpadding="0" cellspacing="0" class="kmImageBlock" width="100%" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;min-width:100%">
                                          <tbody class="kmImageBlockOuter">
                                            <tr>
                                              <td class="kmImageBlockInner" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;padding:9px;" valign="top">
                                                <table align="left" border="0" cellpadding="0" cellspacing="0" class="kmImageContentContainer" width="100%" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;min-width:100%">
                                                  <tbody>
                                                    <tr>
                                                      <td class="kmImageContent" valign="top" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;padding:0;padding-top:0px;padding-bottom:0;padding-left:9px;padding-right:9px;">

                                                        <!-- Your Logo -->

                                                        <img alt="Logo" class="kmImage" width="250" height="220" src="${Global.LOGO}" style="border:0;height:auto;line-height:100%;outline:none;text-decoration:none;padding-bottom:0;display:inline;vertical-align:bottom;margin-left:0;" />
                                                      </td>
                                                    </tr>
                                                  </tbody>
                                                </table>
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                        <table border="0" cellpadding="0" cellspacing="0" class="kmTextBlock" width="100%" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0">
                                          <tbody class="kmTextBlockOuter">
                                            <tr>
                                              <td class="kmTextBlockInner" valign="top" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;">
                                                <table align="left" border="0" cellpadding="0" cellspacing="0" class="kmTextContentContainer" width="100%" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0">
                                                  <tbody>
                                                    <tr>
                                                      <td class="kmTextContent" valign="top" style="border-collapse:collapse;mso-table-lspace:0;mso-table-rspace:0;color:#000;font-family:Helvetica, Arial;font-size:14px;line-height:150%;text-align:left;padding-top:9px;padding-bottom:9px;padding-left:18px;padding-right:18px;">
                                                        <span style="color:#000000;"></span>

                                                        <!-- Your Content Goes here -->

                                                        <p style="margin:0;padding-bottom:1em;text-align: justify;"><span style="font-size:16px;"><span style="color: rgb(0, 0, 0);"><span style="font-family: arial,helvetica,sans-serif;"></span></span></span></p>
                                                        <p style="margin:0;padding-bottom:1em"><span style="font-family:arial,helvetica,sans-serif;"><span style="font-size: 16px;">Hello <strong>${result.first_name} ${result.last_name}</strong>,</span></span></p>
                                                        <!-- <p style="margin:0;padding-bottom:1em"><span style="font-family:arial,helvetica,sans-serif;"><span style="font-size: 16px;">Please use below link to change your Password!</span></span></p> -->
                                                        <p style="margin:0;padding-bottom:1em"><span style="font-family:arial,helvetica,sans-serif;"><span style="font-size: 16px;"></span></span><br />
                                                          <span style="font-family:arial,helvetica,sans-serif;"><span style="font-size: 16px;"></span></span></p>
                                                          <p style="margin:0;padding-bottom:1em"><span style="font-family:arial,helvetica,sans-serif;">
                                                          <span style="font-size: 16px;">${Global.APP_NAME} One Time Password (OTP) is : ${result.forgot_otp}</span></span></p>
                                                          <p style="margin:0;padding-bottom:1em"><span style="font-family:arial,helvetica,sans-serif;"><span style="font-size: 16px;">Thank you,</span></span></p>
                                                          <p style="margin:0;padding-bottom:0"><span style="font-family:arial,helvetica,sans-serif;"><span style="font-size: 16px;">${Global.APP_NAME} Team;</span></span></p>
                                                        </td>
                                                      </tr>
                                                    </tbody>
                                                  </table>
                                                </td>
                                              </tr>
                                            </tbody>
                                          </table>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            </table>
                            <!--[if !mso]><!-->
                          </div>
                        </div>
                        <!--<![endif]-->
      <!--[if mso]>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <![endif]-->
                  </td>
                </tr>
              </tbody>
            </table>
          </center>
        </body>
      </html>
    `
    callback(template)
  },
  send_email_verify: function (result, callback) {
    const template = `
    <!DOCTYPE html>
      <html>
      <head>

        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>Email Confirmation</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style type="text/css">
        /**
         * Google webfonts. Recommended to include the .woff version for cross-client compatibility.
         */
        @media screen {
          @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 400;
            src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v10/ODelI1aHBYDBqgeIAH2zlBM0YzuT7MdOe03otPbuUS0.woff) format('woff');
          }
          @font-face {
            font-family: 'Source Sans Pro';
            font-style: normal;
            font-weight: 700;
            src: local('Source Sans Pro Bold'), local('SourceSansPro-Bold'), url(https://fonts.gstatic.com/s/sourcesanspro/v10/toadOcfmlt9b38dHJxOBGFkQc6VGVFSmCnC_l7QZG60.woff) format('woff');
          }
        }
        /**
         * Avoid browser level font resizing.
         * 1. Windows Mobile
         * 2. iOS / OSX
         */
        body,
        table,
        td,
        a {
          -ms-text-size-adjust: 100%; /* 1 */
          -webkit-text-size-adjust: 100%; /* 2 */
        }
        /**
         * Remove extra space added to tables and cells in Outlook.
         */
        table,
        td {
          mso-table-rspace: 0pt;
          mso-table-lspace: 0pt;
        }
        /**
         * Better fluid images in Internet Explorer.
         */
        img {
          -ms-interpolation-mode: bicubic;
        }
        /**
         * Remove blue links for iOS devices.
         */
        a[x-apple-data-detectors] {
          font-family: inherit !important;
          font-size: inherit !important;
          font-weight: inherit !important;
          line-height: inherit !important;
          color: inherit !important;
          text-decoration: none !important;
        }
        /**
         * Fix centering issues in Android 4.4.
         */
        div[style*="margin: 16px 0;"] {
          margin: 0 !important;
        }
        body {
          width: 100% !important;
          height: 100% !important;
          padding: 0 !important;
          margin: 0 !important;
        }
        /**
         * Collapse table borders to avoid space between cells.
         */
        table {
          border-collapse: collapse !important;
        }
        a {
          color: #1a82e2;
        }
        img {
          height: auto;
          line-height: 100%;
          text-decoration: none;
          border: 0;
          outline: none;
        }
        </style>

      </head>
      <body style="background-color: #e9ecef;">

        <!-- start preheader -->
        <div class="preheader" style="display: none; max-width: 0; max-height: 0; overflow: hidden; font-size: 1px; line-height: 1px; color: #fff; opacity: 0;">
          A preheader is the short summary text that follows the subject line when an email is viewed in the inbox.
        </div>
        <!-- end preheader -->

        <!-- start body -->
        <table border="0" cellpadding="0" cellspacing="0" width="100%">

          <!-- start logo -->
          <tr>
            <td align="center" bgcolor="#e9ecef">
              <!--[if (gte mso 9)|(IE)]>
              <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
              <tr>
              <td align="center" valign="top" width="600">
              <![endif]-->
              <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">
                <tr>
                  <td align="center" valign="top" style="padding: 36px 24px;">
                    <a href="https://www.blogdesire.com" target="_blank" style="display: inline-block;">
                      <img src="${Global.LOGO}" alt="Logo" border="0" width="48" style="display: block; width: 60px; max-width: 48px; min-width: 48px;">
                    </a>
                  </td>
                </tr>
              </table>
              <!--[if (gte mso 9)|(IE)]>
              </td>
              </tr>
              </table>
              <![endif]-->
            </td>
          </tr>
          <!-- end logo -->

          <!-- start hero -->
          <tr>
            <td align="center" bgcolor="#e9ecef">
              <!--[if (gte mso 9)|(IE)]>
              <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
              <tr>
              <td align="center" valign="top" width="600">
              <![endif]-->
              <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">
                <tr>
                  <td align="left" bgcolor="#ffffff" style="padding: 36px 24px 0; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; border-top: 3px solid #d4dadf;">
                    <h1 style="margin: 0; font-size: 32px; font-weight: 700; letter-spacing: -1px; line-height: 48px;">Confirm Your Email Address</h1>
                  </td>
                </tr>
              </table>
              <!--[if (gte mso 9)|(IE)]>
              </td>
              </tr>
              </table>
              <![endif]-->
            </td>
          </tr>
          <!-- end hero -->

          <!-- start copy block -->
          <tr>
            <td align="center" bgcolor="#e9ecef">
              <!--[if (gte mso 9)|(IE)]>
              <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
              <tr>
              <td align="center" valign="top" width="600">
              <![endif]-->
              <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">

                <!-- start copy -->
                <tr>
                  <td align="left" bgcolor="#ffffff" style="padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">
                    <p style="margin: 0;">Tap the button below to confirm your email address. If you didn't create an account with ${Global.APP_NAME}, you can safely delete this email.</p>
                  </td>
                </tr>
                <!-- end copy -->

                <!-- start button -->
                <tr>
                  <td align="left" bgcolor="#ffffff">
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                      <tr>
                        <td align="center" bgcolor="#ffffff" style="padding: 12px;">
                          <table border="0" cellpadding="0" cellspacing="0">
                            <tr>
                              <td align="center" bgcolor="#1a82e2" style="border-radius: 6px;">
                                <a href="${result.url}" target="_blank" style="display: inline-block; padding: 16px 36px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 6px;">Confirm Account</a>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- end button -->

                <!-- start copy -->
                <tr>
                  <td align="left" bgcolor="#ffffff" style="padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">
                    <p style="margin: 0;">If that doesn't work, copy and paste the following link in your browser:</p>
                    <p style="margin: 0;"><a href="${result.url}" target="_blank">${result.url}</a></p>
                  </td>
                </tr>
                <!-- end copy -->

                <!-- start copy -->
                <tr>
                  <td align="left" bgcolor="#ffffff" style="padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px; border-bottom: 3px solid #d4dadf">
                    <p style="margin: 0;">Thank you,<br> <b>${Global.APP_NAME} Team</b></p>
                  </td>
                </tr>
                <!-- end copy -->

              </table>
              <!--[if (gte mso 9)|(IE)]>
              </td>
              </tr>
              </table>
              <![endif]-->
            </td>
          </tr>
          <!-- end copy block -->

          <!-- start footer -->
          <tr>
            <td align="center" bgcolor="#e9ecef" style="padding: 24px;">
              <!--[if (gte mso 9)|(IE)]>
              <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
              <tr>
              <td align="center" valign="top" width="600">
              <![endif]-->
              <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">

                <!-- start permission -->
                <tr>
                  <td align="center" bgcolor="#e9ecef" style="padding: 12px 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #666;">
                    <p style="margin: 0;">You received this email because we received a request for [type_of_action] for your account. If you didn't request [type_of_action] you can safely delete this email.</p>
                  </td>
                </tr>
                <!-- end permission -->

                <!-- start unsubscribe -->
                <tr>
                  <td align="center" bgcolor="#e9ecef" style="padding: 12px 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #666;">
                    <p style="margin: 0;">To stop receiving these emails, you can <a href="https://www.blogdesire.com" target="_blank">unsubscribe</a> at any time.</p>
                    <p style="margin: 0;">Paste 1234 S. Broadway St. City, State 12345</p>
                  </td>
                </tr>
                <!-- end unsubscribe -->

              </table>
              <!--[if (gte mso 9)|(IE)]>
              </td>
              </tr>
              </table>
              <![endif]-->
            </td>
          </tr>
          <!-- end footer -->

        </table>
        <!-- end body -->

      </body>
      </html>
    `
    callback(template)
  },
}

module.exports = template
