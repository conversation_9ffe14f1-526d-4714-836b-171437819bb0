const stripe = require('stripe')(process.env.SECRET_KEY);
const fs = require('fs');
const { logger } = require('../src/utils');

class Customstripe {

    /*
     ** Function to generate token of card using stripe library
     ** 15-11-2019
     ** @param {Object Of Card Parameters} cardobject
     ** @param {Function} callback
     */
    createCardToken(cardobject, callback) {
        stripe.tokens.create({
            card: cardobject
        }, function(err, token) {
            if (err) {
                callback('0', err.message, null);
            } else {
                callback('1', "Card Token Generation Success", token);
            }
        });
    }

    /*
     ** Function to generate payment method id using stripe library
     ** 07-11-2022
     ** @param {card token} cardtoken
     ** @param {Function} callback
     */
    createpaymentmethod(cardtoken, callback) {
        stripe.paymentMethods.create({
            type: 'card',
            card: {
                token: cardtoken,
            },
        }, function(err, paymentMethod) {
            if (err) {
                callback('0', err.message, null);
            } else {
                callback('1', "Payment Method Generation Success", paymentMethod);
            }
        });
    }

    /*
     ** Function to update card using stripe library
     ** 12-01-2021
     ** @param {Object Of Card Parameters} cardobject
     ** @param {Function} callback
     */
    updateCardDetails(cardDetails, cardobject, callback) {
        stripe.customers.updateSource(cardDetails.customer_id, cardDetails.stripe_card_id, cardobject, function(err, card) {
            if (err) {
                callback('0', err.message, null);
            } else {
                callback('1', "Card Update Success", card);
            }
        });
    }

    /*
     ** Function to delete card using stripe library
     ** 12-01-2021
     ** @param {Object Of Card Parameters} cardobject
     ** @param {Function} callback
     */
    deleteCardDetails(cardobject, callback) {
        stripe.customers.deleteSource(cardobject.customer_id, cardobject.stripe_card_id, function(err, card) {
            if (err) {
                callback('0', err.message, null);
            } else {
                callback('1', "Card Delete Success", card);
            }
        });
    }

    /*
     ** Function to create customer using the token of stripe
     ** 15-11-2019
     ** @param {Customer Obejct} customerobject
     ** @param {Function} callback
     */
    createCustomer(customerobject, callback) {
        stripe.customers.create(
            customerobject,
            function(err, customer) {
                if (err) {
                    callback('0', err.message, null);
                } else {
                    callback('1', "Customer create Success", customer);
                }
            });
    }

    /*
     ** Function to create customer using the token of stripe
     ** 15-11-2019
     ** @param {Customer Obejct} customerobject
     ** @param {Function} callback
     */
    deleteCustomer(customer_id, callback) {
        stripe.customers.del(
            customer_id,
            function(err, customer) {
                if (err) {
                    callback('0', err.message, null);
                } else {
                    callback('1', "Customer delete Success", customer);
                }
            });
    }

    /*
     ** Note : Without hold functionality. No need to capture it.
     ** Function to transfer money from card to stripe platform account without destination
     ** 15-11-2019
     ** @param {Payment Object Of Stripe} paymentobject
     ** @param {Function} callback
     */
    tranferStripePlatform(paymentobject, callback) {
        stripe.charges.create(paymentobject, function(errors, charge) {
            if (!errors && charge != undefined) {
                var charge = {
                    transaction_id: charge.id,
                    balance_transaction: charge.balance_transaction
                }
                callback('1', "Transfer success", charge);
            } else {
                console.log("tranferStripePlatform",errors.message);
                callback('0', "Payment has failed due to incorrect details. Please try after some time", null);
            }
        });
    }
    /*
     ** Function to upload bank stripe connect related account
     ** 23-01-2020
     ** @param {Request Data} request
     ** @param {Function} callback
     */
    uploadBankDocument(request, callback) {
      try {
        const fp = fs.readFileSync(request.filepath);
        stripe.files.create(
          {
            purpose: request.purpose,
            file: {
              data: fp,
              name: request.bank_document,
              type: 'application/octet-stream'
            }
          },
          (errors, fileUpload) => {
            if (errors) {
              logger.error(`Failed to upload a file ${ request.filepath } to stripe.`, errors);
              callback('0', errors.message, null);
              return;
            }

            logger.info(`Uploaded a file ${ request.filepath } to stripe`);
            callback('1', "File upload success", fileUpload);
          });
      } catch (e) {
        logger.error('Error during stripe file upload.', e);
        callback('0', e.message, null);
      }
    }

    async registerPerson(account, personObj) {
        const person = await stripe.accounts.createPerson(account.id, personObj);
        return person;
    }

    /*
     ** Function to create account on stripe
     ** 23-01-2020
     ** @param {Account Object} accountObject
     ** @param {Function} callback
     */
    createAccount(accountObject, callback) {
      stripe.accounts.create(
        accountObject,
        (errors, account) => {
          if (errors) {
            logger.error('Failed to create a stripe account.', errors);
            callback('0', errors.message, null);
            return;
          }

          logger.info('Created a stripe account');
          callback('1', 'Account Created', account);
      });
    }

    /*
     ** Function to create stripe charge [Note : if payment object contains capture parameter set to false than need to capture this charge]
     ** @description This is not direct charge so transaction fees will be deducted from platform account [client account]
     ** @param {Payment Object} paymentobject
     ** @param {Function} callback
     */
    createStripeCharge(paymentobject, callback) {
        stripe.charges.create(paymentobject, function(err, charge) {
            if (!err) {
                callback('1', "Charge Created", charge);
            } else {
                console.log("createStripeCharge",err.message);
                callback('0', "Payment has failed due to incorrect details. Please try after some time", null);
            }
        });
    }

    /*
     ** Function to capture the charge which is created with capture false otherwise no need for capture
     ** @param {Charge ID} charge_id
     ** @param {Function} callback
     */
    captureStripeCharge(charge_id,amount, callback) {
        if(amount > 0){
            stripe.charges.capture(charge_id,{amount:Math.round(amount * 100)}, function(err, charge) {
                if (!err) {
                    callback('1', "Charge Captured", charge);
                } else {
                    if(err.message.includes("has already been captured")){
                        callback('0', "Your charge has already been captured", null);
                    } else {
                        callback('0', err.message, null);
                    }
                }
            });
        } else {
            stripe.charges.capture(charge_id, function(err, charge) {
                if (!err) {
                    callback('1', "Charge Captured", charge);
                } else {
                    if(err.message.includes("has already been captured")){
                        callback('0', "Your charge has already been captured", null);
                    } else {
                        callback('0', err.message, null);
                    }
                }
            });
        }
    }

    /*
     ** Function for create payment intents
     ** @param {payment object} payment object
     ** @param {function} callback
     */
    createpaymentintents(paymentobject, callback) {
        stripe.paymentIntents.create(paymentobject, function(err, paymentintent) {
            if (err) {
                console.log("createpaymentintents",err.message)
                callback('0', "Payment has failed due to incorrect details. Please try after some time", null);
            } else {
                callback('1', "payment intents Created", paymentintent);
            }
        });
    }

    /*
     ** Function to refund the charge which is put on hold by stripe.charge method
     ** @param {Charge ID} charge_id
     ** @param {Function} callback
     */
    createChargeRefund(charge_id, amount, callback) {
        if(amount > 0){
            stripe.refunds.create({ charge: charge_id, amount: Math.round(amount*100), reverse_transfer: true }, function(error, refund) {
                if (!error) {
                    callback('1', "Charge Refunded", refund);
                } else {
                    if(error.message.includes("has already been refunded")){
                        callback('0', "Your charge has already been refunded", null);
                    } else {
                        callback('0', error.message, null);
                    }
                }
            });
        } else {
            stripe.refunds.create({ charge: charge_id, reverse_transfer: true }, function(error, refund) {
                if (!error) {
                    callback('1', "Charge Refunded", refund);
                } else {
                    if(error.message.includes("has already been refunded")){
                        callback('0', "Your charge has already been refunded", null);
                    } else {
                        callback('0', error.message, null);
                    }
                }
            });
        }
    }

    /*
     ** Function to create payout
     ** @param {Charge ID} charge_id
     ** @param {Function} callback
     */
    createPayout(payoutParams, stripe_account, callback) {
        stripe.payouts.create(payoutParams, stripe_account, function (error, payoutResult) {
            if (!error && payoutResult != undefined) {
                callback(payoutResult.pending, 'success', '1');
            } else {
                callback(undefined, error.message, '0');
            }
        });
    }

    createTransfer(transferobj,callback){
        stripe.transfers.create({
            amount: transferobj.amount,
            currency: transferobj.currency,
            destination: transferobj.account_id,
            transfer_group: transferobj.description,
        }, function (error, transferResult) {
            if (!error && transferResult != undefined) {
                callback(transferResult, 'success', '1');
            } else {
                callback(null, error.message, '0');
            }
        });
    }
}

// var stripeObj = new Customstripe();
module.exports = new Customstripe();
