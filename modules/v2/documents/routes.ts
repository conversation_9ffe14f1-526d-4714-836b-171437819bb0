import { Request, Response } from 'express'
import Router from 'express-promise-router'
import { healthResponse } from '@/modules/v2/health'
import { logger } from '@/src/utils'
import { getSPTermsSettings } from '@/src/service/serviceProvider'
import { decrypt } from '@/src/service'

import path from 'path'

const router = Router()
const BASE_URL = '/document'

logger.info(`Route: ${BASE_URL}/health`)
logger.info(`Route: ${BASE_URL}/cancellation_policy`)
logger.info(`Route: ${BASE_URL}/about_us`)
logger.info(`Route: ${BASE_URL}/terms_and_conditions`)
logger.info(`Route: ${BASE_URL}/privacy_policy`)
logger.info(`Route: ${BASE_URL}/faq`)

router.get(`/document/health`, healthResponse)
router.get(`/document/cancellation_policy`, cancellationPolicyRoute)
router.get(`/document/about_us`, aboutUsRoute)
router.get(`/document/terms_and_conditions`, termsRoute)
router.get(`/document/privacy_policy`, privacyPolicyRoute)
router.get(`/document/faq`, faqRoute)

/**
 * @swagger
 * tags:
 *   name: Documents
 *   description: Cancellation Policty document that is personalized to a service provider.
 * /v2/document/cancellation_policy:
 *   post:
 *     tags: [cancellation_policy]
 *     summary: Return cancellation policy document for a service provider.
 *     requestBody:
 *       required: true
 *       content:
 *        application/json:
 *          schema:
 *            type: object
 *            properties:
 *              service_provider_id:
 *                type: string 
 */
async function  cancellationPolicyRoute(req: Request, res: Response) {
  const body = decrypt(req.body)
  const sp_id = body?.service_provider_id || req.query?.service_provider_id
  
  let termsSettings: any
  if (sp_id) {
    termsSettings = await getSPTermsSettings(sp_id)
  }
  
  if (!termsSettings || termsSettings.length === 0 || termsSettings.customize_cancellation_charges.toLowerCase() !== 'yes') {
    termsSettings = {
      autocancellation_charges: '100',
      cancellation_charges: '33',
      cancellationwithin_hours: '24'
    }
  }

  const htmlPath = path.join(`${ __dirname }/cancellationPolicy.ejs`)
  return res.render(htmlPath,
    { 
      cancel_within_hours: termsSettings.cancellationwithin_hours, 
      short_notice_cancel_fee: `${termsSettings.cancellation_charges}%`, 
      fee_without_notice: `${termsSettings.autocancellation_charges}%`,
    }
  )
}

async function aboutUsRoute(_: Request, res: Response) {
  const htmlPath = path.join(__dirname + '/aboutUs.ejs')
  return res.render(htmlPath)
} // boookingPaymentRoute


async function termsRoute(_: Request, res: Response) {
  const htmlPath = path.join(__dirname + '/terms.ejs')
  return res.render(htmlPath)
} // boookingPaymentRoute


async function privacyPolicyRoute(_: Request, res: Response) {
  const htmlPath = path.join(__dirname + '/privacyPolicy.ejs')
  return res.render(htmlPath)
} // boookingPaymentRoute


async function faqRoute(_: Request, res: Response) {
  const htmlPath = path.join(__dirname + '/faq.ejs')
  return res.render(htmlPath)
} // boookingPaymentRoute


export default router
