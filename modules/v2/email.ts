import { Request, Response } from 'express'
import { executeWorker, sendEmailWithReportsAttachments, USER_TYPE } from '@/src/service'
import { createPool } from 'generic-pool'
import Router from 'express-promise-router'
import os from 'os'

const EmailRouter = Router()

EmailRouter.post('/email/reports/send', sendReportEmail)

const cpuCount = os.cpus().length
const maxThreads = Math.max(1, Math.min(cpuCount - 1, 4))

const workerPool = createPool({
  create: async () => ({}),
  destroy: async () => {},
}, {
  max: maxThreads,
  min: 0,
  acquireTimeoutMillis: 60000,
})

async function sendReportEmailThreaded(req: Request, res: Response) {
  try {
    const {
      recipientEmail,
      serviceProviderId,
      startDate,
      endDate,
      timezone,
      fileFormat,
      scope,
    } = req.body

    if (
      req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
      req.user_id?.toString() !== serviceProviderId.toString()
    ) {
      return res
        .status(403)
        .json({ message: 'Wrong persona is requesting to email a report' })
    }

    const worker = await workerPool.acquire()
      .catch(() => null)

    if (!worker) {
      return res.status(429).json({
        message: 'Server is busy processing reports. Please try again later.'
      })
    }

    try {
      const result = await executeWorker('workers/emailReportWorker.ts', {
        serviceProviderId,
        scope,
        startDate,
        endDate,
        timezone,
        recipientEmail,
        fileFormat,
      })

      if (result.success) {
        res.status(200).json({ message: 'Email report sent successfully' })
      } else {
        res.status(500).json({ error: result.error || 'Failed to send email report' })
      }
    } finally {
      await workerPool.release(worker)
    }

  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
    res.status(500).json({ error: errorMessage })
  }
}

async function sendReportEmail(req: Request, res: Response) {
  try {
    const {
      recipientEmail,
      serviceProviderId,
      startDate,
      endDate,
      timezone,
      fileFormat,
      scope,
    } = req.body

    if (
      req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
      req.user_id?.toString() !== serviceProviderId.toString()
    ) {
      return res
        .status(403)
        .json({ message: 'Wrong persona is requesting to email a report' })
    }

    await sendEmailWithReportsAttachments(
      serviceProviderId,
      scope,
      startDate,
      endDate,
      timezone,
      recipientEmail,
      fileFormat,
    )

    res.status(200).json({ message: 'Email report sent successfully' })
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
    res.status(500).json({ error: errorMessage })
  }
}

export { EmailRouter }
