import { Request, Response } from 'express'
import Router from 'express-promise-router'
import { executeWorker, FILE_FORMAT, generateReportsBuffer, REPORT_DATE_SCOPE, USER_TYPE } from '@/src/service'

const FilesRouter = Router()

FilesRouter.get('/files/reports', downloadReport)

const CONTENT_TYPES = {
  PDF: 'application/pdf',
  EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
}

const FILE_EXTENSIONS = {
  PDF: 'pdf',
  EXCEL: 'xlsx',
}

async function downloadReportThreaded(req: Request, res: Response) {
  try {
    const {
      serviceProviderId,
      scope,
      startDate,
      endDate,
      timezone,
      fileFormat,
    }: {
      serviceProviderId: number
      startDate: string
      endDate: string
      timezone: string
      fileFormat: FILE_FORMAT
      scope: REPORT_DATE_SCOPE
    } = req.query as any

    if (
      req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
      req.user_id?.toString() !== serviceProviderId.toString()
    ) {
      return res
        .status(403)
        .json({ message: 'Wrong persona is requesting to download a report' })
    }

    const result = await executeWorker('workers/reportBufferWorker.ts', {
      serviceProviderId,
      scope,
      startDate,
      endDate,
      timezone,
      fileFormat,
    }) as { success: boolean, buffer?: string, error?: string }

    if (result.success) {
      const fileBuffer = Buffer.from(result.buffer || '', 'base64')

      res.setHeader('Content-Type', CONTENT_TYPES[fileFormat.toUpperCase()])
      res.setHeader('Content-Disposition', `attachment; filename=report.${FILE_EXTENSIONS[fileFormat.toUpperCase()]}`)
      res.send(fileBuffer)
    } else {
      res.status(500).json({ error: result.error || 'Failed to generate report' })
    }

  } catch (error: any) {
    res.status(500).json({ error: error.message || 'Failed to generate report' })
  }
}

async function downloadReport(req: Request, res: Response) {
  try {
    const {
      serviceProviderId,
      startDate,
      endDate,
      timezone,
      fileFormat,
      scope,
    }: {
      serviceProviderId: number
      startDate: string
      endDate: string
      timezone: string
      fileFormat: FILE_FORMAT,
      scope: REPORT_DATE_SCOPE,
    } = req.query as any

    if (
      req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
      req.user_id?.toString() !== serviceProviderId.toString()
    ) {
      return res
        .status(403)
        .json({ message: 'Wrong persona is requesting to download a report' })
    }

    const result = await generateReportsBuffer(serviceProviderId, scope, startDate, endDate, timezone, fileFormat)
    res.setHeader('Content-Type', CONTENT_TYPES[fileFormat.toUpperCase()])
    res.setHeader('Content-Disposition', `attachment; filename=report.${FILE_EXTENSIONS[fileFormat.toUpperCase()]}`)
    res.send(result)
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to generate report'
    res.status(500).json({ error: errorMessage })
  }
}

export { FilesRouter }
