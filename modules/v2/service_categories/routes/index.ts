import { Request, Response } from 'express'
import Router from 'express-promise-router'
import { healthResponse } from '@/modules/v2/health'
import { getServiceCategories } from '@/src/service'

const router = Router()

router.get(`/service_categories/health`, healthResponse)
router.get(`/service_categories`, getServiceCategoryList)

async function getServiceCategoryList(req: Request, res: Response) {
  const { page = 1 }: { page: number } = req.query as any
  res.status(200).json(await getServiceCategories(page))
}

export default router
