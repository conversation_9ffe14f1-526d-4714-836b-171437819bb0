import express, { Request, Response } from 'express'
import { setStripeAccountData, verifySignatureAndGetEvent } from '@/src/service'
import { logger } from '@/src/utils'

const WebhooksRouter = express.Router()

WebhooksRouter.post('/webhooks/stripe', captureStripe)

async function captureStripe(req: Request, res: Response): Promise<Response> {
  const signature = req.headers['stripe-signature']
  if (!signature || typeof signature !== 'string') {
    return res.status(400).json({
      error: 'Invalid signature header',
    })
  }

  try {
    const event = await verifySignatureAndGetEvent(req.body, signature)
    if (event.type === 'account.updated') {
      const { object: eventData } = event.data
      await setStripeAccountData(
        eventData.id,
        eventData.payouts_enabled,
        eventData.individual?.verification?.status === 'verified',
        eventData.individual?.ssn_last_4_provided || false
      )
    }
    return res.json({ received: true })
  } catch (error) {
    logger.error(`StripeError: ${error}`)
    return res.status(400).json({
      error: `Webhook error: ${String(error)}`,
    })
  }
}

export { WebhooksRouter }
