import { Request, Response } from 'express'
import Router from 'express-promise-router'
import { logger } from '@/src/utils'
import { upsertTwilioChat } from "@/src/service";

const router = Router()
const BASE_URL = '/chat'

logger.info(`Route: ${ BASE_URL }/rooms`)

/**
 * @swagger
 * tags:
 *   name: chat-room
 *   description: The chat managing api
 * /v2/chat/rooms:
 *   post:
 *     tags: [chat-room]
 *     summary: Create a new chat room
 *     requestBody:
 *       required: true
 *       content:
 *        application/json:
 *          schema:
 *            $ref: '#/components/schema/CreateChatRoomRequest'
 */
router.post(`/chat/rooms`, createRoom)

async function createRoom(req: Request, res: Response) {
  const body = req.body

  if (!body.senderId || !body.receiverId || !body.senderType || !body.receiverType) {
    res.status(400)
      .json({ message: 'Missing required fields. [receiverId, receiverType, senderId, senderType] are required.' })
    return
  }

  const data = await upsertTwilioChat(body)
  return res.status(200).json({ data })
}

export default router

/**
 * @swagger
 *  components:
 *      schema:
 *          CreateChatRoomRequest:
 *              type: object
 *              properties:
 *                  senderId:
 *                      type: number
 *                  senderType:
 *                      type: string
 *                  receiverId:
 *                      type: number
 *                  receiverType:
 *                      type: string
 *          Other:
 *              type: object
 *              properties:
 *                  test:
 *                      type: string
 */
