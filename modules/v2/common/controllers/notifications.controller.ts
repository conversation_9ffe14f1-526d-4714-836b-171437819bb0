import { Request, Response } from 'express'
import { NotificationService } from '../service/notifications.service'
import { PATH_PARAMS } from '../constants'

const service = new NotificationService()

export const readAllNotifications = async (req: Request, res: Response) => {
  const { user_id: userId, user_type: userType } = req
  await service.readAllNotifications(Number(userId), userType || null, res)
}

export const markOneAsRead = async (req: Request, res: Response) => {
  const { user_id: userId, user_type: userType } = req
  const { [PATH_PARAMS.NOTIFICATION_ID]: notificationId } = req.params
  await service.markOneAsRead(Number(notificationId), Number(userId), userType || null, res)
}

export const NotificationController = {
  readAllNotifications,
  markOneAsRead,
}
