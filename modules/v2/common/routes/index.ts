import express from 'express'
import { NotificationController } from '../controllers/notifications.controller'
import { PATH_PARAMS } from '../constants'
import { logRoutes } from '@/src/utils/logger'

const router = express.Router()

router.patch(
  `/notifications/read_all`,
  NotificationController.readAllNotifications
)
router.patch(
  `/notifications/:${PATH_PARAMS.NOTIFICATION_ID}/read`,
  NotificationController.markOneAsRead
)

logRoutes(router)

export { router as NotificationsRouter }
