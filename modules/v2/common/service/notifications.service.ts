import { Response } from 'express'
import { Notification } from '@/src/db/models/notifications'
import { logger } from '@/src/utils'

export class NotificationService {
  public async readAllNotifications(userId: number, userType: string | null, res: Response) {
    try {
      const [rowsUpdated] = await Notification.update(
        { status: 'Read' },
        { where: { receiverId: userId, status: 'Unread', receiverType: userType || '' } }
      )

      if (!rowsUpdated) {
        logger.info(`No notifications found for user ${userId}`)
        return res.status(404).json({
          message: `No notifications found for user ${userId}`,
        })
      }

      res.status(201).json({
        message: `All notifications marked as read for user ${userId}`,
      })
    } catch (err) {
      logger.error(`Failed to update notifications for user ${userId}: ${err}`)
      res.status(500).json({
        error: 'Failed to update notifications. Please try again later.',
      })
    }
  }

  public async markOneAsRead(
    notificationId: number,
    userId: number,
    userType: string | null,
    res: Response
  ) {
    try {
      const [rowsUpdated] = await Notification.update(
        { status: 'Read' },
        {
          where: {
            id: notificationId,
            receiverId: userId,
            receiverType: userType || '',
          },
        }
      )
      if (rowsUpdated) {
        return res.status(201).json({ message: 'notification marked as read' })
      }
    } catch (err) {
      logger.error(err)
    }
  }
}
