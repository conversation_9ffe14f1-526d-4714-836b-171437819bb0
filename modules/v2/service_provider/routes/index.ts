import { Request, Response, NextFunction } from 'express'
import Router from 'express-promise-router'
import { healthResponse } from '@/modules/v2/health'
import {
  logger,
  maskEmail,
  SIMPLE_DATE_FORMAT,
  SIMPLE_DATE_FORMAT_WITH_24HR_TIME,
  SIMPLE_DATE_FORMAT_WITH_24HR_TIME_WITH_SECONDS,
} from '@/src/utils'
import t from 'localizify'
import {
  USER_LOGIN_STATUS,
  USER_STATUS,
  createPromo as createPromotion,
  getPromoById,
  getPromosListForClient,
  getForProvider,
  softDeletePromo,
  validate,
  USER_TYPE,
  generateBookingsReport,
  getAvailableSlots,
  generateRevenueReport,
  generateClientReport,
  encrypt,
  createAccountLink,
  addBankAccountOrCard,
  createStripeAccount,
  setStripeBankAccountData,
  getServiceProvider,
  setStripeCardData,
  getProductsListForServiceProvider,
  getServiceProviderClients,
  getServicesListForServiceProvider,
  getServiceProviderAccData,
  getReviewList,
  getServiceProviderBookingsHistory,
  updateAvailability,
  getWorkSchedule,
  getServiceProviderSchedule,
  getServiceProviderByEmailShallow,
  verifyServiceProviderOTP,
  upsertSlots,
  getScheduleSlots,
  ScheduleSlot,
  upsertTimeOffs,
  getTimeOffs,
  TimeOff,
  deleteCustomScheduleSlot,
  deleteTimeOffSlot,
  deleteBusinessLocation,
  getBusinessLocationById,
  createBusinessLocation,
  getBusinessLocations,
  getServiceProviderProfileStatus,
  updateBusinessLocation,
  updateServiceProviderPassword,
  updateServiceProvider,
  sendServiceProviderOTP,
  deleteServiceProviderClient,
  getServiceProvidersMetadata,
} from '@/src/service'
import { StripeAccountLinkReqBody } from '@/src/service/serviceProvider/types'
import { validateCard } from '@/src/helper/validation/card.validation'
import Stripe from 'stripe'
import { validateBankAccount } from '@/src/helper/validation/bank-account.validation'
import { validateBusinessLocation } from '@/src/helper/validation/location-availability.validation'
import moment from 'moment'
import { AppError } from '@/src/helper/errors/app-error'
import Joi from 'joi'
import { find as geoFind } from 'geo-tz'

const router = Router()
const BASE_URL = '/service_provider'

const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.params, { abortEarly: false })

    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      })
    }

    req.params = value
    next()
  }
}

const validateBody = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body, { abortEarly: false })

    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      })
    }

    req.body = value
    next()
  }
}

const deleteLocationSchema = Joi.object({
  id: Joi.number().required(),
  locationId: Joi.number().required()
})

const deleteSlotSchema = Joi.object({
  id: Joi.number().required(),
  locationId: Joi.number().required(),
  slotId: Joi.number().required()
})

const getLocationSchema = Joi.object({
  id: Joi.number().required(),
  locationId: Joi.number().required()
})

const scheduleBreakSchema = Joi.object({
  startTime: Joi.string()
    .pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .required()
    .messages({
      'string.pattern.base': 'startTime must be in HH:mm format (24h format, e.g., 12:00)',
      'any.required': 'startTime is required for breaks'
    }),
  endTime: Joi.string()
    .pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .required()
    .messages({
      'string.pattern.base': 'endTime must be in HH:mm format (24h format, e.g., 13:00)',
      'any.required': 'endTime is required for breaks'
    })
}).custom((obj, helpers) => {
  const startTime = obj.startTime
  const endTime = obj.endTime

  if (startTime && endTime) {
    const start = moment(startTime, 'HH:mm')
    const end = moment(endTime, 'HH:mm')

    if (!start.isBefore(end)) {
      throw new Error('break endTime must be after startTime')
    }
  }

  return obj
})

const scheduleSlotSchema = Joi.object({
  fromTime: Joi.string()
    .pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .allow('')
    .default('')
    .optional()
    .messages({
      'string.pattern.base': 'fromTime must be in HH:mm format (24h format, e.g., 09:00)'
    }),
  toTime: Joi.string()
    .pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .allow('')
    .default('')
    .optional()
    .messages({
      'string.pattern.base': 'toTime must be in HH:mm format (24h format, e.g., 17:00)'
    }),
  day: Joi.string()
    .valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')
    .when('date', {
      is: Joi.exist().not(null),
      then: Joi.optional().default(null),
      otherwise: Joi.required()
    })
    .allow(null),
  date: Joi.date()
    .iso()
    .optional()
    .allow(null)
    .default(null)
    .messages({
      'date.base': `date must be in ${SIMPLE_DATE_FORMAT} format`,
      'date.format': `date must be in ${SIMPLE_DATE_FORMAT} format`,
      'date.past': 'date and time combination cannot be in the past'
    })
    .custom((value, helpers) => {
      if (!value) {
        return value
      }

      const fromTime = helpers.state.ancestors[0].fromTime
      if (!fromTime) {
        return value
      }

      const utcDate = moment.utc(value).format(SIMPLE_DATE_FORMAT)
      const dateTime = moment.utc(`${utcDate} ${fromTime}`, SIMPLE_DATE_FORMAT_WITH_24HR_TIME)
      const now = moment.utc()

      if (!dateTime.isValid()) {
        return helpers.error('date.format')
      }

      if (moment.utc(value).isBefore(now, 'day') || dateTime.isBefore(now)) {
        return helpers.error('date.past')
      }
      return utcDate
    }),
  breaks: Joi.array()
    .items(scheduleBreakSchema)
    .optional()
    .messages({
      'array.base': 'breaks must be an array'
    })
}).custom((obj, helpers) => {
  const hasFromTime = obj.fromTime && obj.fromTime.match(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
  const hasToTime = obj.toTime && obj.toTime.match(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)

  if ((hasFromTime && !hasToTime) || (!hasFromTime && hasToTime)) {
    return helpers.error('any.custom', { message: 'both fromTime and toTime must be provided if either is set' })
  }

  if (obj.date && (!hasFromTime || !hasToTime)) {
    return helpers.error('any.custom', { message: 'both fromTime and toTime are required when date is provided' })
  }

  // Validate breaks are within slot time range
  if (obj.breaks && obj.breaks.length > 0 && hasFromTime && hasToTime) {
    const slotStart = moment(obj.fromTime, 'HH:mm')
    const slotEnd = moment(obj.toTime, 'HH:mm')

    for (const breakItem of obj.breaks) {
      const breakStart = moment(breakItem.startTime, 'HH:mm')
      const breakEnd = moment(breakItem.endTime, 'HH:mm')

      if (breakStart.isBefore(slotStart) || breakEnd.isAfter(slotEnd)) {
        throw new Error(`break time (${breakItem.startTime}-${breakItem.endTime}) must be within slot time (${obj.fromTime}-${obj.toTime})`)
      }
    }
  }

  return obj
})

function validateSchedule (req: Request, res: Response, next: NextFunction) {
  const { error, value } = Joi.array()
    .items(scheduleSlotSchema)
    .required()
    .min(1)
    .messages({
      'array.min': 'At least one schedule slot is required',
      'any.required': 'schedule slots are required'
    })
    .validate(req.body, { abortEarly: false })

  if (error) {
    return res.status(400).json({
      error: 'Validation failed',
      details: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    })
  }

  req.body = value
  next()
}

const updatePasswordSchema = Joi.object({
  password: Joi.string()
    .trim()
    .min(6)
    .required()
    .messages({
      'string.empty': 'Password cannot be empty',
      'string.min': 'Password must have at least 6 characters',
      'any.required': 'Password is required'
    }),
})

const createLocationSchema = Joi.object({
  name: Joi.string().required(),
  latitude: Joi.string().required(),
  longitude: Joi.string().required(),
  countryName: Joi.string().required(),
  countryCode: Joi.string().allow(''),
  stateName: Joi.string().required(),
  cityName: Joi.string().required(),
  postalCode: Joi.string().required(),
  product_tax_percent: Joi.number()
    .min(0)
    .optional()
    .default(0)
    .messages({
      'number.base': 'product_tax_percent must be a number',
      'number.min': 'product_tax_percent must be a positive number'
    }),
  service_tax_percent: Joi.number()
    .min(0)
    .optional()
    .default(0)
    .messages({
      'number.base': 'service_tax_percent must be a number',
      'number.min': 'service_tax_percent must be a positive number'
    }),
  time_slot_duration_minutes: Joi.number()
    .min(0)
    .optional()
    .default(0)
    .messages({
      'number.base': 'time_slot_duration_minutes must be a number',
      'number.min': 'time_slot_duration_minutes must be a positive number'
    }),
  booking_buffer_interval_minutes: Joi.number()
    .min(0)
    .optional()
    .default(0)
    .messages({
      'number.base': 'booking_buffer_interval_minutes must be a number',
      'number.min': 'booking_buffer_interval_minutes must be a positive number'
    }),
  max_future_booking_window_minutes: Joi.number()
    .min(0)
    .optional()
    .default(0)
    .messages({
      'number.base': 'max_future_booking_window_minutes must be a number',
      'number.min': 'max_future_booking_window_minutes must be a positive number'
    }),
})

const getLocationsSchema = Joi.object({
  id: Joi.number().required()
})

const serviceProviderMetadataSchema = Joi.object({
  serviceProviderIds: Joi.array()
    .items(Joi.number().integer().positive())
    .min(1)
    .max(50)
    .required()
    .messages({
      'array.min': 'At least one service provider ID is required',
      'array.max': 'Maximum 50 service provider IDs allowed',
      'any.required': 'serviceProviderIds array is required'
    })
})

logger.info(`Route: ${BASE_URL}/health`)
logger.info(`Route: ${BASE_URL}/get_available_slot`)
logger.info(`Route: ${BASE_URL}/:id/promos`)
logger.info(`Route: ${BASE_URL}/:id/promos/clients/:clientId`)
logger.info(`Route: ${BASE_URL}/:id/promos/:promoId`)

router.get(`/service_provider/health`, healthResponse)
router.post(`/service_provider/get_available_slot`, getAvailableSlot)
router.post(`/service_provider/metadata`, validateBody(serviceProviderMetadataSchema), getServiceProvidersMetadataHandler)
router.get(`/service_provider/:id/promos/:promoId`, getPromo)
router.get(`/service_provider/:id/promos`, getPromosForProvider)
router.get(`/service_provider/:id/promos/clients/:clientId`, getPromosForClient)
router.post(`/service_provider/:id/promos`, createPromo)
router.get(`/service_provider/:id/clients`, getClients)
router.get(`/service_provider/:id/services`, getServices)
router.get(`/service_provider/:id/history/bookings`, getBookingsHistory)
router.get(`/service_provider/:id/products`, getProducts)
router.get(`/service_provider/:id/reviews`, getReviews)
router.get(`/service_provider/:id/reports/booking`, getBookingsReport)
router.get(`/service_provider/:id/reports/revenue`, getRevenueReport)
router.get(`/service_provider/:id/reports/client`, getClientReport)
router.get('/service_provider/:accountId/stripe/account_link', getStripeAccountLink)
router.get(`/service_provider/:id`, getServiceProviderDetails)
router.get(
  `/service_provider/:id/profile-status`,
  validateServiceProvider,
  getServiceProviderProfileStatusHandler,
)
router.get(`/service_provider/:id/schedule`, getServiceProviderHomeScreen)
router.get(
  '/service_provider/:id/locations/:locationId',
  validateServiceProvider,
  validateParams(getLocationSchema),
  getLocation,
)
router.get(
  '/service_provider/:id/locations/:locationId/schedule-slots',
  validateServiceProvider,
  validateGetSchedule,
  getScheduleSlotsApi,
)
router.get(
  '/service_provider/:id/locations/:locationId/time-offs',
  validateServiceProvider,
  validateGetTimeOffs,
  getTimeOffsApi,
)
router.get(
  '/service_provider/:id/locations',
  validateServiceProvider,
  validateParams(getLocationsSchema),
  getLocations
)

router.post('/service_provider/:serviceProviderId/stripe/add_card', addCard)
router.post('/service_provider/:serviceProviderId/stripe/add_bank_account', addBankAccount)
router.post('/service_provider/:id/stripe/create_account', stripeAccount)
router.post('/service_provider/:id/savelocationavailability', validateCallerIsServiceProvider, saveLocationAvailability)
router.post(
  '/service_provider/:id/locations/:locationId/schedule-slots',
  validateServiceProvider,
  validateSchedule,
  saveSchedule,
)
router.post('/service_provider/forgot_password', forgotPassword)
router.put('/service_provider/new_password', validateCallerIsServiceProvider, validateBody(updatePasswordSchema), updatePassword)
router.post('/service_provider/verify_otp', verifyOtp)
router.post(
  '/service_provider/:id/locations/:locationId/time-offs',
  validateServiceProvider,
  validateTimeOffs,
  saveTimeOffs,
)
router.post(
  '/service_provider/:id/locations',
  validateServiceProvider,
  validateBody(createLocationSchema),
  createLocation,
)
router.put(
  '/service_provider/:id/locations/:locationId',
  validateServiceProvider,
  validateBody(createLocationSchema),
  updateLocation,
)

router.delete(`/service_provider/:id/promos/:promoId`, deletePromo)
router.delete(
  '/service_provider/:id/locations/:locationId',
  validateServiceProvider,
  validateParams(deleteLocationSchema),
  deleteLocation
)
router.delete(
  '/service_provider/:id/locations/:locationId/schedule-slots/:slotId',
  validateServiceProvider,
  validateParams(deleteSlotSchema),
  deleteScheduleSlot
)
router.delete(
  '/service_provider/:id/locations/:locationId/time-offs/:slotId',
  validateServiceProvider,
  validateParams(deleteSlotSchema),
  deleteTimeOff
)
router.delete('/service_provider', validateCallerIsServiceProvider, deleteServiceProvider)
router.delete(`/service_provider/clients/:clientId`, validateCallerIsServiceProvider, deleteClient)

async function deleteServiceProvider(req: Request, res: Response) {
  const id = req.user_id

  await updateServiceProvider(Number(id), {
    is_deleted: 1,
    updatetime: moment().format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_WITH_SECONDS),
    login_status: USER_LOGIN_STATUS.OFFLINE,
    status: USER_STATUS.INACTIVE,
  })

  return res.status(200).send({ message: 'Service provider deleted successfully' })

}

async function saveSchedule(req: Request, res: Response) {
  const { id, locationId } = req.params as any

  try {
    const data = await upsertSlots(req.body.map((it: ScheduleSlot) => ({
      ...it,
      serviceProviderId: id,
      businessLocationId: locationId,
    })))
    return res.status(200).json(data)
  } catch (err) {
    logger.error('Failed to save schedule', { error: err, id, locationId })
    return res.status(500).json({ error: 'Failed to save schedule' })
  }
}

async function verifyOtp(
  req: Request<{}, {}, { otp: string | number }, { email: string }>,
  res: Response
) {
  try {
    const { email } = req.query
    const { otp } = req.body
    if (email && otp) {
      const { id } = await getServiceProviderByEmailShallow(email)
      if (id) {
        const result = await verifyServiceProviderOTP({
          status: 'forgotpassword',
          user_id: id,
          otp,
        })
        return res.status(200).json(result)
      }
      return res.status(404).json({
        error: `Service provider with email ${maskEmail(email)} not found`,
      })
    }
    return res.status(400).json({
      error: `Invalid request`,
      message: `Email is required in query params. Otp is required in request body`,
    })
  } catch (err) {
    logger.error(err)
    return res
      .status(err instanceof AppError ? err.statusCode : 500)
      .json({ error: String(err) })
  }
}

async function updatePassword(req: Request, res: Response) {
  const { password } = req.body
  await updateServiceProviderPassword(parseInt(req.user_id as string), password)
  return res.status(200).json({ message: 'Password was successfully updated' })
}

async function forgotPassword(
  req: Request<{}, {}, {}, { email: string }>,
  res: Response
) {
  try {
    const { email } = req.query
    if (!email) {
      return res.status(400).json({ error: `email is missing in query param` })
    }
    const user = await getServiceProviderByEmailShallow(email)
    if (user?.id) {
      await sendServiceProviderOTP({ serviceProviderId: user.id, ...user, status: 'forgotpassword' })
      return res.status(200).json({
        message: `Password reset verification code was sent to your phone and email`,
      })
    } else {
      return res
        .status(400)
        .json({ error: `Service provider with email '${email}' not found` })
    }
  } catch (err) {
    res.status(500).json(`Internal error: ${err}`)
  }
}

async function getServiceProviderHomeScreen(req: Request, res: Response) {
  const { date } = req.query
  const { id } = req.params
  if (!date) {
    return res
      .status(400)
      .json({ error: `date is required in query parameters` })
  }
  if (!moment(String(date), 'YYYY-MM-DD', true).isValid()) {
    return res
      .status(400)
      .json({ error: `Invalid date format, required: "YYYY-MM-DD` })
  }
  if (
    req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
    req.user_id?.toString() !== id.toString()
  ) {
    return res
      .status(403)
      .json({ message: 'Wrong user requesting the clients' })
  }
  const schedule = await getServiceProviderSchedule(Number(id), String(date))
  if (schedule) {
    return res.status(200).json(schedule)
  }
  return res
    .status(404)
    .json({ error: `Nothing found for service provider with id ${id}` })
}

async function saveLocationAvailability(req: Request, res: Response) {
  const { id } = req.params as any
  if (
    req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
    req.user_id?.toString() !== id.toString()
  ) {
    return res
      .status(403)
      .json({ message: 'Wrong persona requesting the clients' })
  }
  const reqBody = await validateBusinessLocation(res, req.body)
  await updateAvailability(id, reqBody!.business_location)
  const locationIds = req.body.business_location
    .filter((location) => location.id && location.id.length)
    .map((location) => location.id)

  let hours = await getWorkSchedule(id, locationIds)
  hours = (hours as any[]).map((item) => {
    return {
      ...item,
      from_time: item.from_time
        ? moment(`1990-10-10 ${item.from_time}`).format('h:mm A')
        : '',
      to_time: item.to_time
        ? moment(`1990-10-10 ${item.to_time}`).format('h:mm A')
        : '',
    }
  })

  const data = {
    business_location: {
      slot_available: hours,
    },
  }
  logger.info({ response: data })
  return res.status(201).json({ message: 'Schedule saved successfully', data })
}

async function getBookingsHistory(req: Request, res: Response) {
  const { id } = req.params as any
  const { page = 1 }: { page: number } = req.query as any

  if (
    req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
    req.user_id?.toString() !== id.toString()
  ) {
    return res
      .status(403)
      .json({ message: 'Wrong persona requesting the clients' })
  }

  return res.status(200).json(await getServiceProviderBookingsHistory(id, page))
}

async function getServiceProviderDetails(req: Request, res: Response) {
  const { id } = req.params as any
  const serviceProvider = await getServiceProviderAccData(Number(id))
  if (serviceProvider) {
    return res.status(200).json(serviceProvider)
  }
  return res.status(404).json({ error: 'Service provider not found.' })
}

async function getServiceProviderProfileStatusHandler(req: Request, res: Response) {
  const { id } = req.params as any
  return res.status(200).json(await getServiceProviderProfileStatus(Number(id)))
}

async function getReviews(req: Request, res: Response) {
  const { id }: { id: number } = req.params as any
  const {
    page = 1,
    createdDate = null,
    userName = null,
    categoryId = null,
    rating = null,
    sort = 'DESC',
  }: {
    page: number
    createdDate: string | null
    userName: string | null
    categoryId: number | null
    rating: number | null
    sort: string | null
  } = req.query as any

  res
    .status(200)
    .json(
      await getReviewList(
        id,
        page,
        createdDate,
        userName,
        categoryId,
        rating,
        sort
      )
    )
}

async function getServiceProvidersMetadataHandler(req: Request, res: Response) {
  const { serviceProviderIds } = req.body
  res.status(200).json(await getServiceProvidersMetadata(serviceProviderIds))
}

async function stripeAccount(req: Request, res: Response) {
  try {
    const { id } = req.params as any
    const data = await createStripeAccount(id)
    res.status(200).json({ data })
  } catch (error) {
    logger.error(error)
    return res.status(400).json({ error: String(error) })
  }
}

async function addCard(
  req: Request<
    { serviceProviderId: number },
    {},
    Stripe.AccountUpdateParams.Card
  >,
  res: Response
) {
  try {
    const { serviceProviderId } = req.params as any
    const serviceProvider = await getServiceProvider(serviceProviderId)
    if (serviceProvider && serviceProvider.merchant_account_id) {
      const card = await validateCard(res, req.body)
      const data = await addBankAccountOrCard(
        serviceProvider.merchant_account_id,
        card,
        'card'
      )
      if (data.card) {
        const cardData = {
          serviceProviderId,
          stripeCardId: data.card.id,
          last4: data.card.last4,
          brand: data.card.brand,
          expMonth: data.card.exp_month,
          expYear: data.card.exp_year,
        }
        await setStripeCardData(cardData)
        return res.status(201).json(cardData)
      }
      return res
        .status(400)
        .json({ error: 'Failed to add card. Please try again.' })
    }
    return res.status(400).json({
      error:
        'Failed to add card. Service provider or his stripe account not found.',
    })
  } catch (error) {
    logger.error(error)
    return res.status(400).json({ error: String(error) })
  }
}

async function addBankAccount(
  req: Request<
    { serviceProviderId: number },
    {},
    Stripe.AccountUpdateParams.BankAccount
  >,
  res: Response
) {
  try {
    const { serviceProviderId } = req.params
    const serviceProvider = await getServiceProvider(serviceProviderId)
    if (!serviceProvider?.merchant_account_id) {
      return res
        .status(400)
        .json({ error: 'No stripe account found for this service provider.' })
    }
    const accountDetails = await validateBankAccount(res, req.body)
    const data = await addBankAccountOrCard(
      serviceProvider.merchant_account_id,
      accountDetails,
      'bank_account'
    )

    if (data.bank_account) {
      const bankAccountData = {
        serviceProviderId,
        stripeBankAccountId: data.bank_account.id,
        last4: data.bank_account.last4,
        routingNumber: data.bank_account.routing_number,
        bankName: data.bank_account.bank_name,
        accountHolderName: data.bank_account.account_holder_name,
        accountHolderType: data.bank_account.account_holder_type,
        country: data.bank_account.country,
        currency: data.bank_account.currency,
      }

      await setStripeBankAccountData(bankAccountData)

      return res.status(201).json(bankAccountData)
    }
    return res
      .status(400)
      .json({ error: 'Failed to add bank account data. Please try again.' })
  } catch (error) {
    logger.error(error)
    return res.status(500).json({ error: 'An internal server error occurred.' })
  }
}

async function getStripeAccountLink(
  req: Request<
    { accountId: string },
    { url: string },
    StripeAccountLinkReqBody,
    { returnUrl: string }
  >,
  res: Response
) {
  const { accountId } = req.params
  const { returnUrl } = req.query
  if (accountId && returnUrl) {
    const accountLink = await createAccountLink(accountId, returnUrl)
    return res.status(301).json(accountLink)
  }
  return res
    .status(400)
    .json({ error: `accountId and returnUrl are required in request body` })
}

async function getProducts(req: Request, res: Response) {
  const { id }: { id: number } = req.params as any
  const { page = 1 }: { page: number } = req.query as any

  if (
    req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
    req.user_id?.toString() !== id.toString()
  ) {
    return res
      .status(403)
      .json({ message: 'Wrong persona requesting the clients' })
  }

  res.status(200).json(await getProductsListForServiceProvider(id, page))
}

async function getServices(req: Request, res: Response) {
  const { id }: { id: number } = req.params as any
  const { page = 1 }: { page: number } = req.query as any

  if (
    req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
    req.user_id?.toString() !== id.toString()
  ) {
    return res
      .status(403)
      .json({ message: 'Wrong persona requesting the clients' })
  }

  res.status(200).json(await getServicesListForServiceProvider(id, page))
}

async function getClients(req: Request, res: Response) {
  const { id }: { id: number } = req.params as any
  const { search, page = 1 }: {
    search: string
    page: number
  } = req.query as any

  if (
    req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
    req.user_id?.toString() !== id.toString()
  ) {
    return res
      .status(403)
      .json({ message: 'Wrong persona requesting the clients' })
  }

  res.status(200).json(await getServiceProviderClients(id, search, page))
}

async function getBookingsReport(req: Request, res: Response) {
  const { id } = req.params as any

  if (
    req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
    req.user_id?.toString() !== id.toString()
  ) {
    return res
      .status(403)
      .json({ message: 'Wrong persona requesting the bookings report' })
  }

  const { scope, startDate, endDate } = req.query as any
  const data = await generateBookingsReport({
    scope,
    startDate,
    endDate,
    serviceProviderId: parseInt(id),
  })
  return sendSuccess(res, { data })
}

async function getRevenueReport(req: Request, res: Response) {
  const { id } = req.params as any

  if (
    req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
    req.user_id?.toString() !== id.toString()
  ) {
    return res
      .status(403)
      .json({ message: 'Wrong persona requesting the revenue report' })
  }

  const { scope, startDate, endDate } = req.query as any
  const data = await generateRevenueReport({
    scope,
    startDate,
    endDate,
    serviceProviderId: parseInt(id),
  })
  return sendSuccess(res, { data })
}

async function getClientReport(req: Request, res: Response) {
  const { id } = req.params as any

  if (
    req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
    req.user_id?.toString() !== id.toString()
  ) {
    return res
      .status(403)
      .json({ message: 'Wrong persona requesting the client report' })
  }

  const { scope, startDate, endDate } = req.query as any
  const data = await generateClientReport({
    scope,
    startDate,
    endDate,
    serviceProviderId: parseInt(id),
  })
  return sendSuccess(res, { data })
}

function sendSuccess(res, data = {}) {
  return res.status(200).json(encrypt({ code: 1, ...data }))
}

async function getAvailableSlot(req: Request, res: Response) {
  try {
    const available_slots = await getAvailableSlots(req.body)
    return res.status(200).json(available_slots)
  } catch (error) {
    logger.error(error)
    return res.status(500).json({ error: 'Server error.' })
  }
} // get_available_slot

async function getPromo(req: Request, res: Response) {
  const { promoId } = req.params as any
  const data = await getPromoById(promoId)
  res.status(200).json({ data })
}

async function getPromosForClient(req: Request, res: Response) {
  const { id, clientId } = req.params as any
  const data = await getPromosListForClient(id, clientId)
  res.status(200).json({ data })
}

async function getPromosForProvider(req: Request, res: Response) {
  const { id } = req.params as any
  const data = await getForProvider(id)
  res.status(200).json({ data })
}

async function deletePromo(req: Request, res: Response) {
  const { promoId } = req.params as any
  await softDeletePromo(promoId, parseInt(req.user_id ?? '0'))
  res.status(200).send()
}

async function createPromo(req: Request, res: Response) {
  const { id } = req.params as any
  const rawData = req.body
  const rules = {
    occasional_promo_image: 'required',
    discount_type: 'required|in:Percentage,Flat',
    discount_amount: 'required|min:0',
    max_discount_amount: 'required_if:discount_type,Percentage|min:0|max:100',
    description: 'required',
    code: 'required',
    valid_till: 'required',
    applied_on: '',
    applied_service_on: '',
    client_list: '',
  }
  const message = { required: t('required') }
  const keywords = { images: t('keyword_images') }
  const errors = validate(rawData, rules, message, keywords)

  if (errors) {
    res.status(400).json(errors)
    return
  }

  const data = await createPromotion({ ...rawData, service_provider_id: id })
  res.status(200).json({ data })
}

function validateGetSchedule(req: Request, res: Response, next: NextFunction) {
  const schema = Joi.object({
    custom: Joi.boolean().default(false),
    month: Joi.date()
      .iso()
      .default(() => moment().startOf('month').format(SIMPLE_DATE_FORMAT))
      .custom((value, helpers) => {
        const date = moment.utc(value).startOf('month').format(SIMPLE_DATE_FORMAT)
        if (!moment(date, SIMPLE_DATE_FORMAT, true).isValid()) {
          return helpers.error('date.format')
        }
        return date
      })
      .messages({
        'date.base': `date must be in ${SIMPLE_DATE_FORMAT} format`,
        'date.format': `date must be in ${SIMPLE_DATE_FORMAT} format`
      })
  })

  const { error, value } = schema.validate(req.query, { abortEarly: false })

  if (error) {
    return res.status(400).json({
      error: 'Validation failed',
      details: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    })
  }

  req.query = value

  next()
}

async function getScheduleSlotsApi(req: Request, res: Response) {
  const { id, locationId } = req.params as any
  const { custom, month } = req.query as any

  try {
    const data = await getScheduleSlots(Number(id), Number(locationId), custom, month)
    return res.status(200).json(data)
  } catch (err) {
    logger.error('Failed to get schedule', { error: err, id, locationId, custom, month })
    return res.status(500).json({ error: 'Failed to get schedule' })
  }
}

function validateServiceProvider(req: Request, res: Response, next: NextFunction) {
  const { id } = req.params

  if (req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER ||
      req.user_id?.toString() !== id.toString()) {
    return res.status(403).json({ message: 'Wrong user' })
  }

  next()
}

function validateCallerIsServiceProvider(req: Request, res: Response, next: NextFunction) {
  if (req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER) {
    return res.status(403).json({ message: 'Not a service provider type requester' })
  }

  next()
}

const timeOffSchema = Joi.object({
  fromTime: Joi.string()
    .pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .required()
    .messages({
      'string.pattern.base': 'fromTime must be in HH:mm format (24h format, e.g., 09:00)'
    }),
  toTime: Joi.string()
    .pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .required()
    .messages({
      'string.pattern.base': 'toTime must be in HH:mm format (24h format, e.g., 17:00)'
    }),
  date: Joi.date()
    .iso()
    .required()
    .custom((value, helpers) => {
      if (!value) {
        return value
      }

      const fromTime = helpers.state.ancestors[0].fromTime
      if (!fromTime) {
        return value
      }

      const utcDate = moment.utc(value).format(SIMPLE_DATE_FORMAT)
      const dateTime = moment.utc(`${utcDate} ${fromTime}`, SIMPLE_DATE_FORMAT_WITH_24HR_TIME)
      const now = moment.utc()

      if (!dateTime.isValid()) {
        return helpers.error('date.format')
      }

      if (moment.utc(value).isBefore(now, 'day') || dateTime.isBefore(now)) {
        return helpers.error('date.past')
      }
      return utcDate
    })
    .messages({
      'date.base': `date must be in ${SIMPLE_DATE_FORMAT} format`,
      'date.format': `date must be in ${SIMPLE_DATE_FORMAT} format`,
      'date.past': 'date and time combination cannot be in the past'
    })
})

function validateTimeOffs(req: Request, res: Response, next: NextFunction) {
  const { error, value } = Joi.array()
    .items(timeOffSchema)
    .required()
    .min(1)
    .messages({
      'array.min': 'At least one time-off slot is required',
      'any.required': 'time-off slots are required'
    })
    .validate(req.body, { abortEarly: false })

  if (error) {
    return res.status(400).json({
      error: 'Validation failed',
      details: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    })
  }

  req.body = value
  next()
}

function validateGetTimeOffs(req: Request, res: Response, next: NextFunction) {
  const schema = Joi.object({
    month: Joi.date()
      .iso()
      .default(() => moment().startOf('month').format(SIMPLE_DATE_FORMAT))
      .custom((value, helpers) => {
        const date = moment.utc(value).startOf('month').format(SIMPLE_DATE_FORMAT)
        if (!moment(date, SIMPLE_DATE_FORMAT, true).isValid()) {
          return helpers.error('date.format')
        }
        return date
      })
      .messages({
        'date.base': `date must be in ${SIMPLE_DATE_FORMAT} format`,
        'date.format': `date must be in ${SIMPLE_DATE_FORMAT} format`
      })
  })

  const { error, value } = schema.validate(req.query, { abortEarly: false })

  if (error) {
    return res.status(400).json({
      error: 'Validation failed',
      details: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    })
  }

  req.query = value
  next()
}

async function saveTimeOffs(req: Request, res: Response) {
  const { id, locationId } = req.params as any
  const data = await upsertTimeOffs(req.body.map((it: TimeOff) => ({
    ...it,
    serviceProviderId: id,
    businessLocationId: locationId,
  })))
  return res.status(200).json(data)
}

async function getTimeOffsApi(req: Request, res: Response) {
  const { id, locationId } = req.params as any
  const { month } = req.query as any
  const data = await getTimeOffs(Number(id), Number(locationId), month)
  return res.status(200).json(data)
}

async function deleteScheduleSlot(req: Request, res: Response) {
  const { id, locationId, slotId } = req.params

  try {
    await deleteCustomScheduleSlot(
      Number(slotId),
      Number(id),
      Number(locationId)
    )
    return res.status(200).json({ message: 'Schedule slot deleted successfully' })
  } catch (error: any) {
    logger.error({ message: 'Failed to delete schedule slot', error, meta: { id, locationId, slotId }})
    return res.status(400).json({ error: error.message })
  }
}

async function deleteTimeOff(req: Request, res: Response) {
  const { id, locationId, slotId } = req.params

  try {
    await deleteTimeOffSlot(
      Number(slotId),
      Number(id),
      Number(locationId)
    )
    return res.status(200).json({ message: 'Time-off slot deleted successfully' })
  } catch (error: any) {
    logger.error({ message: 'Failed to delete time-off slot', error, meta: { id, locationId, slotId }})
    return res.status(400).json({ error: error.message })
  }
}

async function deleteLocation(req: Request, res: Response) {
  const { id, locationId } = req.params

  try {
    await deleteBusinessLocation(
      Number(id),
      Number(locationId)
    )
    return res.status(200).json({ message: 'Business location deleted successfully' })
  } catch (err: any) {
    logger.error('Failed to delete business location', { error: err, id, locationId })
    return res.status(400).json({ error: err.message })
  }
}

async function getLocation(req: Request, res: Response) {
  const { id, locationId } = req.params

  try {
    const location = await getBusinessLocationById(
      Number(id),
      Number(locationId)
    )

    if (!location) {
      return res.status(404).json({ error: 'Business location not found' })
    }

    return res.status(200).json(location)
  } catch (err: any) {
    logger.error('Failed to get business location', { error: err, id, locationId })
    return res.status(400).json({ error: err.message })
  }
}

async function createLocation(req: Request, res: Response) {
  const { id } = req.params
  const locationData = req.body

  try {
    const [timezone] = geoFind(
      Number(locationData.latitude),
      Number(locationData.longitude)
    )

    const newLocationId = await createBusinessLocation({
      service_provider_id: Number(id),
      name: locationData.name,
      latitude: locationData.latitude,
      longitude: locationData.longitude,
      country_name: locationData.countryName,
      country_code: locationData.countryCode || '',
      state_name: locationData.stateName,
      city_name: locationData.cityName,
      postal_code: locationData.postalCode,
      timezone_code: timezone,
      product_tax_percent: locationData.product_tax_percent || 0,
      service_tax_percent: locationData.service_tax_percent || 0,
      time_slot_duration_minutes: locationData.time_slot_duration_minutes,
      booking_buffer_interval_minutes: locationData.booking_buffer_interval_minutes,
      max_future_booking_window_minutes: locationData.max_future_booking_window_minutes,
      status: 'Active',
    })

    return res.status(201).json({
      message: 'Business location created successfully',
      locationId: newLocationId
    })
  } catch (err: any) {
    logger.error({ message: 'Failed to create business location', error: err, meta: { id, locationData } })
    return res.status(400).json({ error: err.message })
  }
}

async function updateLocation(req: Request, res: Response) {
  const { id, locationId } = req.params
  const locationData = req.body

  try {
    const businessLocation = await getBusinessLocationById(Number(id), Number(locationId))

    if (!businessLocation) {
      return res.status(404).send()
    }

    const [timezone] = geoFind(
      Number(locationData.latitude),
      Number(locationData.longitude)
    )

    await updateBusinessLocation({
      id: Number(locationId),
      name: locationData.name,
      latitude: locationData.latitude,
      longitude: locationData.longitude,
      country_name: locationData.countryName,
      country_code: locationData.countryCode || '',
      state_name: locationData.stateName,
      city_name: locationData.cityName,
      postal_code: locationData.postalCode,
      timezone_code: timezone,
      product_tax_percent: locationData.product_tax_percent || 0,
      service_tax_percent: locationData.service_tax_percent || 0,
      time_slot_duration_minutes: locationData.time_slot_duration_minutes || 0,
      booking_buffer_interval_minutes: locationData.booking_buffer_interval_minutes || 0,
      max_future_booking_window_minutes: locationData.max_future_booking_window_minutes || 0,
    })

    return res.status(204).send()
  } catch (error: any) {
    logger.error({ message: 'Failed to update business location', error, meta: { id, ...locationData } })
    return res.status(400).json({ error: error.message })
  }
}

async function getLocations(req: Request, res: Response) {
  const { id } = req.params

  try {
    const locations = await getBusinessLocations(Number(id))
    return res.status(200).json(locations)
  } catch (err: any) {
    logger.error('Failed to get business locations', { error: err, id })
    return res.status(400).json({ error: err.message })
  }
}

async function deleteClient(req: Request, res: Response) {
  const { clientId } = req.params
  await deleteServiceProviderClient(Number(req.user_id), Number(clientId))
  return res.status(200).json({ message: 'Client deleted successfully' })
}

export default router
