import { Request, Response } from 'express'
import Router from 'express-promise-router'
import { BlockList } from '@/src/db/models/blockLists'
import { logger } from '@/src/utils'
import Joi from 'joi'
import { getServiceProviderUUID, getUserUUID, getBlockedUsers, getBlockedUsersCount } from '@/src/service'

const BlockListsRouter = Router()

const pageSchema = Joi.object({
  page: Joi.number()
    .min(1)
    .default(1)
})

const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: Function) => {
    const { error, value } = schema.validate(req.query, { abortEarly: false })

    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      })
    }

    req.query = value
    next()
  }
}

BlockListsRouter.post('/users/:id', async (req: Request, res: Response) => {
  const { id } = req.params
  const blockedByUserUUID = req.user_uuid
  const userType = req.user_type as string

  try {
    const blockedUserUUID = await resolveBlockedUserUUID(id, userType)

    if (!blockedUserUUID) {
      return res.status(404).json({ message: 'User not found' })
    }

    const existingBlock = await BlockList.findOne({
      where: {
        blockedUserUUID,
        blockedByUserUUID,
        isDeleted: false
      }
    }) as any | null

    if (existingBlock) {
      return res.status(200).json({ message: 'User is already blocked' })
    }

    const block = await BlockList.create({
      blockedUserUUID,
      blockedByUserUUID,
      isDeleted: false
    }) as any

    return res.status(201).json({ data: block.id })
  } catch (error: any) {
    logger.error({
      message: 'Failed to block user',
      error,
      meta: { id, blockedByUserUUID }
    })
    return res.status(500).json({ message: 'Failed to block user' })
  }
})

BlockListsRouter.get('/users', validateParams(pageSchema), async (req: Request, res: Response) => {
  const { page } = req.query
  const blockedByUserUUID = req.user_uuid as string

  try {
    const records = await getBlockedUsers(blockedByUserUUID, Number(page))
    const total = await getBlockedUsersCount(blockedByUserUUID)

    return res.status(200).send(records)
  } catch (error: any) {
    logger.error({
      message: 'Failed to fetch blocked users',
      error,
      meta: { blockedByUserUUID, page }
    })
    return res.status(500).send({ message: 'Failed to fetch blocked users' })
  }
})

BlockListsRouter.delete('/users/:id', async (req: Request, res: Response) => {
  const { id } = req.params
  const blockedByUserUUID = req.user_uuid
  const userType = req.user_type as string

  try {
    const blockedUserUUID = await resolveBlockedUserUUID(id, userType)

    if (!blockedUserUUID) {
      return res.status(404).json({ message: 'User not found' })
    }

    const block = await BlockList.findOne({
      where: {
        blockedUserUUID,
        blockedByUserUUID,
        isDeleted: false
      }
    })

    if (!block) {
      return res.status(204).send()
    }

    await block.update({ isDeleted: true })

    return res.status(204).send()
  } catch (error: any) {
    logger.error({
      message: 'Failed to unblock the user',
      error,
      meta: { id, blockedByUserUUID }
    })
    return res.status(500).send({ message: 'Failed to unblock the user' })
  }
})

function resolveBlockedUserUUID(blockedUserId: string, requesterUserType: string): Promise<string | null> {
  if (!isNaN(Number(blockedUserId))) {
    return requesterUserType?.toLocaleLowerCase() === 'user' ?
      getServiceProviderUUID(Number(blockedUserId)) :
      getUserUUID(Number(blockedUserId))
  }

  return Promise.resolve(blockedUserId)
}

export default BlockListsRouter
