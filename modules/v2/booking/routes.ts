import { Request, Response } from "express"
import Router from "express-promise-router"
import { healthResponse } from "@/modules/v2/health"
import { logger } from "@/src/utils"
import { validate } from "@/src/service"
import { payForService } from "@/src/service/bookings"

const router = Router()
const BASE_URL = "/booking"

logger.info(`Route: ${BASE_URL}/health`)
logger.info(`Route: ${BASE_URL}/create`)
logger.info(`Route: ${BASE_URL}/:id/pay`)

router.get(`/booking/health`, healthResponse)
router.post(`/booking/create`, createBookingRoute)
router.post(`/booking/:id/pay`, boookingPaymentRoute)


async function createBookingRoute(req: Request, res: Response) {
  const rules = {
    business_location_id: "required",
    service_provider_id: "required",
    user_id: "required",
    date: "required",
    timeslot: "required",
    service: "",
    product: "",
    description: "",
    promocode: "",
    discount: "",
    tax: "",
    total_amount: "required",
    // remaining_amount: "",
    wallet_amount: "", // amount to deduct from user"s wallet
    total_duration: "required",
  }
  const errors = validate(req.body, rules, { "required": "required" }, {})
  if (errors)
    return res.status(406).json(errors)

  // createBooking()
  return res.status(501).json({ "error": "Not implemented yet" })
} // user_details


async function boookingPaymentRoute(req: Request, res: Response) {
  const booking_id = req.params.id
  const rules = {
    service_provider_id: "required",
    user_id: "required",
    booking_id: "required",
    total_amount: "required",
    card_id: "required",
    tip_amount: ""
  }

  const errors = validate(req.body, rules, { "required": "required" }, {})
  if (errors)
    return res.status(406).json(errors)

  let payment
  try {
    payment = await payForService({ ...req.body, booking_id })
  } catch(err) {
    logger.error(err)
    return res.status(500).json({ "error": `Failed to make a payment for booking ${booking_id}`})
  }

  return res.status(200).json({
    transaction_id: payment?.id,
    amount: payment?.amount,
  })
} // boookingPaymentRoute

export default router
