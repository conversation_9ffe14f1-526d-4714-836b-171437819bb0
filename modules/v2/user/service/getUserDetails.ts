import { Pool } from 'mysql2/promise'
import { db_conn } from '@/src/db'
import { Global } from '@/src/constants'
import { logger } from '@/src/utils/logger'

interface UserDetails {
  unique_id: number
  first_name: string
  last_name: string
  full_name: string
  email: string
  country_code: string
  phone: string
  profile_image: string
  gender: string
  total_reviews: number
  avg_rating: number
  city: string
  dob: Date
  wallet_amount: number
  social_id: number
  login_type: string
  latitude: number
  longitude: number
  fixed_latitude: number
  fixed_longitude: number
  long_address: string
  short_address: string
  app_version: string
  login_status: string
  status: string
  is_deleted: number
  last_login: Date
}

export async function getUserDetails(
  user_id: string,
  token: string
): Promise<UserDetails | null | undefined> {
  if (!user_id)
    return null

  const token_check = token ? `AND device.token = '${token}'` : ""
  const s3ImagePath = `${Global.S3_BUCKET_ROOT}${Global.USER_IMAGE}`
  const query = `SELECT
    unique_id, social_id,
    first_name, last_name, dob,
    CONCAT(user.first_name, ' ', user.last_name) AS full_name,
    email, country_code, phone,
    CONCAT('${s3ImagePath}', user.profile_image) AS profile_image,
    gender, total_reviews, avg_rating,
    city, long_address, short_address,
    login_type, latitude, longitude, fixed_latitude, fixed_longitude,
    app_version, login_status, status, is_deleted, last_login,
    wallet_amount
  FROM
    tbl_user user
  JOIN
    tbl_user_deviceinfo device ON user.id = device.user_id
  WHERE
    user.id = ?
    AND user.is_deleted = 0
    ?
    AND device.user_type = 'user';`

  try {
    const [userDetails] = await db_conn.query(query, [user_id, token_check])
    return userDetails[0] as UserDetails
  } catch (err) {
    logger.error(`Failed to execute query for get_user_detai: ${err}`)
    return null
  }
} // get_user_detail
