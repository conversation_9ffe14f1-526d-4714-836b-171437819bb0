import { NextFunction, Request, Response } from 'express'
import Router from 'express-promise-router'
import { healthResponse } from '@/modules/v2/health'
import { getUserDetails } from '@/modules/v2/user/service'
import {
  getActiveUserByEmail,
  getBookings,
  IBookingsRequest,
  sendOTP,
  updateUser,
  USER_LOGIN_STATUS,
  USER_STATUS,
  USER_TYPE,
  validate,
  verifyOTP,
} from '@/src/service'
import { logger, maskEmail, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_WITH_SECONDS } from '@/src/utils'
import { AppError } from '@/src/helper/errors/app-error'
import moment from 'moment/moment'

const router = Router()
const BASE_URL = '/user'

function validateCallerIsUser(req: Request, res: Response, next: NextFunction) {
  if (req.user_type?.toLowerCase() !== USER_TYPE.USER) {
    return res.status(403).json({ message: 'Not a user type requester' })
  }

  next()
}

logger.info(`Route: ${BASE_URL}/health`)
logger.info(`Route: ${BASE_URL}/:user_id`)

router.get(`/user/health`, healthResponse)
router.get(`/user/:user_id`, userDetailsRoute)
router.get(`/user/bookings`, userBookingsRoute)
router.post(`/user/forgot_password`, forgotPassword)
router.post(`/user/verify_otp`, verifyOtp)

router.delete('/user', validateCallerIsUser, deleteUser)

async function deleteUser(req: Request, res: Response) {
  const id = req.user_id as string
  await updateUser(
    id,
    {
      is_deleted: 1,
      updatetime: moment().format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_WITH_SECONDS),
      login_status: USER_LOGIN_STATUS.OFFLINE,
      status: USER_STATUS.INACTIVE,
    },
  )

  return res.status(200).send({ message: 'User deleted successfully' })
}

async function forgotPassword(
  req: Request<{}, {}, {}, { email: string }>,
  res: Response
) {
  try {
    const { email } = req.query
    if (!email) {
      return res.status(400).json({ error: 'Email is missing in query param' })
    }
    const user = await getActiveUserByEmail(email)
    if (!user?.id) {
      return res
        .status(400)
        .json({ error: `User with email ${maskEmail(email)} not found` })
    }
    await sendOTP({ user_id: user.id, status: 'forgotpassword', email })
    return res.status(200).json({
      message: `Password reset email has been sent to ${maskEmail(email)}`,
    })
  } catch (err) {
    logger.error({
      message: 'Failed to process forgot password request',
      error: err instanceof AppError ? err.message : 'Internal server error',
      email: maskEmail(req.query.email),
      traceId: req.headers['x-trace-id']
    })
    return res.status(err instanceof AppError ? err.statusCode : 500).json({
      error: err instanceof AppError ? err.message : 'Unable to process your request at this time. Please try again later.'
    })
  }
}

async function verifyOtp(
  req: Request<{}, {}, { otp: string | number }, { email: string }>,
  res: Response
) {
  try {
    const { email } = req.query
    const { otp } = req.body
    if (email && otp) {
      const user = await getActiveUserByEmail(email)
      if (!user?.id) {
        return res
          .status(400)
          .json({ error: `User with email ${maskEmail(email)} not found` })
      }
      const result = await verifyOTP({
        status: 'forgotpassword',
        otp,
        user_id: user.id,
      })
      return res.status(200).json(result)
    }
  } catch (err) {
    logger.error({
      message: 'Failed to verify OTP',
      error: err instanceof AppError ? err.message : 'Internal server error',
      email: maskEmail(req.query.email),
      traceId: req.headers['x-trace-id']
    })
    return res.status(err instanceof AppError ? err.statusCode : 500).json({
      error: err instanceof AppError ? err.message : 'Unable to verify your code at this time. Please try again later.'
    })
  }
}

async function userDetailsRoute(req: Request, res: Response) {
  const user_id = req.params.user_id
  if (user_id == undefined) {
    return res.status(400).json({ message: 'User ID not provided!' })
  }

  const userData = await getUserDetails(user_id, req.get('token') as string)
  if (userData == null || userData == undefined)
    return res.status(400).json({ error: 'Failed to get user details' })

  return res.status(200).json({
    user: userData,
  })
} // userDetailsRoute

async function userBookingsRoute(req: Request, res: Response) {
  const props: IBookingsRequest = req.body as IBookingsRequest
  const rules = {
    date: 'required',
    user_id: 'required',
    service_provider_id: '',
    business_location_id: '',
    end_date: '',
  }
  const validator = validate(props, rules, { required: 'required' }, {})
  if (validator) return res.status(406).json(validator)

  props.date = req.body?.date
  props.end_date = req.body?.end_date

  const [bookings] = await getBookings(props)
  return res.status(200).json({ bookings: bookings })
} // userBookingsRoute

export default router
