import Router from 'express-promise-router'
import { CommentLike, deletePostComment } from '@/src/service'

const CommentsRouter = Router()

/**
 * @route POST /comments/:commentId/likes
 * @desc Like a comment
 * @access Private
 */
CommentsRouter.post('/:commentId/likes', async (req, res) => {
  const commentId = parseInt(req.params.commentId)
  const userId = req.user_uuid

  if (!userId) {
    return res.status(401).send({ message: 'User not authenticated' })
  }

  // Check if like already exists
  const existingLike = await CommentLike.findOne({
    where: {
      commentId,
      userId
    }
  })

  if (existingLike) {
    return res.status(201).send({ message: 'Comment already liked' })
  }

  // Create new like
  await CommentLike.create({
    commentId,
    userId
  })

  res.status(201).send({ message: 'Comment liked successfully' })
})

/**
 * @route DELETE /comments/:commentId/likes
 * @desc Unlike a comment
 * @access Private
 */
CommentsRouter.delete('/:commentId/likes', async (req, res) => {
  const commentId = parseInt(req.params.commentId)
  const userId = req.user_uuid

  if (!userId) {
    return res.status(401).send({ message: 'User not authenticated' })
  }

  await CommentLike.destroy({
    where: {
      commentId,
      userId
    }
  })

  res.status(200).send({ message: 'Comment like was removed successfully' })
})

CommentsRouter.delete(
  '/:commentId',
  async (req, res) => {
    const commentId = parseInt(req.params.commentId)
    await deletePostComment(commentId, parseInt(req.user_id as string), req.user_type as string)
    res.status(200).send({ message: 'Comment was successfully deleted' })
  })

export default CommentsRouter
