import { Request, Response } from 'express'
import { deleteImage, USER_TYPE } from '@/src/service'
import Router from 'express-promise-router'

const ImagesRouter = Router()

ImagesRouter.delete('/images/:id', softDeleteImage)

async function softDeleteImage(req: Request, res: Response) {
  const { id } = req.params
  const { user_type } = req

  if (user_type !== USER_TYPE.SERVICE_PROVIDER) {
    return res.status(403).json({ message: 'Forbidden' })
  }

  await deleteImage(Number(id))
  res.status(200).json({ message: 'Image deleted successfully' })
}

export default ImagesRouter
