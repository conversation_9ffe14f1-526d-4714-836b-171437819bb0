import { Request, Response, NextFunction } from 'express'
import Router from 'express-promise-router'
import {
  validate,
  decrypt,
  getBookmarkedServiceProviders
} from '@/src/service'
import {
  getProductCategories,
  addProduct,
  getProducts,
  getMeasureUnits,
  deleteProduct,
  updateProduct,
  getBookmarkedProducts,
  getBookmarkedServices,
} from '@/src/service/product'
import { sendError, sendSuccess } from '@/src/utils'
import { logger } from '@/src/utils'
import { ProductBookmark, ServiceBookmark, Product } from '@/src/db/models/product'
import { Global } from '@/src/constants'
import Joi from 'joi'

const router = Router()

type BookmarkType = 'product' | 'service'
type BookmarkModel = typeof ProductBookmark | typeof ServiceBookmark

const pageSchema = Joi.object({
  page: Joi.number()
    .min(1)
    .default(1)
})

const bookmarksQuery = Joi.object({
  page: Joi.number()
    .min(1)
    .default(1),
  type: Joi.string()
    .required()
    .valid('product', 'service', 'service_provider')
})

const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.params, { abortEarly: false })

    if (error) {
      return res.status(400).json({
        error: 'Params Validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      })
    }

    req.params = value
    next()
  }
}

const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.query, { abortEarly: false })

    if (error) {
      return res.status(400).json({
        error: 'Query Validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      })
    }

    req.params = value
    next()
  }
}

router.post(`/service_provider/add_product`, addProductRoute)
router.post(`/service_provider/all_product`, getAllProductsRoute)
router.post(`/service_provider/product_detail`, getProductByIdRoute)
router.post(`/service_provider/get_product_size`, getMeasureUnitsRoute)
router.post(`/service_provider/product_category_list`, getProductCategoriesRoute)
router.post(`/service_provider/delete_product`, deleteProductRoute)
router.post(`/service_provider/edit_product`, editProductRoute)
router.post(`/user/all_product`, getAllProductsRoute)
router.post(`/user/product_detail`, getProductByIdRouteForUser)
router.post(`/user/category_list`, getProductCategoriesRoute)
router.post(`/user/product_service_bookmark`, addProductServiceBookmarkRoute)
router.post(`/bookmarks`, addProductServiceBookmarkRoute)
router.get(`/bookmarks`, validateQuery(bookmarksQuery), getBookmarksGeneric)
router.get(`/user/bookmarks/products`, validateQuery(pageSchema), (req, res) => getBookmarksRoute(req, res, 'product'))
router.get(`/user/bookmarks/services`, validateQuery(pageSchema), (req, res) => getBookmarksRoute(req, res, 'service'))
router.get(`/user/bookmarks/service_providers`, validateQuery(pageSchema), (req, res) => getBookmarksRoute(req, res, 'service_provider'))

async function getAllProductsRoute(req: Request, res: Response) {
  const rawData = decrypt(req.body)
  const isUser = req.user_type === 'user'
  const service_provider_id = rawData.service_provider_id
  const user_id = isUser ? req.user_id : 0
  const page = rawData.page || 0

  try {
    const products = await getProducts({ service_provider_id, user_id, page })
    return sendSuccess(res, { data: products })
  } catch (error: any) {
    logger.error({
      message: 'Failed to get products for this provider',
      error,
      meta: { service_provider_id, page }
    })
    return sendError(res, { message: 'Failed to get products for this provider'}, 1)
  }
}

async function getProductByIdRoute(req: Request, res: Response) {
  const rawData = decrypt(req.body)
  const product_id = parseFloat(rawData.product_id)
  const isUser = req.user_type === 'user'
  const service_provider_id = req.user_id || req.params?.user_id
  const user_id = isUser ? req.user_id : 0

  try {
    let products = await getProducts({ service_provider_id, user_id, product_id })
    products = await normalizeProductDetailResponse(products)
    products = products.length > 0 ? products[0] : {} as any
    return sendSuccess(res, { data: products })
  } catch (error: any) {
    logger.error({
      message: 'Failed to get details for this product',
      error,
      meta: { product_id, service_provider_id }
    })
    return sendError(res, { message: 'Failed to get details for this product'}, 1)
  }
}

async function getProductByIdRouteForUser(req: Request, res: Response) {
  const rawData = decrypt(req.body)
  const product_id = parseFloat(rawData.product_id)
  const isUser = req.user_type === 'user'
  const user_id = isUser ? req.user_id : 0

  try {
    const service_provider_id = ((await Product.findByPk(product_id)) as any).service_provider_id
    let products = await getProducts({ product_id, user_id, service_provider_id })
    products = await normalizeProductDetailResponse(products)
    products = products.length > 0 ? products[0] : {} as any
    return sendSuccess(res, { data: products })
  } catch (error: any) {
    logger.error({
      message: 'Failed to get details for this product',
      error,
      meta: { product_id }
    })
    return sendError(res, { message: 'Failed to get details for this product'}, 1)
  }
}

async function addProductRoute(req: Request, res: Response) {
  const rawData = decrypt(req.body)
  const rules = {
    product_images: 'required',
    product_name: 'required',
    description: '',
    category_id: 'required',
    variant_list: 'required',
    unit_type: '',
    publish: 'required',
  }

  const errors = validate(rawData, rules, { "required": "required" }, {})
  if (errors)
      return sendError(res, errors)

  try {
    rawData.variant_list = await Promise.all(rawData.variant_list.map(async (variant) => {
      const size = parseFloat(variant.size.toString().split(' ')[0].trim())
      const measure_unit_shortname = variant.size.toString().split(' ')[1].replace('.', '').trim()
      return {
        ...variant,
        size,
        measure_unit_shortname,
      }
    }))

    await addProduct({
      service_provider_id: req.user_id || '',
      category_id: rawData.category_id,
      name: rawData.product_name.trim(),
      images: rawData.product_images,
      description: rawData.description.trim() || '',
      allergies_description: rawData.allergies_description?.trim() || null,
      is_active: Boolean(rawData?.publish || true),
      options: rawData.variant_list,
    })
  } catch(error: any) {
    logger.error({
      message: 'Failed to add new product',
      error,
      meta: { service_provider_id: req.user_id, category_id: rawData.category_id }
    })
    return sendError(res, { message: 'Failed to add new product. Please contact support if the error persists.' }, 0)
  }

  return sendSuccess(res, {})
}

async function editProductRoute(req: Request, res: Response) {
  const rawData = decrypt(req.body)

  let remove_variant_ids
  if(typeof rawData.remove_variant_ids === 'string') {
    remove_variant_ids = (rawData.remove_variant_ids || '').split(',')
  } else {
    // becaues it shuold be a list of ids eventually
    remove_variant_ids = rawData.remove_variant_ids
  }

  if (!rawData.variant_list) {
    rawData.variant_list = []
  }

  for await (const toRemoveId of remove_variant_ids) {
    rawData.variant_list.push({
      is_deleted: 1,
      subproduct_id: parseInt(toRemoveId),
    })
  } // for

  rawData.variant_list = await normalizeVariantProps(
    rawData.variant_list,
    rawData.product_id
  )

  try {
    await updateProduct({
      service_provider_id: req.user_id || '',
      name: rawData.product_name,
      category_id: rawData.category_id,
      images: rawData.product_images,
      options: rawData.variant_list,
      description: rawData.description,
      allergies_description: rawData.allergies_description?.trim() || '',
      is_active: (rawData.publish || 'Yes') === 'Yes' ? true : false,
      id: rawData.product_id,
    })
  } catch(error: any) {
    logger.error({
      message: 'Failed to update a product',
      error,
      meta: {
        service_provider_id: req.user_id,
        product_id: rawData.product_id,
        category_id: rawData.category_id
      }
    })
    return sendError(res, {
      message: 'Failed to update a product. Please contact support if error persists.'
    })
  }
}

async function deleteProductRoute(req: Request, res: Response) {
  const rawData = decrypt(req.body)
  try {
    await deleteProduct({
      product_id: rawData.product_id,
      service_provider_id: req.user_id || '',
    })
  } catch (error: any) {
    logger.error({
      message: 'Failed to delete a product',
      error,
      meta: { product_id: rawData.product_id, service_provider_id: req.user_id }
    })
    return sendError(res, { message: 'Failed to delete a product. Please contact support if error persists.' }, 0)
  }
  return sendSuccess(res, { message: 'Product has been deleted.' }, 1)
}

async function getMeasureUnitsRoute(req: Request, res: Response) {
  const units = await getMeasureUnits()
  return sendSuccess(res, { data: units })
}

async function getProductCategoriesRoute(req: Request, res: Response) {
  const categories = await getProductCategories()
  return sendSuccess(res, { data: categories })
}

interface BookmarkConfig {
  model: BookmarkModel
  idField: 'productId' | 'serviceId'
}

const BOOKMARK_CONFIGS: Record<BookmarkType, BookmarkConfig> = {
  product: {
    model: ProductBookmark,
    idField: 'productId'
  },
  service: {
    model: ServiceBookmark,
    idField: 'serviceId'
  }
}

const handleBookmark = async (
  type: BookmarkType,
  typeId: number,
  userId: number,
  status: 0 | 1
): Promise<{ success: boolean; message: string }> => {
  const config = BOOKMARK_CONFIGS[type]
  const whereClause = {
    [config.idField]: typeId,
    userId
  }

  if (status === 0) {
    const deleted = await config.model.destroy({ where: whereClause })
    return {
      success: true,
      message: deleted
        ? `${type} bookmark removed successfully`
        : `${type} bookmark not found`
    }
  }

  const [, created] = await config.model.findOrCreate({
    where: whereClause
  })

  return {
    success: true,
    message: created
      ? `${type} bookmarked successfully`
      : `${type} already bookmarked`
  }
}

async function addProductServiceBookmarkRoute(req: Request, res: Response) {
  const rawData = decrypt(req.body)
  const rules = {
    type_id: 'required',
    status: 'required|numeric|min:0|max:1',
    type: 'required|in:product,service'
  }

  const errors = validate(rawData, rules, {
    'required': 'required',
    'in': 'in',
    'numeric': 'numeric',
    'min': 'min',
    'max': 'max'
  }, {})
  if (errors){
    return sendError(res, errors)
  }

  try {
    const result = await handleBookmark(
      rawData.type as BookmarkType,
      rawData.type_id,
      Number(req.user_id),
      Number(rawData.status) as 0 | 1
    )
    return sendSuccess(res, { message: result.message })
  } catch (error: any) {
    logger.error({
      message: 'Failed to process product/service bookmark request',
      error,
      meta: {
        type_id: rawData.type_id,
        status: rawData.status,
        type: rawData.type,
        user_id: req.user_id
      }
    })
    return sendError(res, { message: 'Failed to process product/service bookmark request' }, 1)
  }
}

function parseSizeStr(sizeWithUnit: string) {
  const size = parseFloat(sizeWithUnit.split(' ')[0].trim())
  const shortname = sizeWithUnit.split(' ')[1].replace('.', '').trim()
  return { size, shortname }
}

async function normalizeVariantProps(variant: any[], product_id) {
  const result = await Promise.all(variant.map(opt => {
    const result: any = {
      ...opt,
      product_id,
      ...(opt.subproduct_id ? { id: parseInt(opt.subproduct_id) } : null),
      ...(opt.product_size_id ? { measure_unit_id: opt.product_size_id } : null),
    }

    if (opt.size) {
      const { size, shortname } = parseSizeStr(opt.size)
      result.size = size
      result.measure_unit_shortname = shortname
    }
    return result
  }))
  return result
}

/* Since IOS expects a specific garbage-like formatted data - gotta our response
in that specific way. One day we'll fix both front and backend.... */
async function normalizeProductDetailResponse(products): Promise<object[]> {
  const result = await new Promise(resolve => {
    const result = {}
    products.map((p: any) => {
      if (!(p.product_id in result)) {
        result[p.product_id] = { ...p}
      }
      if (!result[p.product_id].variant_list) {
        result[p.product_id].variant_list = []
      }

      result[p.product_id].image = [{ multiple_product_image: p.main_product_image }]

      result[p.product_id].variant_list.push({
        id: p.pv_id,
        subproduct_id: p.pv_id,
        product_id: p.pv_pid,
        price: p.price,
        quantity: p.quantity,
        remaining_quantity: p.quantity,
        size: `${p.size} ${p.shortname}`,
        product_size_id: p.measure_unit_id,
        measure_unit_id: p.measure_unit_id,
      })
    })
    resolve(Object.values(result))
  })
  return result as any
}

async function getBookmarksGeneric(req: Request, res: Response) {
  await getBookmarksRoute(req, res, req.query.type as BookmarkType)
}

async function getBookmarksRoute(req: Request, res: Response, type: BookmarkType | 'service_provider') {
  try {
    const page = Number(req.query.page)
    const limit = Global.PER_PAGE
    let data: any[] = []

    switch (type) {
      case 'product':
        data = await getBookmarkedProducts(Number(req.user_id), page, limit)
        break
      case 'service':
        data = await getBookmarkedServices(Number(req.user_id), page, limit)
        break
      case 'service_provider':
        data = await getBookmarkedServiceProviders(Number(req.user_id), page, limit)
        break
    }
    return res.status(200).send(data)
  } catch (error: any) {
    logger.error({
      message: 'Failed to get bookmarks',
      error,
      meta: {
        type: req.query.type,
        page: req.query.page,
        user_id: req.user_id
      }
    })
    return res.status(500).send({ error: 'Failed to get bookmarks' })
  }
}

export default router
