var express = require('express')
var common = require('../../../config/common')
var booth_owner_model = require('./booth_owner_model')
var router = require('express-promise-router')()
const { t } = require('localizify')
const { request } = require('express')

/**Api for add location  */
router.post('/add_location', function (req, res) {
  //console.log'add_location');
  common.decryption(req.body, function (request) {
    var rules = {
      name: 'required',
      address: 'required',
      latitude: 'required',
      longitude: 'required',
      description: 'required',
      //start_availability: 'required',
      //end_availability: 'required',
      workspace: 'required',
      //location_schedule: 'required',
      location_image: 'required',
      location_amenities: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      booth_owner_model.add_location(
        request,
        function (response, message, msgcode) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**Api for update location  */
router.post('/update_location', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      location_id: 'required',
      name: 'required',
      address: 'required',
      latitude: 'required',
      longitude: 'required',
      description: 'required',
      //start_availability: 'required',
      //end_availability: 'required',
      workspace: 'required',
      //location_schedule: 'required',
      location_image: 'required',
      location_amenities: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      booth_owner_model.update_location(
        request,
        function (response, message, msgcode) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**Api for get location listing */
router.post('/all_booth_location', function (req, res) {
  //console.log'all_booth_location');
  request.service_provider_id = req.user_id
  booth_owner_model.all_booth_location(
    request,
    req.user_id,
    function (response, message, msgcode) {
      common.send_response(req, res, msgcode, message, response)
    }
  )
})

/**Api for change location status */
router.post('/change_location', function (req, res) {
  //console.log'change_location');
  common.decryption(req.body, function (request) {
    var rules = {
      location_id: 'required',
      status: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      var data = {
        status: request.status,
      }
      common.update_data(
        'tbl_booth_renter_location',
        request.location_id,
        data,
        function (is_location) {
          if (is_location) {
            common.send_response(
              req,
              res,
              '1',
              'Location updated successfully',
              true
            )
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              false
            )
          }
        }
      )
    }
  })
})

/**
 * Api for get location detail
 */
router.post('/location_detail', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      location_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      booth_owner_model.location_detail(
        request,
        function (response, message, msgcode) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Api for change workspace status
 */
router.post('/change_workspace_status', function (req, res) {
  //console.log'change_workspace_status');
  common.decryption(req.body, function (request) {
    var rules = {
      workspace_id: 'required',
      status: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      var data = {
        status: request.status,
      }
      common.update_data(
        'tbl_workspace',
        request.workspace_id,
        data,
        function (is_location) {
          if (is_location) {
            common.send_response(
              req,
              res,
              '1',
              'Workspace status updated successfully',
              true
            )
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              false
            )
          }
        }
      )
    }
  })
})

/**Api for delete workspace image */
router.post('/remove_workspace_image', function (req, res) {
  //console.log'remove_workspace_image');
  common.decryption(req.body, function (request) {
    var rules = {
      image_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      var condition = ' id = ' + request.image_id + ''
      common.delete_data('tbl_image', condition, function (is_location) {
        if (is_location) {
          common.send_response(
            req,
            res,
            '1',
            'Image deleted successfully',
            true
          )
        } else {
          common.send_response(req, res, '0', t('restapi_globals_error'), false)
        }
      })
    }
  })
})

/**Delete workspace */
router.post('/delete_workspace', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      workspace_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      var data = {
        is_deleted: '1',
      }
      common.update_data(
        'tbl_workspace',
        request.workspace_id,
        data,
        function (is_location) {
          if (is_location) {
            var data = {
              is_deleted: '1',
            }
            common.update_data_condition(
              'tbl_workspace_amenties',
              "workspace_id = '" + request.workspace_id + "'",
              data,
              function (is_location) {
                var data = {
                  is_deleted: '1',
                }
                common.update_data_condition(
                  'tbl_workspace_schedule',
                  "workspace_id = '" + request.workspace_id + "'",
                  data,
                  function (is_location) {
                    var data = {
                      is_deleted: '1',
                    }
                    common.update_data_condition(
                      'tbl_image',
                      "type_id = '" +
                        request.workspace_id +
                        "' AND type = 'workspace_location_image'",
                      data,
                      function (is_location) {
                        common.update_data_condition(
                          'tbl_rent_booth',
                          "workspace_id = '" + request.workspace_id + "'",
                          data,
                          function (is_location) {
                            common.send_response(
                              req,
                              res,
                              '1',
                              'Workspace status deleted successfully',
                              true
                            )
                          }
                        )
                      }
                    )
                  }
                )
              }
            )
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              false
            )
          }
        }
      )
    }
  })
})

/**Delete location */
router.post('/delete_location', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      location_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      location_id: t('keyword_locationid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      var data = {
        is_deleted: '1',
      }
      common.update_data(
        'tbl_booth_renter_location',
        request.location_id,
        data,
        function (is_location) {
          if (is_location) {
            common.update_data_condition(
              'tbl_image',
              "type_id = '" +
                request.location_id +
                "' AND type = 'booth_location_image'",
              data,
              function (is_location) {
                common.update_data_condition(
                  'tbl_bчooth_amenties',
                  "booth_location_id = '" + request.location_id + "'",
                  data,
                  function (is_location) {
                    common.update_data_condition(
                      'tbl_booth_location_schedule',
                      "booth_location_id = '" + request.location_id + "'",
                      data,
                      function (is_location) {
                        common.update_data_condition(
                          'tbl_image',
                          "type_id IN (SELECT id FROM tbl_workspace WHERE booth_location_id = '" +
                            request.location_id +
                            "') AND type = 'workspace_location_image'",
                          data,
                          function (is_location) {
                            common.update_data_condition(
                              'tbl_workspace',
                              "booth_location_id = '" +
                                request.location_id +
                                "'",
                              data,
                              function (is_location) {
                                common.update_data_condition(
                                  'tbl_workspace_amenties',
                                  "booth_location_id = '" +
                                    request.location_id +
                                    "'",
                                  data,
                                  function (is_location) {
                                    common.update_data_condition(
                                      'tbl_workspace_schedule',
                                      "booth_location_id = '" +
                                        request.location_id +
                                        "'",
                                      data,
                                      function (is_location) {
                                        common.update_data_condition(
                                          'tbl_rent_booth',
                                          "booth_location_id = '" +
                                            request.location_id +
                                            "'",
                                          data,
                                          function (is_location) {
                                            common.send_response(
                                              req,
                                              res,
                                              '1',
                                              'Location status deleted successfully',
                                              true
                                            )
                                          }
                                        )
                                      }
                                    )
                                  }
                                )
                              }
                            )
                          }
                        )
                      }
                    )
                  }
                )
              }
            )
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              false
            )
          }
        }
      )
    }
  })
})

/**Edit workspace */
router.post('/edit_workspace', function (req, res) {
  //console.log'edit_workspace');
  common.decryption(req.body, function (request) {
    var rules = {
      workspace_id: 'required',
      booth_location_id: 'required',
      workspace_category_id: 'required',
      tag_name: 'required',
      description: 'required',
      start_availability: 'required',
      end_availability: 'required',
      hourly_rate: 'required',
      weekly_rate: 'required',
      monthly_rate: 'required',
      workspace_shedule: 'required',
      workspace_image: 'required',
      workspace_amenities: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      booth_owner_model.edit_workspace(
        request,
        function (response, message, msgcode) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**workspace detail */
router.post('/workspace_detail', function (req, res) {
  //console.log'workspace_detail');
  common.decryption(req.body, function (request) {
    var rules = {
      workspace_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      booth_owner_model.workspace_detail(
        request,
        function (response, message, msgcode) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * API for booking history
 */
router.post('/booking_history', function (req, res) {
  //console.log'booking_history');
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      page: 'Page',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.booth_owner_id = req.user_id
      booth_owner_model.booking_history(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * API for review list
 */
router.post('/review_list', function (req, res) {
  //console.log'review_list');
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      page: 'Page',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.booth_owner_id = req.user_id
      booth_owner_model.review_list(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Api for review detail screen
 */
router.post('/review_detail', function (req, res) {
  //console.log'review_detail');
  common.decryption(req.body, function (request) {
    var rules = {
      review_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      page: 'Page',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      booth_owner_model.review_detail(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * API for add review for service provider
 */
router.post('/addreview', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_rent_id: 'required',
      service_provider_id: 'required',
      ratting: 'required',
      review: '',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      review_id: 'Review Id',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.booth_owner_id = req.user_id
      booth_owner_model.rate_serviceproviderforrentbooth(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Api for review reply
 */
router.post('/review_reply', function (req, res) {
  //console.log'review_reply');
  common.decryption(req.body, function (request) {
    var rules = {
      review_id: 'required',
      reply: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      review_id: 'Review Id',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      booth_owner_model.review_reply(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Api for calender list
 */
router.post('/booth_schedule_calender', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      location_id: 'required',
      month: 'required',
      year: 'required',
      workspace_category_id: '',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      review_id: 'Review Id',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      booth_owner_model.booth_schedule_calender(
        request,
        req.user_id,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * API FOR accept booth renter
 */
router.post('/acceptboothrenter', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_rent_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.booth_owner_id = req.user_id
      booth_owner_model.acceptboothrenter(
        request,
        function (msgcode, messageobject, responsedata) {
          common.send_response(req, res, msgcode, messageobject, responsedata)
        }
      )
    }
  })
})

/**
 * API FOR reject booth renter
 */
router.post('/rejectboothrenter', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_rent_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.booth_owner_id = req.user_id
      booth_owner_model.rejectboothrenter(
        request,
        function (msgcode, messageobject, responsedata) {
          common.send_response(req, res, msgcode, messageobject, responsedata)
        }
      )
    }
  })
})

/**
 * API FOR cancel booth renter
 */
router.post('/cancelboothrenter', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_rent_id: 'required',
      cancel_id: '',
      cancel_reason: '',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.booth_owner_id = req.user_id
      booth_owner_model.cancelboothrenter(
        request,
        function (msgcode, messageobject, responsedata) {
          common.send_response(req, res, msgcode, messageobject, responsedata)
        }
      )
    }
  })
})

/**
 * API for report list
 */
router.post('/report', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      type: '',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.booth_owner_id = req.user_id
      booth_owner_model.report(
        request,
        req.user_id,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * API for get booked dates
 */
router.post('/getbookeddates', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_location_id: 'required',
      month: 'required',
      year: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.booth_owner_id = req.user_id
      booth_owner_model.get_boothbooked_dates(
        request,
        function (messagecode, messageobject, responseData) {
          common.send_response(
            req,
            res,
            messagecode,
            messageobject,
            responseData
          )
        }
      )
    }
  })
})

/**
 * API for get booth booking list
 */
router.post('/getboothbookedlist', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_location_id: 'required',
      date: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.booth_owner_id = req.user_id
      booth_owner_model.getboothbookinglist(request, function (bookinglist) {
        if (bookinglist != null) {
          common.send_response(
            req,
            res,
            '1',
            t('restapi_boothbookinglist_found'),
            bookinglist
          )
        } else {
          common.send_response(
            req,
            res,
            '0',
            t('restapi_boothbookinglist_notfound'),
            null
          )
        }
      })
    }
  })
})
module.exports = router
