var con = require('../../../config/database');
var global = require('../../../config/constant');
var asyncloop = require('node-async-loop');
var datetime = require('node-datetime');
const { t } = require('localizify');
var common = require('../../../config/common');
const stripe = require("../../../config/payment");
const { USER_TYPE } = require('../../../src/service')

var booth_owner = {
    /**Function for add location */
    add_location: function(request, callback) {
        var locations = {
            name: request.name,
            latitude: request.latitude,
            longitude: request.longitude,
            service_provider_id: request.service_provider_id,
            address: request.address,
            description: request.description,
            is_deleted: '0',
            status: 'Active',
            updatetime: datetime.create().format('Y-m-d H:M:S'),
            insertdate: datetime.create().format('Y-m-d H:M:S')
        }
        common.single_insert_data('tbl_booth_renter_location', locations, function(is_location) {
            if (is_location) {
                var location_id = is_location.insertId;
                booth_owner.booth_location_image(request, location_id, function(is_location_image) {
                    if (is_location_image) {
                        booth_owner.booth_location_amenties(request, location_id, function(is_location_amenties) {
                            if (is_location_amenties) {
                                /*booth_owner.booth_location_schedule(request, location_id, function(is_location_schedule) {
                                    if (is_location_schedule) {*/
                                        booth_owner.workspace(request, location_id, function(is_workspace) {
                                            if (is_workspace) {
                                                callback(true, 'Booth location added successfully', '1');
                                            } else {
                                                callback(null, 'workspace not found', '0');
                                            }
                                        });
                                    /*} else {
                                        callback(null, 'Booth location schedule not found', '0');
                                    }
                                })*/
                            } else {
                                callback(null, 'Booth location amenties not found', '0');
                            }
                        })
                    } else {
                        callback(null, 'Booth location image not found', '0');
                    }
                })
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**Function for update location */
    update_location:function(request,callback){
        common.delete_data('tbl_image',"type_id = '"+request.location_id+"' AND type = 'booth_location_image'", function(is_location) {
            common.delete_data('tbl_booth_amenties',"booth_location_id = '"+request.location_id+"'", function(is_location) {
                //common.delete_data('tbl_booth_location_schedule',"booth_location_id = '"+request.location_id+"'", function(is_location) {
                    common.delete_data('tbl_image', "type_id IN (SELECT id FROM tbl_workspace WHERE booth_location_id = '"+request.location_id+"') AND type = 'workspace_location_image'", function(is_location) {
                        common.delete_data('tbl_workspace_amenties',"booth_location_id = '"+request.location_id+"'" , function(is_location) {
                            common.delete_data('tbl_workspace_schedule',"booth_location_id = '"+request.location_id+"'" , function(is_location) {
                                var locationsdata = {
                                    name: request.name,
                                    latitude: request.latitude,
                                    longitude: request.longitude,
                                    service_provider_id: request.service_provider_id,
                                    address: request.address,
                                    description: request.description,
                                    start_availability: request.start_availability,
                                    end_availability: request.end_availability,
                                    updatetime: datetime.create().format('Y-m-d H:M:S')
                                }
                                common.update_data('tbl_booth_renter_location',request.location_id,locationsdata,function(is_location){
                                    if (is_location) {
                                        var location_id = request.location_id;
                                        booth_owner.booth_location_image(request, location_id, function(is_location_image) {
                                            if (is_location_image) {
                                                booth_owner.booth_location_amenties(request, location_id, function(is_location_amenties) {
                                                    if (is_location_amenties) {
                                                        /*booth_owner.booth_location_schedule(request, location_id, function(is_location_schedule) {
                                                            if (is_location_schedule) {*/
                                                                booth_owner.workspace(request, location_id, function(is_workspace) {
                                                                    if (is_workspace) {
                                                                        callback(true, 'Booth location added successfully', '1');
                                                                    } else {
                                                                        callback(null, 'workspace not found', '0');
                                                                    }
                                                                });
                                                            /*} else {
                                                                callback(null, 'Booth location schedule not found', '0');
                                                            }
                                                        });*/
                                                    } else {
                                                        callback(null, 'Booth location amenties not found', '0');
                                                    }
                                                });
                                            } else {
                                                callback(null, 'Booth location image not found', '0');
                                            }
                                        })
                                    } else {
                                        callback(null, t('restapi_globals_error'), '0');
                                    }
                                });
                            });
                        });
                    });
                //});
            });
        });
    },

    /**Function for add booth location */
    booth_location_image: function(request, location_id, callback) {
        if (request.location_image) {
            asyncloop(request.location_image, function(item, next) {
                if (item) {
                    var location_image = {
                        image_name: item.image_name,
                        type_id: location_id,
                        type: "booth_location_image",
                        is_deleted: '0',
                        insertdate: datetime.create().format('Y-m-d H:M:S')
                    }
                    common.single_insert_data('tbl_image', location_image, function(err, image_added) {
                        next();
                    })
                } else {
                    next()
                }
            }, function() {
                callback(true)
            })
        } else {
            callback(false);
        }
    },

    /**Function for add booth location amenties */
    booth_location_amenties: function(request, location_id, callback) {
        if (request.location_amenities) {
            asyncloop(request.location_amenities, function(item, next) {
                if (item) {
                    var amenities = {
                        amenities_id: item.amenities_id,
                        booth_location_id: location_id,
                        service_provider_id: request.service_provider_id,
                        is_deleted: '0',
                        insertdate: datetime.create().format('Y-m-d H:M:S')
                    }
                    common.single_insert_data('tbl_booth_amenties', amenities, function(err, image_added) {
                        next();
                    })
                } else {
                    next();
                }
            }, function() {
                callback(true)
            })
        } else {
            callback(false);
        }
    },

    /**Function for add booth location schedule */
    booth_location_schedule: function(request, location_id, callback) {
        if (request.location_schedule) {
            asyncloop(request.location_schedule, function(item, next) {
                if (item.slot != undefined && item.slot.length > 0) {
                    asyncloop(item.slot, function(item1, next1) {
                        if (item1) {
                            var booth_location_schedule = {
                                booth_location_id: location_id,
                                service_provider_id: request.service_provider_id,
                                day: item.day,
                                status: item.status,
                                open: item1.open,
                                close: item1.close,
                                is_deleted: '0',
                                insertdate: datetime.create().format('Y-m-d H:M:S')
                            }
                            common.single_insert_data('tbl_booth_location_schedule', booth_location_schedule, function(err, schedule_added) {
                                next1();
                            })
                        } else {
                            var booth_location_schedule = {
                                booth_location_id: location_id,
                                service_provider_id: request.service_provider_id,
                                day: item.day,
                                status: item.status,
                                open: '00:00',
                                close: '00:00',
                                is_deleted: '0',
                                insertdate: datetime.create().format('Y-m-d H:M:S')
                            }
                            common.single_insert_data('tbl_booth_location_schedule', booth_location_schedule, function(err, schedule_added) {
                                next1();
                            })
                        }
                    }, function() {
                        next();
                    })
                } else {
                    var booth_location_schedule = {
                        booth_location_id: location_id,
                        service_provider_id: request.service_provider_id,
                        day: item.day,
                        status: item.status,
                        open: '00:00',
                        close: '00:00',
                        is_deleted: '0',
                        insertdate: datetime.create().format('Y-m-d H:M:S')
                    }
                    common.single_insert_data('tbl_booth_location_schedule', booth_location_schedule, function(err, schedule_added) {
                        next();
                    });
                }
            }, function() {
                callback(true)
            })
        } else {
            callback(false);
        }
    },

    /**Function for add workspace of booth owner */
    workspace: function(request, location_id, callback) {
        if (request.workspace) {
            asyncloop(request.workspace, function(item, next) {
                if (item) {
                    if(item.workspace_id != undefined && parseInt(item.workspace_id) > 0){
                        var update_workspace = {
                            workspace_category_id: item.workspace_category_id,
                            quantity: item.quantity,
                            tag_name: item.tag_name,
                            description: item.description,
                            start_availability: item.start_availability,
                            end_availability: item.end_availability,
                            service_provider_id: request.service_provider_id,
                            booth_location_id: location_id,
                            hourly_rate: item.hourly_rate,
                            weekly_rate: item.weekly_rate,
                            monthly_rate: item.monthly_rate,
                            updatetime: datetime.create().format('Y-m-d H:M:S')
                        }
                        common.update_data("tbl_workspace",item.workspace_id,update_workspace,function(workspace_added){
                            item.service_provider_id = request.service_provider_id;
                            booth_owner.workspace_location_schedule(item, location_id, item.workspace_id, function(workspace_schedule) {
                                booth_owner.workspace_location_image(item, item.workspace_id, function(workspace_image) {
                                    booth_owner.workspace_amenties(item, location_id, item.workspace_id, function(workspaces_amenties) {
                                        next();
                                    });
                                });
                            });
                        })
                    } else {
                        var add_workspace = {
                            workspace_category_id: item.workspace_category_id,
                            quantity: item.quantity,
                            tag_name: item.tag_name,
                            description: item.description,
                            start_availability: item.start_availability,
                            end_availability: item.end_availability,
                            service_provider_id: request.service_provider_id,
                            booth_location_id: location_id,
                            hourly_rate: item.hourly_rate,
                            weekly_rate: item.weekly_rate,
                            monthly_rate: item.monthly_rate,
                            status: 'Active',
                            is_deleted: '0',
                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                            insertdate: datetime.create().format('Y-m-d H:M:S')
                        }
                        common.single_insert_data('tbl_workspace', add_workspace, function(workspace_added) {
                            var workspace_id = workspace_added.insertId;
                            item.service_provider_id = request.service_provider_id;
                            booth_owner.workspace_location_schedule(item, location_id, workspace_id, function(workspace_schedule) {
                                booth_owner.workspace_location_image(item, workspace_id, function(workspace_image) {
                                    booth_owner.workspace_amenties(item, location_id, workspace_id, function(workspaces_amenties) {
                                        next();
                                    });
                                });
                            });
                        });
                    }
                } else {
                    next();
                }
            }, function() {
                callback(true);
            })
        } else {
            callback(false);
        }
    },

    /**Function for add workspace shcedule */
    workspace_location_schedule: function(request, location_id, workspace_id, callback) {
        if (request.workspace_shedule) {
            asyncloop(request.workspace_shedule, function(item, next) {
                if (item.slot != undefined && item.slot.length > 0) {
                    asyncloop(item.slot, function(item1, next1) {
                        if (item1) {
                            var workspace_schedule = {
                                day: item.day,
                                status: item.status,
                                open: item1.open,
                                close: item1.close,
                                is_deleted: '0',
                                booth_location_id: location_id,
                                workspace_id: workspace_id,
                                service_provider_id: request.service_provider_id,
                                insertdate: datetime.create().format('Y-m-d H:M:S')
                            }
                            common.single_insert_data('tbl_workspace_schedule', workspace_schedule, function(err, schedule_added) {
                                next1();
                            })
                        } else {
                            var workspace_schedule = {
                                day: item.day,
                                status: item.status,
                                open: '00:00',
                                close: '00:00',
                                is_deleted: '0',
                                booth_location_id: location_id,
                                workspace_id: workspace_id,
                                service_provider_id: request.service_provider_id,
                                insertdate: datetime.create().format('Y-m-d H:M:S')
                            }
                            common.single_insert_data('tbl_workspace_schedule', workspace_schedule, function(err, schedule_added) {
                                next1();
                            })
                        }
                    }, function() {
                        next();
                    })
                } else {
                    var workspace_schedule = {
                        day: item.day,
                        status: item.status,
                        open: '00:00',
                        close: '00:00',
                        is_deleted: '0',
                        booth_location_id: location_id,
                        workspace_id: workspace_id,
                        service_provider_id: request.service_provider_id,
                        insertdate: datetime.create().format('Y-m-d H:M:S')
                    }
                    common.single_insert_data('tbl_workspace_schedule', workspace_schedule, function(err, schedule_added) {
                        next();
                    });
                }
            }, function() {
                callback(true)
            })
        } else {
            callback(false);
        }
    },

    /**Function for add workspace image */
    workspace_location_image: function(request, workspace_id, callback) {
        if (request.workspace_image) {
            asyncloop(request.workspace_image, function(item, next) {
                if (item) {
                    var location_image = {
                        image_name: item.image_name,
                        type_id: workspace_id,
                        type: "workspace_location_image",
                        is_deleted: '0',
                        insertdate: datetime.create().format('Y-m-d H:M:S')
                    }
                    common.single_insert_data('tbl_image', location_image, function(err, image_added) {
                        next();
                    })
                } else {
                    next()
                }
            }, function() {
                callback(true)
            })
        } else {
            callback(false);
        }
    },

    /**Function for add workspace amenties */
    workspace_amenties: function(request, location_id, workspace_id, callback) {
        if (request.workspace_amenities) {
            asyncloop(request.workspace_amenities, function(item, next) {
                if (item) {
                    var location_image = {
                        amenities_id: item.amenities_id,
                        booth_location_id: location_id,
                        workspace_id: workspace_id,
                        service_provider_id: request.service_provider_id,
                        is_deleted: '0',
                        insertdate: datetime.create().format('Y-m-d H:M:S')
                    }
                    common.single_insert_data('tbl_workspace_amenties', location_image, function(err, image_added) {
                        next();
                    })
                } else {
                    next();
                }
            }, function() {
                callback(true)
            })
        } else {
            callback(false);
        }
    },

    /**Function for get booth location listing */
    all_booth_location: function(request, service_provider_id, callback) {
        var sql = con.query("select *,date_format(`start_availability`, '%Y-%m-%d') as start_availability,date_format(`end_availability`, '%Y-%m-%d') as end_availability from tbl_booth_renter_location where service_provider_id = " + service_provider_id + " AND is_deleted = '0' ORDER BY id DESC", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    asyncloop(result, function(item, next) {
                        if (item) {
                            var sql = con.query("select w.*,wc.name as category_name,IFNULL((SELECT COUNT(id) FROM tbl_rent_booth WHERE service_provider_id = '"+service_provider_id+"' AND booth_location_id = '"+item.id+"' AND workspace_id = w.id AND is_deleted = '0' GROUP BY service_provider_id),0) as total_rented,date_format(`start_availability`, '%Y-%m-%d') as start_availability,date_format(`end_availability`, '%Y-%m-%d') as end_availability from tbl_workspace w LEFT JOIN tbl_master_workspace_category wc ON wc.id = w.workspace_category_id where w.booth_location_id = " + item.id + " AND w.service_provider_id = " + service_provider_id + " AND w.is_deleted = '0'", function(err1, result1) {
                                if (!err1 && result1[0] != "" && result1[0] != undefined) {
                                    item.workspace = result1;
                                } else {
                                    item.workspace = [];
                                }
                                var sql = con.query("select ma.*,CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',ma.image_name) as image_name from tbl_master_amenities ma LEFT JOIN tbl_booth_amenties ba ON ba.amenities_id = ma.id where ba.booth_location_id = " + item.id + " AND ba.is_deleted = '0' AND ma.is_deleted = '0' ORDER BY ID DESC", function(err2, result2) {
                                    if (!err2 && result2[0] != undefined) {
                                        item.amenities = result2;
                                    } else {
                                        item.amenities = [];
                                    }
                                    next()
                                })
                            })
                        } else {
                            next();
                        }
                    }, function() {
                        callback(result, t('restapi_location_found'), '1');
                    })
                } else {
                    callback(null, t('restapi_location_not_found'), '2');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**Function for location detail */
    location_detail: function(request, callback) {
        var sql = con.query("select * from tbl_booth_renter_location where id = " + request.location_id + " AND is_deleted = '0'", function(error, results) {
            if (!error) {
                if (results[0] != undefined && results[0] != "") {
                    con.query("SELECT timg.id as location_image_id,timg.*,CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',timg.image_name) as image_name FROM tbl_image as timg WHERE timg.type_id = '"+request.location_id+"' AND timg.type = 'booth_location_image' AND timg.is_deleted = '0'",function(error1,locationimages,fields){
                        results[0].location_images = (!error1 && locationimages[0] != undefined) ? locationimages : [];
                        con.query("SELECT tba.id as location_amenties_id,tba.*,tma.name,CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',tma.image_name) as image_name FROM tbl_booth_amenties as tba LEFT JOIN tbl_master_amenities as tma ON tba.amenities_id = tma.id WHERE tba.booth_location_id = '"+request.location_id+"' AND tba.is_deleted = '0'",function(error2,locationamenties,fields){
                            results[0].location_amenties = (!error2 && locationamenties[0] != undefined) ? locationamenties : [];
                            booth_owner.get_location_schedule(request.location_id,function(locationschedule){
                                results[0].location_schedules = locationschedule;
                                var sql = con.query("select w.*,w.id as workspace_id,date_format(`start_availability`, '%Y-%m-%d') as start_availability,date_format(`end_availability`, '%Y-%m-%d') as end_availability,wc.name as category_name from tbl_workspace w LEFT JOIN tbl_master_workspace_category wc ON wc.id = w.workspace_category_id where w.booth_location_id  = " + request.location_id + " AND w.service_provider_id = " + request.service_provider_id + " AND w.is_deleted = '0'", function(err, result) {
                                    if (!err) {
                                        if (result[0] != "" && result[0] != undefined) {
                                            asyncloop(result, function(item, next) {
                                                if (item) {
                                                    booth_owner.get_workspace_amenties(item.workspace_id, function(work_amenities) {
                                                        item.workspace_amenities = work_amenities;
                                                        booth_owner.get_workspace_location_image(item.workspace_id, function(work_image) {
                                                            item.workspace_image = work_image;
                                                            booth_owner.get_workspace_schedule(item.workspace_id, function(work_schedule) {
                                                                item.workspace_shedule = work_schedule;
                                                                next()
                                                            });
                                                        });
                                                    });
                                                } else {
                                                    next();
                                                }
                                            }, function() {
                                                results[0].workspace = result;
                                                callback(results[0], t('restapi_location_detail_found'), '1');
                                            });
                                        } else {
                                            results[0].workspace = [];
                                            callback(results[0], t('restapi_location_detail_found'), '1');
                                        }
                                    } else {
                                        callback(null, t('restapi_globals_error'), '0');
                                    }
                                });
                            });
                        });
                    });
                } else {
                    callback(null, t('restapi_location_detail_not_found'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })

    },

    /**Function for get location schedule */
    get_location_schedule: function(location_id, callback) {
        var sql = con.query("select booth_location_id,service_provider_id,day,status from tbl_booth_location_schedule where booth_location_id = " + location_id + " AND is_deleted = '0' group by day", function(err1, result1) {
            if (!err1 && result1[0] != undefined && result1[0] != "") {
                asyncloop(result1, function(item, next) {
                    if (item) {
                        var sql = con.query("select id,open,close from tbl_booth_location_schedule where booth_location_id = " + location_id + " AND is_deleted = '0' AND day='" + item.day + "'", function(err2, result2) {
                            if (!err2 && result2[0] != undefined) {
                                item.slot = result2;
                            } else {
                                item.slot = [];
                            }
                            next();
                        })
                    } else {
                        next();
                    }
                }, function() {
                    callback(result1);
                })
            } else {
                callback([]);
            }
        })
    },

    /**Function for get workspace amenties */
    get_workspace_amenties: function(workspace_id, callback) {
        var sql = con.query("select ma.id as amenities_id,ma.name,CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',ma.image_name) as image_name from tbl_workspace_amenties wa left join tbl_master_amenities ma on ma.id = wa.amenities_id where wa.workspace_id = " + workspace_id + " AND wa.is_deleted = '0'", function(err1, result1) {
            if (!err1 && result1[0] != undefined && result1[0] != "") {
                callback(result1);
            } else {
                callback([]);
            }
        })
    },

    /**Function for get workspace image */
    get_workspace_location_image: function(workspace_id, callback) {
        var sql = con.query("select id as image_id,CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',image_name) as image_name from tbl_image where type_id = " + workspace_id + " AND type = 'workspace_location_image' AND is_deleted = '0'", function(err1, result1) {
            if (!err1 && result1[0] != undefined && result1[0] != "") {
                callback(result1);
            } else {
                callback([]);
            }
        })
    },

    /**Function for get workspace schedule */
    get_workspace_schedule: function(workspace_id, callback) {
        var sql = con.query("select day,booth_location_id,workspace_id,service_provider_id,status from tbl_workspace_schedule where workspace_id = " + workspace_id + " AND is_deleted = '0' group by day", function(err1, result1) {
            if (!err1 && result1[0] != undefined && result1[0] != "") {
                asyncloop(result1, function(item, next) {
                    if (item) {
                        var sql = con.query("select open,close from tbl_workspace_schedule where workspace_id = " + workspace_id + " AND is_deleted = '0' AND day='" + item.day + "'", function(err2, result2) {
                            if (!err2 && result2[0] != undefined) {
                                item.slot = result2;
                            } else {
                                item.slot = [];
                            }
                            next();
                        })
                    } else {
                        next();
                    }
                }, function() {
                    callback(result1);
                })
            } else {
                callback([]);
            }
        })
    },

    /**Function for Edit workspace  */
    edit_workspace: function(request, callback) {
        var condition = " workspace_id = " + request.workspace_id;
        common.delete_data('tbl_workspace_schedule', condition, function(is_schedule_delete) {
            var condition1 = " type_id = " + request.workspace_id + " AND type = 'workspace_location_image'";
            common.delete_data('tbl_image', condition1, function(is_schedule_delete) {
                common.delete_data('tbl_workspace_amenties', condition, function(is_amenities_delete) {
                    var edit_workspace = {
                        workspace_category_id: request.workspace_category_id,
                        quantity: request.quantity,
                        tag_name: request.tag_name,
                        description: request.description,
                        start_availability: request.start_availability,
                        end_availability: request.end_availability,
                        service_provider_id: request.service_provider_id,
                        booth_location_id: request.booth_location_id,
                        hourly_rate: request.hourly_rate,
                        weekly_rate: request.weekly_rate,
                        monthly_rate: request.monthly_rate,
                        is_deleted: '0',
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                    }
                    common.update_data('tbl_workspace', request.workspace_id, edit_workspace, function(is_workspace_update) {
                        if (is_workspace_update) {
                            booth_owner.workspace_location_schedule(request, request.booth_location_id, request.workspace_id, function(workspace_schedule) {
                                booth_owner.workspace_location_image(request, request.workspace_id, function(workspace_image) {
                                    booth_owner.workspace_amenties(request, request.booth_location_id, request.workspace_id, function(workspaces_amenties) {
                                        callback(true, 'Workspace updated successfully', '1')
                                    })
                                })
                            })
                        } else {
                            callback(null, t('restapi_globals_error'), '0');
                        }
                    })
                })
            })
        })
    },

    /**Function for get workspace detail */
    workspace_detail: function(request, callback) {
        var sql = con.query("select w.*,w.id as workspace_id,date_format(`start_availability`, '%Y-%m-%d') as start_availability,date_format(`end_availability`, '%Y-%m-%d') as end_availability,wc.name as category_name from tbl_workspace w LEFT JOIN tbl_master_workspace_category wc ON wc.id = w.workspace_category_id where w.id  = " + request.workspace_id + " AND w.service_provider_id = " + request.service_provider_id + " AND w.is_deleted = '0'", function(err, result) {
            if (!err) {
                //console.logthis.sql);
                if (result[0] != "" && result[0] != undefined) {
                    asyncloop(result, function(item, next) {
                        if (item) {
                            booth_owner.get_workspace_amenties(item.workspace_id, function(work_amenities) {
                                item.workspace_amenities = work_amenities;
                                booth_owner.get_workspace_location_image(item.workspace_id, function(work_image) {
                                    item.workspace_image = work_image;
                                    booth_owner.get_workspace_schedule(item.workspace_id, function(work_schedule) {
                                        item.workspace_shedule = work_schedule;
                                        next()
                                    })
                                })
                            })
                        } else {
                            next();
                        }
                    }, function() {
                        callback(result[0], 'Workspace detail found successfully', '1');

                    })
                } else {
                    callback(null, t('restapi_location_detail_not_found'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * Function for accept booth renter
     * @param {request} as request
     * @param {function} as callback
     */
    acceptboothrenter:function(request,callback){
        require("../service_provider/service_provider_model").booth_detail(request,function(messagecode,messageobject,responsedata){
            if(messagecode == '1' && responsedata != null){
                if(responsedata.status == 'Cancelled'){
                    callback("2",t('restapi_boothrentalreadyCancelled_error'),null);
                } else if(responsedata.status == 'Completed'){
                    callback("2",t('restapi_boothrentalreadyCompleted_error'),null);
                } else if(responsedata.status == 'Processing'){
                    callback("2",t('restapi_boothrentalreadyProcessing_error'),null);
                } else if(responsedata.status == 'Rejected'){
                    callback("2",t('restapi_boothrentalreadyRejected_error'),null);
                } else if(responsedata.status == 'Accepted'){
                    callback("2",t('restapi_boothrentalreadyAccepted_error'),null);
                } else {
                    var updparams = {
                        status : 'Accepted',
                        reply_datetime: datetime.create().format('Y-m-d H:M:S'),
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                    }
                    common.update_data('tbl_rent_booth',request.booth_rent_id,updparams,function(is_updated){
                        if(is_updated){
                            require("../service_provider/service_provider_model").booth_detail(request,function(messagecode,messageobject,responsedata){
                                if(messagecode == '1' && responsedata != null){
                                    var message = {
                                        sender_id: request.booth_owner_id,
                                        sender_type: USER_TYPE.SERVICE_PROVIDER,
                                        receiver_id: responsedata.service_provider_id,
                                        type: 'service_provider',
                                        receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                        message: "Your booth rent request has been accepted",
                                        title: "Accept Booth Rent",
                                        isaction_id:request.booth_rent_id,
                                        tag: 'acceptboothrentbooking',
                                    }
                                    require('../../../config/common').prepare_notification(responsedata.service_provider_id, message, function(notification) {
                                        callback('1', t('restapi_boothrentAccepted_success'), responsedata)
                                    });
                                } else {
                                    callback(messagecode,messageobject,responsedata);
                                }
                            });
                        } else {
                            callback('0', t('restapi_globals_error'),null);
                        }
                    });
                }
            } else {
                callback(messagecode,messageobject,responsedata);
            }
        });
    },

    /**
     * Function for reject booth renter
     * @param {request} as request
     * @param {function} as callback
     */
    rejectboothrenter:function(request,callback){
        require("../service_provider/service_provider_model").booth_detail(request,function(messagecode,messageobject,responsedata){
            if(messagecode == '1' && responsedata != null){
                if(responsedata.status == 'Cancelled'){
                    callback("2",t('restapi_boothrentalreadyCancelled_error'),null);
                } else if(responsedata.status == 'Completed'){
                    callback("2",t('restapi_boothrentalreadyCompleted_error'),null);
                } else if(responsedata.status == 'Processing'){
                    callback("2",t('restapi_boothrentalreadyProcessing_error'),null);
                } else if(responsedata.status == 'Rejected'){
                    callback("2",t('restapi_boothrentalreadyRejected_error'),null);
                } else if(responsedata.status == 'Accepted'){
                    callback("2",t('restapi_boothrentalreadyAccepted_error'),null);
                } else {
                    if(responsedata.payment_mode == 'Card' && responsedata.payment_intent_id != ""){
                        stripe.createChargeRefund(responsedata.payment_intent_id,responsedata.total_amount,function(rescode,resmessage,responsedata){
                            var updparams = {
                                status : 'Rejected',
                                reply_datetime: datetime.create().format('Y-m-d H:M:S'),
                                updatetime: datetime.create().format('Y-m-d H:M:S'),
                            }
                            common.update_data('tbl_rent_booth',request.booth_rent_id,updparams,function(is_updated){
                                if(is_updated){
                                    require("../service_provider/service_provider_model").booth_detail(request,function(messagecode,messageobject,responsedata){
                                        if(messagecode == '1' && responsedata != null){
                                            var message = {
                                                sender_id: request.booth_owner_id,
                                                sender_type: USER_TYPE.SERVICE_PROVIDER,
                                                receiver_id: responsedata.service_provider_id,
                                                type: 'service_provider',
                                                receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                                message: "Your booth rent request has been rejected",
                                                title: "Reject Booth Rent",
                                                isaction_id:request.booth_rent_id,
                                                tag: 'rejectboothrentbooking',
                                            }
                                            require('../../../config/common').prepare_notification(responsedata.service_provider_id, message, function(notification) {
                                                callback('1', t('restapi_boothrentRejected_success'), responsedata)
                                            });
                                        } else {
                                            callback(messagecode,messageobject,responsedata);
                                        }
                                    });
                                } else {
                                    callback('0', t('restapi_globals_error'),null);
                                }
                            });
                        });
                    } else {
                        var updparams = {
                            status : 'Rejected',
                            reply_datetime: datetime.create().format('Y-m-d H:M:S'),
                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                        }
                        common.update_data('tbl_rent_booth',request.booth_rent_id,updparams,function(is_updated){
                            if(is_updated){
                                require("../service_provider/service_provider_model").booth_detail(request,function(messagecode,messageobject,responsedata){
                                    if(messagecode == '1' && responsedata != null){
                                        var message = {
                                            sender_id: request.booth_owner_id,
                                            sender_type: USER_TYPE.SERVICE_PROVIDER,
                                            receiver_id: responsedata.service_provider_id,
                                            type: 'service_provider',
                                            receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                            message: "Your booth rent request has been rejected",
                                            title: "Reject Booth Rent",
                                            isaction_id:request.booth_rent_id,
                                            tag: 'rejectboothrentbooking',
                                        }
                                        require('../../../config/common').prepare_notification(responsedata.service_provider_id, message, function(notification) {
                                            callback('1', t('restapi_boothrentRejected_success'), responsedata)
                                        });
                                    } else {
                                        callback(messagecode,messageobject,responsedata);
                                    }
                                });
                            } else {
                                callback('0', t('restapi_globals_error'),null);
                            }
                        });
                    }
                }
            } else {
                callback(messagecode,messageobject,responsedata);
            }
        });
    },

    /**
     * Function for cancel booth renter
     * @param {request} as request
     * @param {function} as callback
     */
    cancelboothrenter:function(request,callback){
        require("../service_provider/service_provider_model").booth_detail(request,function(messagecode,messageobject,responsedata){
            if(messagecode == '1' && responsedata != null){
                if(responsedata.status == 'Cancelled'){
                    callback("2",t('restapi_boothrentalreadyCancelled_error'),null);
                } else if(responsedata.status == 'Completed'){
                    callback("2",t('restapi_boothrentalreadyCompleted_error'),null);
                } else if(responsedata.status == 'Processing'){
                    callback("2",t('restapi_boothrentalreadyProcessing_error'),null);
                } else if(responsedata.status == 'Rejected'){
                    callback("2",t('restapi_boothrentalreadyRejected_error'),null);
                } else if(responsedata.status == 'Waiting'){
                    callback("2",t('restapi_boothrentalreadyWaiting_error'),null);
                } else {
                    if(responsedata.payment_mode == 'Card' && responsedata.payment_intent_id != ""){
                        stripe.createChargeRefund(responsedata.payment_intent_id,responsedata.total_amount,function(rescode,resmessage,responsedata){
                            var updparams = {
                                status : 'Cancelled',
                                cancel_by:'booth_host',
                                cancel_datetime: datetime.create().format('Y-m-d H:M:S'),
                                updatetime: datetime.create().format('Y-m-d H:M:S'),
                            }
                            if(request.cancel_id != undefined && request.cancel_id != ""){
                                updparams.cancel_id = request.cancel_id;
                            }
                            if(request.cancel_reason != undefined && request.cancel_reason != ""){
                                updparams.cancel_reason = request.cancel_reason;
                            }
                            common.update_data('tbl_rent_booth',request.booth_rent_id,updparams,function(is_updated){
                                if(is_updated){
                                    require("../service_provider/service_provider_model").booth_detail(request,function(messagecode,messageobject,responsedata){
                                        if(messagecode == '1' && responsedata != null){
                                            var message = {
                                                sender_id: request.booth_owner_id,
                                                sender_type: USER_TYPE.SERVICE_PROVIDER,
                                                receiver_id: responsedata.service_provider_id,
                                                type: 'service_provider',
                                                receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                                message: "Your booth rent booking has been cancelled by booth renter",
                                                title: "Cancel Booth Rent",
                                                isaction_id:request.booth_rent_id,
                                                tag: 'cancelboothrentbooking',
                                            }
                                            require('../../../config/common').prepare_notification(responsedata.service_provider_id, message, function(notification) {
                                                callback('1', t('restapi_boothrentCancelled_success'), responsedata)
                                            });
                                        } else {
                                            callback(messagecode,messageobject,responsedata);
                                        }
                                    });
                                } else {
                                    callback('0', t('restapi_globals_error'),null);
                                }
                            });
                        });
                    } else {
                        var updparams = {
                            status : 'Cancelled',
                            cancel_by:'booth_host',
                            cancel_datetime: datetime.create().format('Y-m-d H:M:S'),
                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                        }
                        if(request.cancel_id != undefined && request.cancel_id != ""){
                            updparams.cancel_id = request.cancel_id;
                        }
                        if(request.cancel_reason != undefined && request.cancel_reason != ""){
                            updparams.cancel_reason = request.cancel_reason;
                        }
                        common.update_data('tbl_rent_booth',request.booth_rent_id,updparams,function(is_updated){
                            if(is_updated){
                                require("../service_provider/service_provider_model").booth_detail(request,function(messagecode,messageobject,responsedata){
                                    if(messagecode == '1' && responsedata != null){
                                        var message = {
                                            sender_id: request.booth_owner_id,
                                            sender_type: USER_TYPE.SERVICE_PROVIDER,
                                            receiver_id: responsedata.service_provider_id,
                                            type: 'service_provider',
                                            receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                            message: "Your booth rent booking has been cancelled by booth renter",
                                            title: "Cancel Booth Rent",
                                            isaction_id:request.booth_rent_id,
                                            tag: 'cancelboothrentbooking',
                                        }
                                        require('../../../config/common').prepare_notification(responsedata.service_provider_id, message, function(notification) {
                                            callback('1', t('restapi_boothrentCancelled_success'), responsedata)
                                        });
                                    } else {
                                        callback(messagecode,messageobject,responsedata);
                                    }
                                });
                            } else {
                                callback('0', t('restapi_globals_error'),null);
                            }
                        });
                    }
                }
            } else {
                callback(messagecode,messageobject,responsedata);
            }
        });
    },

    /**
     * This function is used for booth booking history
     */
    booking_history: function(request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;

        var condition = "trb.is_deleted = '0' AND trb.booth_owner_id = " + request.booth_owner_id + "";
        var condition1 = " AND trb.status IN ('Waiting','Accepted','Processing')";
        if (request.date) {
            condition1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
        } else {
            //condition1 += " AND trb.date >= CURDATE() ";
        }
        if (request.booth_location_id) {
            condition += " AND trb.booth_location_id = " + request.booth_location_id + "";
        }
        var sort_by = ' order by trb.id DESC';
        if(request.sort_by != undefined && request.sort_by == 'Oldest'){
            sort_by = ' order by trb.id ASC';
        }
        con.query("select trb.*,date_format(trb.date,'%Y-%m-%d') as date,trb.id as booth_rent_id,tbr.address,tbr.name as booth_location_name,tbr.latitude,tbr.longitude,date_format(trb.start_slot_time,'%H:%i:%s') as start_slot_time,date_format(trb.end_slot_time,'%H:%i:%s') as end_slot_time,trb.status as booking_status,tsp.first_name,tsp.last_name,tsp.email,CONCAT(tsp.country_code,tsp.phone) as phonenumber,CONCAT('"+global.S3_BUCKET_ROOT + global.SP_IMAGE+"',tsp.profile_image) as profile_image from tbl_rent_booth trb LEFT JOIN tbl_booth_renter_location tbr ON tbr.id = trb.booth_location_id LEFT JOIN tbl_service_provider as tsp ON trb.service_provider_id = tsp.id where  " + condition + condition1 + sort_by +" LIMIT " + limit + ", " + per_page + "", function(err, upcomming) {
            if (!err) {
                var condition1 = " AND trb.status IN ('Completed','Rejected','Cancelled')";
                if (request.date) {
                    condition1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
                } else {
                    //condition1 += " AND trb.date < CURDATE()";
                }
                con.query("select trb.*,date_format(trb.date,'%Y-%m-%d') as date,trb.id as booth_rent_id,tbr.address,tbr.name as booth_location_name,tbr.latitude,tbr.longitude,date_format(trb.start_slot_time,'%H:%i:%s') as start_slot_time,date_format(trb.end_slot_time,'%H:%i:%s') as end_slot_time,trb.status as booking_status,tsp.first_name,tsp.last_name,tsp.email,CONCAT(tsp.country_code,tsp.phone) as phonenumber,CONCAT('"+global.S3_BUCKET_ROOT + global.SP_IMAGE+"',tsp.profile_image) as profile_image from tbl_rent_booth trb LEFT JOIN tbl_booth_renter_location tbr ON tbr.id = trb.booth_location_id LEFT JOIN tbl_service_provider as tsp ON trb.service_provider_id = tsp.id where  " + condition + condition1 + " order by trb.id DESC LIMIT " + limit + ", " + per_page + "", function(err1, past) {
                    if (!err1) {
                        if (upcomming[0] != "" && upcomming[0] != undefined) {
                            var upcomming_booking = upcomming;
                        } else {
                            var upcomming_booking = [];
                        }
                        if (past[0] != "" && past[0] != undefined) {
                            var past_booking = past;
                        } else {
                            var past_booking = [];
                        }
                        if ((upcomming[0] != "" && upcomming[0] != undefined) || (past[0] != "" && past[0] != undefined)) {
                            callback('1', "Booth history found successfully", { upcomming: upcomming_booking, past: past_booking });
                        } else {
                            callback('2', 'Booth history not found', { upcomming: [], past: [] });
                        }
                    } else {
                        callback('0', t('restapi_globals_error'), false);
                    }
                });
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        });
    },

    /**
     * Function for review list
     */
    review_list: function(request, callback) {
        con.query("SELECT IFNULL(AVG(ratting), 0) as avg_ratting,count(id) as total_reviews FROM tbl_booth_review WHERE booth_owner_id = '"+request.booth_owner_id+"' AND receiver_type = 'booth_host' AND ratting > 0",function(err1,avgratedata,fields){
            var avgratting = (!err1 && avgratedata.length > 0) ? avgratedata[0].avg_ratting : 0.00;
            var totalreviews = (!err1 && avgratedata.length > 0) ? avgratedata[0].total_reviews : 0;
            var page = request.page - 1;
            var limit = page * global.PER_PAGE;
            var per_page = global.PER_PAGE;
            var condition = "br.booth_owner_id = " + request.booth_owner_id + " AND br.receiver_type = 'booth_host'";
            if (request.word) {
                condition += " AND (s.first_name LIKE '" + request.word + "%' OR s.last_name LIKE '" + request.word + "%')";
            }
            if (request.ratting) {
                condition += " AND br.ratting = " + request.ratting + " ";
            }
            var order_by = " ORDER BY ";
            if (request.sorting) {
                order_by += "br.id " + request.sorting + " ";
            } else {
                order_by += "br.id DESC ";
            }
            var having = " having ";
            if (request.date) {
                having += " '" + request.date + "' = date_format(insertdate,'%Y-%m-%d') ";
            } else {
                having += " '1' = '1' ";
            }
            con.query("select br.id as review_id,br.ratting,br.review,IFNULL(br.review_reply,'') as review_reply,br.service_provider_id,CONCAT('" + global.S3_BUCKET_ROOT + global.SP_IMAGE + "','',s.profile_image) as profile_image,s.first_name,s.last_name,date_format(br.insertdate,'%Y-%m-%d %H:%i:%s') as insert_date,IFNULL((SELECT GROUP_CONCAT(name SEPARATOR', ') as names from tbl_review_list WHERE FIND_IN_SET(id,br.review_id)),'') as selected_reviews from tbl_booth_review br LEFT JOIN tbl_service_provider s ON s.id = br.service_provider_id where " + condition + " group by br.booth_rent_id " + having + order_by + " LIMIT " + limit + ", " + per_page + "", function(err, result) {
                if (!err) {
                    if (result.length > 0) {
                        callback('1', 'Review list successfully', {avg_ratting:avgratting,total_reviews:totalreviews,review_list:result});
                    } else {
                        callback('2', 'Review list not found', null);
                    }
                } else {
                    callback('0', t('restapi_globals_error'), null);
                }
            });
        });
    },

    /**
     * Function for reply review
     */
    review_reply: function(request, callback) {
        var update_param = {
            review_reply: request.reply,
            updatetime: datetime.create().format('Y-m-d H:M:S')
        }
        common.update_data('tbl_booth_review', request.review_id, update_param, function(is_updated) {
            if (is_updated) {
                callback('1', "You have replied successfully", null);
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for review detail api
     */
    review_detail: function(request, callback) {
        var sql = con.query("select br.id as review_id,br.ratting,br.review,IFNULL(br.review_reply,'') as review_reply,br.service_provider_id,CONCAT('" + global.S3_BUCKET_ROOT + global.SP_IMAGE + "','',s.profile_image) as profile_image,s.first_name,s.last_name,date_format(br.insertdate,'%Y-%m-%d %H:%i:%s') as insert_date,br.booth_rent_id from tbl_booth_review br LEFT JOIN tbl_service_provider s ON s.id = br.service_provider_id where br.id = " + request.review_id + "", function(err, result) {
            if (!err) {
                if (result.length > 0) {
                    callback('1', "Review detail found successfully", result[0]);
                } else {
                    callback('0', "Review detail not found", null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for get booth schedule calender
     */
    booth_schedule_calender: function(request, service_provider_id, callback) {
        var wherecon = "w.booth_location_id  = " + request.location_id + " AND w.service_provider_id = " + request.service_provider_id + " AND w.is_deleted = '0'";
        if(request.workspace_category_id != undefined && request.workspace_category_id != ""){
            wherecon += " AND w.workspace_category_id = '"+request.workspace_category_id+"'";
        }
        con.query("select w.*,w.id as workspace_id,date_format(`start_availability`, '%Y-%m-%d') as start_availability,date_format(`end_availability`, '%Y-%m-%d') as end_availability,wc.name as category_name from tbl_workspace w LEFT JOIN tbl_master_workspace_category wc ON wc.id = w.workspace_category_id where "+wherecon+" GROUP BY w.id ORDER BY w.id DESC",function(err,result){
            if(!err && result.length > 0){
                var rows = 0;
                asyncloop(result, function(item, next) {
                    request.workspace_id = item.workspace_id;
                    booth_owner.rent_workspace_list(request, request.location_id, function(customer_list) {
                        result[rows].bookings = customer_list;
                        rows++;
                        next();
                    });
                },function(){
                    callback('1', "Booth schedule found successfully", result);
                });
            } else {
                callback('0', "Please add location to check booth schedule", null);
            }
        });
        /*var wherecon = "is_deleted = '0' AND service_provider_id = " + service_provider_id + " AND status = 'Active'"
        var sql = con.query("select id location_id,address,description,name from tbl_booth_renter_location where " + wherecon + "", function(err, location_name) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else if (location_name.length < 0 || location_name[0].name == undefined) {
                callback('0', "Please add location to check booth schedule", null);
            } else {
                var final_array = {};
                final_array.booth_location = location_name;
                var location_id = location_name[0].location_id;
                if (request.location_id) {
                    location_id = request.location_id;
                }
                booth_owner.workspace_category(location_id, request, function(category) {
                    final_array.workspace_category = category;
                    booth_owner.rent_workspace_list(request, location_id, function(customer_list) {
                        final_array.customer_list = customer_list;
                        if (Object.keys(final_array).length === 0) {
                            callback('0', "Booth schedule list not found", null);
                        } else {
                            callback('1', "Booth schedule found successfully", final_array);
                        }
                    })
                })
            }
        })*/
    },

    /**
     * Function for get workspace category
     */
    workspace_category: function(location_id, request, callback) {
        var wherecon = " w.booth_location_id = " + location_id + " AND w.is_deleted = '0'";
        // if (request.workspace_category_id) {
        //     wherecon += " AND w.workspace_category_id = " + request.workspace_category_id + "";
        // }
        var sql = con.query("select w.id as workspace_id,w.tag_name,w.workspace_category_id,wc.name as category_name  from tbl_workspace w LEFT JOIN tbl_master_workspace_category wc ON wc.id = w.workspace_category_id where  " + wherecon + " ", function(err1, result1) {
            //console.logerr1);
            if (!err1 && result1.length > 0) {
                callback(result1)
            } else {
                callback([])
            }
        })
    },

    /**
     * Function for get workspace list
     */
    rent_workspace_list: function(request, location_id, callback) {
        var wherecon = "rb.booth_location_id = " + location_id + " AND rb.is_deleted = '0' ";
        if (request.month != undefined && request.year != undefined) {
            wherecon += " AND date_format(rb.date,'%Y-%m') = '"+request.year+"-"+request.month+"'";
        }
        if (request.workspace_category_id != undefined) {
            wherecon += " AND w.workspace_category_id = " + request.workspace_category_id + ""
        }
        if(request.workspace_id != undefined && request.workspace_id != ""){
            wherecon += " AND rb.workspace_id = " + request.workspace_id + ""
        }
        var sql = con.query("select rb.*,w.tag_name,wc.name as category_name,CONCAT('" + global.S3_BUCKET_ROOT + global.SP_IMAGE + "','',sp.profile_image) as profile_image,sp.first_name,sp.last_name from tbl_rent_booth rb LEFT JOIN tbl_workspace w ON w.id = rb.workspace_id LEFT JOIN tbl_master_workspace_category wc ON wc.id = w.workspace_category_id LEFT JOIN tbl_service_provider sp ON sp.id = rb.service_provider_id where " + wherecon + " ORDER BY rb.date ASC", function(err, result) {
            if (!err && result.length > 0) {
                callback(result)
            } else {
                callback([])
            }
        })
    },

    /**
     * Function for get Booth Renter Retention Ratio
     */
    getBoothRenterRetentionRatio(booth_owner_id,request,callback){
        var wherecon = "booth_owner_id = '"+booth_owner_id+"'";
        var wherecon1 = "booth_owner_id = '"+booth_owner_id+"'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
                wherecon1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN DATE_SUB(DATE('"+request.start_date+"'),INTERVAL DATEDIFF('"+request.end_date+"','"+request.start_date+"') DAY) AND '"+request.start_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
                wherecon1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN DATE_SUB(DATE('"+request.start_date+"'),INTERVAL 30 DAY) AND '"+request.start_date+"'";
            } else {
                wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
                wherecon1 += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 60 DAY) AND DATE_SUB(DATE(NOW()), INTERVAL 30 DAY)";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
                wherecon1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN DATE_SUB(DATE('"+request.start_date+"'),INTERVAL 7 DAY) AND '"+request.start_date+"'";
            } else {
                wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
                wherecon1 += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 14 DAY) AND DATE_SUB(DATE(NOW()), INTERVAL 7 DAY)";
            }
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '"+request.date+"'";
                wherecon1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_SUB(DATE('"+request.date+"'),INTERVAL 1 DAY)";
            } else {
                wherecon += " AND date = current_date()";
                wherecon1 += " AND date = DATE_SUB(DATE(NOW()),INTERVAL 1 DAY)";
            }
        }
        con.query("SELECT (((IFNULL((SELECT COUNT(DISTINCT service_provider_id) FROM tbl_rent_booth WHERE "+wherecon+" GROUP BY booth_owner_id),0) - IFNULL((SELECT COUNT(DISTINCT service_provider_id) FROM tbl_rent_booth WHERE booth_owner_id = '"+booth_owner_id+"' AND id IN (SELECT id FROM tbl_rent_booth WHERE "+wherecon+" GROUP BY booth_owner_id HAVING count(id) <= 1) GROUP BY booth_owner_id),0)) / IFNULL((SELECT COUNT(DISTINCT service_provider_id) FROM tbl_rent_booth WHERE "+wherecon1+" GROUP BY booth_owner_id),0))*100) as BRRR",function(err1, result1){
            if(err1){
                console.log("Booth Renter Retention Ratio",err1);
            }
            if (!err1 && result1[0] != undefined && result1[0].BRRR != undefined) {
                callback(parseFloat(result1[0].BRRR).toFixed(2));
            } else {
                callback(parseFloat(0).toFixed(2));
            }
        });
    },

    /**
     * Function for get earning per hour
     */
    getEarningPerHour:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.status = 'Completed' AND trb.payment_status = 'Paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '"+request.date+"'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
        }
        con.query("select trb.*,IFNULL(SUM(HOUR(TIMEDIFF(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%H:%i:%S'),DATE_FORMAT(CONVERT_TZ(CONCAT(trb.end_date,' ',trb.end_slot_time),'+00:00','"+request.timezone_diff+"'),'%H:%i:%S')))),0) as total_hour,IFNULL(SUM(trb.total_amount),0) as total_earning from tbl_rent_booth as trb where "+wherecon+" GROUP BY trb.booth_owner_id", function(err1, result1) {
            if(err1){
                console.log("earning per hour",err1);
            }
            if (!err1 && result1[0] != undefined && result1[0].total_earning != undefined) {
                if(parseFloat(result1[0].total_earning) > 0 && parseFloat(result1[0].total_hour) > 0){
                    callback(parseFloat(result1[0].total_earning / result1[0].total_hour).toFixed(2))
                } else {
                    callback(parseFloat(0).toFixed(2));
                }
            } else {
                callback(parseFloat(0).toFixed(2));
            }
        });
    },

    /**
     * Function for get top booth renters
     */
    getTopBoothRenters:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.is_deleted = '0' AND trb.status = 'Completed' AND trb.payment_status = 'Paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '"+request.date+"'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
        }
        con.query("SELECT trb.*,SUM(trb.total_amount) as total_revenue,CONCAT(tsp.first_name,' ',tsp.last_name) as name,tsp.email,CONCAT(tsp.country_code,'',tsp.phone) as phone,CONCAT('" + global.S3_BUCKET_ROOT + global.SP_IMAGE + "','',tsp.profile_image) as profile_image FROM tbl_rent_booth as trb LEFT JOIN tbl_service_provider as tsp ON trb.service_provider_id = tsp.id WHERE "+wherecon+" GROUP BY trb.service_provider_id ORDER BY total_revenue DESC LIMIT 20", function(err1, result1) {
            if(err1){
                console.log("Top Booth Renters",err1);
            }
            if (!err1 && result1.length > 0) {
                callback(result1);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get top booth rented
     */
    getTopBoothRented:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.is_deleted = '0' AND trb.status = 'Completed' AND trb.payment_status = 'Paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '"+request.date+"'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
        }
        con.query("SELECT trb.*,SUM(trb.total_amount) as total_revenue,tw.tag_name,tw.description,twc.name as category_name,CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',ti.image_name) as image_name FROM tbl_rent_booth as trb LEFT JOIN tbl_workspace as tw ON trb.workspace_id = tw.id LEFT JOIN tbl_master_workspace_category as twc ON twc.id = tw.workspace_category_id LEFT JOIN tbl_image as ti ON ti.type_id = trb.workspace_id AND ti.type = 'workspace_location_image' WHERE "+wherecon+" GROUP BY trb.workspace_id ORDER BY total_revenue DESC LIMIT 20", function(err1, result1) {
            if(err1){
                console.log("Top Booth Rented",err1);
            }
            if (!err1 && result1.length > 0) {
                callback(result1);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get report tiles data
     */
    getreporttiles(booth_owner_id,request,callback){
        var finaltiles = [];
        if(request.type != undefined && request.type == 'old'){
            var sqlquery = "SELECT tmt.* FROM tbl_service_provider_tiles as tspt INNER JOIN tbl_master_tiles as tmt ON tspt.tiles_id = tmt.id WHERE tspt.service_provider_id = '"+booth_owner_id+"' AND tmt.status = 'Active' AND tmt.user_type = 'BoothOwner' AND tmt.is_deleted = '0' GROUP BY tmt.id ORDER BY tmt.id ASC";
        } else {
            var sqlquery = "SELECT tmt.* FROM tbl_master_tiles as tmt WHERE tmt.status = 'Active' AND tmt.user_type = 'BoothOwner' AND tmt.is_deleted = '0' GROUP BY tmt.id ORDER BY tmt.id ASC";
        }
        con.query(sqlquery,function(error,tiles,fields){
            if(!error && tiles.length > 0){
                asyncloop(tiles, function(item, next) {
                    var tiledata = {
                        id:item.id,
                        name:item.name,
                        compare_value:0,
                        value:0,
                        arrayvalue:[],
                    }
                    if (item.id == 8) {
                        booth_owner.getBoothRenterRetentionRatio(booth_owner_id,request,function(boothrenter_retention_ratio){
                            tiledata["value"] = boothrenter_retention_ratio;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 9) {
                        booth_owner.getEarningPerHour(booth_owner_id,request,function(earning_per_hour){
                            tiledata["value"] = earning_per_hour;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 10) {
                        booth_owner.getTopBoothRenters(booth_owner_id,request,function(top_boothrenters){
                            tiledata["arrayvalue"] = top_boothrenters;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 11) {
                        booth_owner.getTopBoothRented(booth_owner_id,request,function(top_boothrented){
                            tiledata["arrayvalue"] = top_boothrented;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else {
                        next()
                    }
                },function() {
                    callback(finaltiles);
                });
            } else {
                callback(finaltiles);
            }
        });
    },

    /**
     * Function for report calculation
     */
    report: function(request, booth_owner_id, callback) {
        booth_owner.getreporttiles(booth_owner_id,request,function(finaltiles){
            if(finaltiles.length > 0){
                if(request.type != undefined && request.type == 'old'){
                    booth_owner.getreportgraphs(booth_owner_id,request,function(graphs){
                        callback('1', "Report found successfully", { tiles: finaltiles, graph: graphs });
                    });
                } else {
                    callback('1', "Report found successfully", { tiles: finaltiles, graph: [] });
                }
            } else {
                callback('0', "Please add tiles to dashboard", null);
            }
        });
    },

    /**
     * Function for get report graphs data
     */
    getreportgraphs(booth_owner_id,request,callback){
        var finalgraphs = [];
        var comparerequest = {};
        if(request.period != undefined && request.period != ""){
            comparerequest["period"] = request.period;
        }
        if(request.compare_date != undefined && request.compare_date != ""){
            comparerequest["date"] = request.compare_date;
        }
        if(request.compare_start_date != undefined && request.compare_start_date != "" && request.compare_end_date != undefined && request.compare_end_date != ""){
            comparerequest["start_date"] = request.compare_start_date;
            comparerequest["end_date"] = request.compare_end_date;
        }
        con.query("SELECT tmg.* FROM tbl_service_provider_graph as tspg INNER JOIN tbl_master_graph as tmg ON tspg.graph_id = tmg.id WHERE tspg.service_provider_id = '"+booth_owner_id+"' AND tmg.status = 'Active' AND tmg.user_type = 'BoothOwner' AND tmg.is_deleted = '0' GROUP BY tmg.id ORDER BY tmg.id ASC",function(error,graphs,fields){

            if(!error && graphs.length > 0){
                asyncloop(graphs, function(item,next){
                    var tiledata = {
                        id:item.id,
                        name:item.name,
                        values:[],
                    }
                    if (item.id == 13) {
                        booth_owner.getTotalEarningsPerBoothgraph(booth_owner_id,request,function(totalearning_perbooth){
                            if(totalearning_perbooth.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    booth_owner.getTotalEarningsPerBoothgraph(booth_owner_id,comparerequest,function(compare_totalearning_perbooth){
                                        if(compare_totalearning_perbooth.length > 0){
                                            require("../service_provider/service_provider_model").compare_multiple_value(totalearning_perbooth, compare_totalearning_perbooth, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = totalearning_perbooth;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = totalearning_perbooth;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 14) {
                        booth_owner.getTotalEarningsAllBoothgraph(booth_owner_id,request,function(totalearning_allbooth){
                            if(totalearning_allbooth.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    booth_owner.getTotalEarningsAllBoothgraph(booth_owner_id,comparerequest,function(compare_totalearning_allbooth){
                                        if(compare_totalearning_allbooth.length > 0){
                                            require("../service_provider/service_provider_model").compare_multiple_value(totalearning_allbooth, compare_totalearning_allbooth, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = totalearning_allbooth;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = totalearning_allbooth;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 15) {
                        if(request.period == 'Daily'){
                            booth_owner.getBusiestHoursofthedaygraph(booth_owner_id,request,function(hoursofthe_day){
                                if(hoursofthe_day.length > 0){
                                    if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                        booth_owner.getBusiestHoursofthedaygraph(booth_owner_id,comparerequest,function(compare_hoursofthe_day){
                                            if(compare_hoursofthe_day.length > 0){
                                                require("../service_provider/service_provider_model").compare_multiple_value(hoursofthe_day, compare_hoursofthe_day, function(compared_value) {
                                                    tiledata["values"] = compared_value;
                                                    finalgraphs.push(tiledata);
                                                    next();
                                                });
                                            } else {
                                                tiledata["values"] = hoursofthe_day;
                                                finalgraphs.push(tiledata);
                                                next();
                                            }
                                        });
                                    } else {
                                        tiledata["values"] = hoursofthe_day;
                                        finalgraphs.push(tiledata);
                                        next();
                                    }
                                } else {
                                    tiledata["values"] = [];
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            });
                        } else {
                            next();
                        }
                    } else if (item.id == 16) {
                        if(request.period == 'Weekly' || request.period == 'Custom'){
                            booth_owner.getBusiestDaysinaWeekgraph(booth_owner_id,request,function(daysofthe_week){
                                if(daysofthe_week.length > 0){
                                    if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                        booth_owner.getBusiestDaysinaWeekgraph(booth_owner_id,comparerequest,function(compare_daysofthe_week){
                                            if(compare_daysofthe_week.length > 0){
                                                require("../service_provider/service_provider_model").compare_multiple_value(daysofthe_week, compare_daysofthe_week, function(compared_value) {
                                                    tiledata["values"] = compared_value;
                                                    finalgraphs.push(tiledata);
                                                    next();
                                                });
                                            } else {
                                                tiledata["values"] = daysofthe_week;
                                                finalgraphs.push(tiledata);
                                                next();
                                            }
                                        });
                                    } else {
                                        tiledata["values"] = daysofthe_week;
                                        finalgraphs.push(tiledata);
                                        next();
                                    }
                                } else {
                                    tiledata["values"] = [];
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            });
                        } else {
                            next();
                        }
                    } else if (item.id == 17) {
                        booth_owner.getBusiestDaysinaYeargraph(booth_owner_id,request,function(daysofthe_year){
                            if(daysofthe_year.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    booth_owner.getBusiestDaysinaYeargraph(booth_owner_id,comparerequest,function(compare_daysofthe_year){
                                        if(compare_daysofthe_year.length > 0){
                                            require("../service_provider/service_provider_model").compare_multiple_value(daysofthe_year, compare_daysofthe_year, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = daysofthe_year;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = daysofthe_year;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 18) {
                        if(request.period == 'Monthly'){
                            booth_owner.getBusiestMonthoftheYeargraph(booth_owner_id,request,function(weekofthe_year){
                                if(weekofthe_year.length > 0){
                                    if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                        booth_owner.getBusiestMonthoftheYeargraph(booth_owner_id,comparerequest,function(compare_weekofthe_year){
                                            if(compare_weekofthe_year.length > 0){
                                                require("../service_provider/service_provider_model").compare_multiple_value(weekofthe_year, compare_weekofthe_year, function(compared_value) {
                                                    tiledata["values"] = compared_value;
                                                    finalgraphs.push(tiledata);
                                                    next();
                                                });
                                            } else {
                                                tiledata["values"] = weekofthe_year;
                                                finalgraphs.push(tiledata);
                                                next();
                                            }
                                        });
                                    } else {
                                        tiledata["values"] = weekofthe_year;
                                        finalgraphs.push(tiledata);
                                        next();
                                    }
                                } else {
                                    tiledata["values"] = [];
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            });
                        } else {
                            next();
                        }
                    } else if (item.id == 19) {
                        booth_owner.getCancellationsgraph(booth_owner_id,request,function(cancellations){
                            if(cancellations.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    booth_owner.getCancellationsgraph(booth_owner_id,comparerequest,function(compare_cancellations){
                                        if(compare_cancellations.length > 0){
                                            require("../service_provider/service_provider_model").compare_value(cancellations, compare_cancellations, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = cancellations;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = cancellations;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 20) {
                        booth_owner.getAllboothrentersgraph(booth_owner_id,request,function(allboothrenter){
                            if(allboothrenter.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    booth_owner.getAllboothrentersgraph(booth_owner_id,comparerequest,function(compare_allboothrenter){
                                        if(compare_allboothrenter.length > 0){
                                            require("../service_provider/service_provider_model").compare_value(allboothrenter, compare_allboothrenter, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = allboothrenter;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = allboothrenter;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else {
                        next();
                    }
                },function(){
                    callback(finalgraphs);
                });
            } else {
                callback(finalgraphs);
            }
        });
    },

    /**
     * Function for get Total Earnings Per Booth graph
     */
    getTotalEarningsPerBoothgraph:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.is_deleted = '0' AND trb.status = 'Completed' AND trb.payment_status = 'Paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,count(trb.total_amount) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_rent_booth as trb WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("TotalEarningsAllBooth", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("select DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,count(trb.total_amount) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_rent_booth as trb where " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("TotalEarningsAllBooth", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,count(trb.total_amount) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_rent_booth as trb where " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("TotalEarningsAllBooth", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
            con.query("select date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H:00') as day_name,count(trb.total_amount) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_rent_booth as trb where " + wherecon + " GROUP BY date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H') ORDER BY date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("TotalEarningsAllBooth", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for get Total Earnings All Booth graph
     */
    getTotalEarningsAllBoothgraph:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.is_deleted = '0' AND trb.status = 'Completed' AND trb.payment_status = 'Paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,count(trb.total_amount) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_rent_booth as trb WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("TotalEarningsAllBooth", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("select DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,count(trb.total_amount) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_rent_booth as trb where " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("TotalEarningsAllBooth", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,count(trb.total_amount) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_rent_booth as trb where " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("TotalEarningsAllBooth", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
            con.query("select date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H:00') as day_name,count(trb.total_amount) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_rent_booth as trb where " + wherecon + " GROUP BY date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H') ORDER BY date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("TotalEarningsAllBooth", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for get Busiest Hours of the day graph data
     */
    getBusiestHoursofthedaygraph:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.is_deleted = '0' AND trb.status = 'Completed' AND trb.payment_status = 'Paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '"+request.date+"'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
        }
        con.query("select date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H:00') as day_name,date_format(trb.date,'%Y-%m-%d') as date,IFNULL(count(id),0) as main_count from tbl_rent_booth as trb WHERE "+wherecon+" GROUP BY date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H') ORDER BY date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H') ASC", function(error, results) {
            if(error){
                console.log("Busiest Hours of the day graph data",error);
            }
            if (!error && results[0] != undefined) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Busiest Days in a Week graph data
     */
    getBusiestDaysinaWeekgraph:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.is_deleted = '0' AND trb.status = 'Completed' AND trb.payment_status = 'Paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '"+request.date+"'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
        }
        con.query("select DAYNAME(trb.date) as day_name,date_format(trb.date,'%Y-%m-%d') as date,IFNULL(count(id),0) as main_count from tbl_rent_booth as trb WHERE "+wherecon+" GROUP BY DAYNAME(trb.date) ORDER BY DAYNAME(trb.date) ASC", function(error, results) {
            if(error){
                console.log("Busiest Days in a Week graph data",error);
            }
            if (!error && results[0] != undefined) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Busiest Days in a Year graph data
     */
    getBusiestDaysinaYeargraph:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.is_deleted = '0' AND trb.status = 'Completed' AND trb.payment_status = 'Paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '"+request.date+"'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
        }
        con.query("select DAYNAME(trb.date) as day_name,date_format(trb.date,'%Y-%m-%d') as date,IFNULL(count(id),0) as main_count from tbl_rent_booth as trb WHERE "+wherecon+" GROUP BY DAYNAME(trb.date) ORDER BY DAYNAME(trb.date) ASC", function(error, results) {
            if(error){
                console.log("Busiest Days in a Year graph data",error);
            }
            if (!error && results[0] != undefined) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Busiest Month of the Year graph data
     */
    getBusiestMonthoftheYeargraph:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.is_deleted = '0' AND trb.status = 'Completed' AND trb.payment_status = 'Paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '"+request.date+"'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
        }
        con.query("select MONTHNAME(trb.date) as day_name,date_format(trb.date,'%Y-%m-%d') as date,IFNULL(count(id),0) as main_count from tbl_rent_booth as trb WHERE "+wherecon+" GROUP BY MONTHNAME(trb.date) ORDER BY MONTH(trb.date) ASC", function(error, results) {
            if(error){
                console.log("Busiest Month of the Year graph data",error);
            }
            if (!error && results[0] != undefined) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for cancellations graph data
     */
    getCancellationsgraph:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.status = 'Cancelled' AND trb.is_deleted = '0'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("SELECT count(trb.id) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_rent_booth as trb WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getCancellationsgraph", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("SELECT count(trb.id) as main_count,DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_rent_booth as trb WHERE " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("getCancellationsgraph", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("SELECT count(trb.id) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_rent_booth as trb WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getCancellationsgraph", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
            con.query("SELECT count(trb.id) as main_count,date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H:00') as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_rent_booth as trb WHERE " + wherecon + " GROUP BY date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H') ORDER BY date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("getCancellationsgraph", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for All booth renters graph data
     */
    getAllboothrentersgraph:function(booth_owner_id,request,callback){
        var wherecon = "trb.booth_owner_id = '" + booth_owner_id + "' AND trb.is_deleted = '0' AND trb.status = 'Completed' AND trb.payment_status = 'Paid' AND trb.service_provider_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("SELECT COUNT(DISTINCT trb.service_provider_id) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_rent_booth as trb WHERE " + wherecon + " GROUP BY DAYNAME(trb.date) ORDER BY DAYNAME(trb.date) ASC", function (error, results) {
                if (error) {
                    console.log("Allboothrenters", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("SELECT COUNT(DISTINCT trb.service_provider_id) as main_count,DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_rent_booth as trb WHERE " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("Allboothrenters", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND trb.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("SELECT COUNT(DISTINCT trb.service_provider_id) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_rent_booth as trb WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("Allboothrenters", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND trb.date = current_date()";
            }
            con.query("SELECT COUNT(DISTINCT trb.service_provider_id) as main_count,date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H:00') as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_rent_booth as trb WHERE " + wherecon + " GROUP BY date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H') ORDER BY date_format(CONCAT(trb.date,' ',trb.start_slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("Allboothrenters", error);
                }
                if (!error && results.length > 0) {
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for rate service provider for rent booth
     */
    rate_serviceproviderforrentbooth:function(request,callback){
        con.query("select * from tbl_booth_review where booth_rent_id = " + request.booth_rent_id + " AND service_provider_id = '"+request.service_provider_id+"' AND receiver_type = 'service_provider'", function (err, result) {
            if (!err) {
                if (result.length > 0) {
                    var update_ratting = {
                        ratting: request.ratting,
                        review_id:(request.review_id != undefined && request.review_id != "") ? request.review_id : "",
                        review: (request.review != undefined && request.review != "") ? request.review : result[0].review,
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                    }
                    common.update_data('tbl_booth_review', result[0].id, update_ratting, function (is_ratting) {
                        if (is_ratting) {
                            con.query("SELECT * FROM tbl_service_provider WHERE id = '" + request.booth_owner_id + "'", function (err, spdata) {
                                if (!err && spdata[0] != undefined) {
                                    var message = {
                                        sender_id: request.booth_owner_id,
                                        sender_type: USER_TYPE.SERVICE_PROVIDER,
                                        receiver_id: request.service_provider_id,
                                        type: 'service_provider',
                                        receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                        message: `${ spdata[0].first_name } ${ spdata[0].last_name } left you a review.`,
                                        title: 'Rate & Reviews',
                                        isaction_id: request.booth_rent_id,
                                        tag: 'updateboothratereview',
                                    }
                                    require('../../../config/common').prepare_notification(request.service_provider_id, message, function (notification) {
                                        callback('1', "Review updated successfully", true);
                                    });
                                } else {
                                    callback('1', "Review updated successfully", true);
                                }
                            });
                        } else {
                            callback('0', "Review can not be updated. Please try after sometime", null);
                        }
                    })
                } else {
                    var insert_ratting = {
                        ratting: request.ratting,
                        review: (request.review != undefined && request.review != "") ? request.review : "",
                        booth_owner_id: request.booth_owner_id,
                        service_provider_id: request.service_provider_id,
                        receiver_type: USER_TYPE.SERVICE_PROVIDER,
                        review_id:(request.review_id != undefined && request.review_id != "") ? request.review_id : "",
                        review_reply: "",
                        booth_rent_id: request.booth_rent_id,
                        is_deleted: '0',
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                    }
                    common.single_insert_data('tbl_booth_review', insert_ratting, function (is_revew_add) {
                        if (is_revew_add) {
                            con.query("SELECT * FROM tbl_service_provider WHERE id = '" + request.booth_owner_id + "'", function (err, spdata) {
                                if (!err && spdata[0] != undefined) {
                                    var message = {
                                        sender_id: request.booth_owner_id,
                                        sender_type: USER_TYPE.SERVICE_PROVIDER,
                                        receiver_id: request.service_provider_id,
                                        type: 'service_provider',
                                        receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                        message: `${ spdata[0].first_name } ${ spdata[0].last_name } left you a review.`,
                                        title: 'Rate & Reviews',
                                        isaction_id: request.booth_rent_id,
                                        tag: 'newboothratereview',
                                    }
                                    require('../../../config/common').prepare_notification(request.service_provider_id, message, function (notification) {
                                        callback('1', "Review added successfully", true);
                                    });
                                } else {
                                    callback('1', "Review added successfully", true);
                                }
                            });
                        } else {
                            callback('0', "Review can not be added. Please try after sometime", null);
                        }
                    })
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for get booth booked dates
     */
    get_boothbooked_dates:function(request,callback){
        const days = require("../service_provider/service_provider_model").get_dates_from_month_year(request.month, request.year);
        var calender = [];
        asyncloop(days,function(item,next){
            request.date = item.date;
            booth_owner.getboothbookinglist(request,function(bookings){
                if(bookings != null){
                    calender.push(item.date);
                    next();
                } else {
                    next();
                }
            });
        },function(){
            callback("1","Calender detail found successfully",calender);
        });
    },

    /**
     * Function for get booth booking list
     */
    getboothbookinglist:function(request,callback){
        con.query("select trb.id as booth_rent_id,trb.*,tbr.address,tbr.name as booth_location_name,tbr.latitude,tbr.longitude,tw.tag_name,tw.description,tmwc.name as category_name,tw.workspace_category_id as workspace_category_id,tsp.first_name,tsp.last_name,tsp.email,CONCAT(tsp.country_code,tsp.phone) as phone,CONCAT('"+global.S3_BUCKET_ROOT+global.SP_IMAGE+"',tsp.profile_image) as profile_image FROM tbl_rent_booth as trb LEFT JOIN tbl_booth_renter_location tbr ON trb.booth_location_id = tbr.id LEFT JOIN tbl_workspace as tw ON trb.workspace_id = tw.id LEFT JOIN tbl_master_workspace_category as tmwc ON tw.workspace_category_id = tmwc.id LEFT JOIN tbl_service_provider as tsp ON trb.service_provider_id = tsp.id WHERE trb.is_deleted = '0' AND trb.booth_location_id = '"+request.booth_location_id+"' AND trb.booth_owner_id = '"+request.booth_owner_id+"' AND trb.status IN ('Waiting','Accepted','Processing','Completed') AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '"+request.date+"' GROUP BY trb.id ORDER BY trb.id DESC", function (error,bookingdata){
            if(!error && bookingdata[0]!= undefined){
                callback(bookingdata);
            } else {
                callback(null);
            }
        });
    },
}

module.exports = booth_owner;
