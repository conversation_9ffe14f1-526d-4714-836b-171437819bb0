var con = require('../../../config/database');
var global = require('../../../config/constant');
var asyncloop = require('node-async-loop');
var datetime = require('node-datetime');
var randtoken = require('rand-token').generator();
const cryptoLib = require('cryptlib');
const shakey = cryptoLib.getHashSha256(global.key, 32);
const { t } = require('localizify');
var common = require('../../../config/common');
var moment = require('moment');
var template = require("../../../config/template");
const Global = require('../../../config/constant');
const stripe = require("../../../config/payment");
const serviceProviderModelsV2 = require('../../../src/service/serviceProvider')
const { logger } = require("../../../src/utils");
const { createIntent, BOOKING_PAYMENT_METHOD, USER_TYPE } = require('../../../src/service')
const { generateOTP } = require('../../../src/utils/otp')
const { omit, map } = require('lodash')

var user = {
    //check unique email or phone
    check_unique: function(unique_key, callback) {
        if (unique_key.email != undefined && unique_key.email != "") {
            var len = Object.keys(unique_key).length;
            var i = 1;
            asyncloop(unique_key, function(item, next) {
                var query = con.query("SELECT * FROM tbl_user WHERE is_deleted = '0' AND " + item.key + " = ?", item.value, function(err, result, fields) {
                    if (!err) {
                        if (result == '') {
                            if (len == i) {
                                callback(true);
                                return;
                            }
                        } else {
                            var message = "";
                            if (item.key == 'email') {
                                message = t('restapi_email_alreadyexists_error');
                            } else if (item.key == 'phone') {
                                message = t('restapi_phone_alreadyexists_error');
                            } else {
                                message = t('restapi_socialid_alreadyexists_error');
                            }
                            // message = ((item.key == 'email') ? t('restapi_email_alreadyexists_error') : t('restapi_phone_alreadyexists_error'))
                            callback(false, message);
                            return;
                        }
                    } else {
                        callback(false + err);
                        return;
                    }
                    i++;
                    next();
                });
            }, function() {
                callback();
            });
        } else {
            callback(true);
        }
    },

    //check retailer unique email or phone
    check_retailerunique: function(unique_key, callback) {
        var len = Object.keys(unique_key).length;
        var i = 1;
        asyncloop(unique_key, function(item, next) {
            var query = con.query("SELECT * FROM tbl_retailer WHERE is_deleted='0' AND " + item.key + " = ?", item.value, function(err, result, fields) {
                if (!err) {
                    if (result == '') {
                        if (len == i) {
                            callback(true);
                            return;
                        }
                    } else {
                        let message = ((item.key == 'email') ? t('restapi_email_alreadyexists_error') : t('restapi_phone_alreadyexists_error'))
                        callback(false, t(message));
                        return;
                    }
                } else {
                    callback(false);
                    return;
                }
                i++;
                next();
            });
        }, function() {
            callback();
        });
    },

    //add user
    add_user: function(request, callback) {
        if (request.social_id == undefined && (request.login_type == "F" || request.login_type == "G" || request.login_type == "A")) {
            callback(null, t("rest_keywords_socialid"), '0');
        }
        if (request.login_type == "S" && request.password == undefined) {
            callback(null, t("rest_keywords_password"), '0');
        }
        common.generate_unique_code('tbl_user', function(unique_code) {
            var insert = {
                first_name: request.first_name,
                last_name: request.last_name,
                email: request.email,
                country_code: request.country_code,
                phone: request.phone,
                latitude: (request.latitude != undefined && request.latitude != "") ? request.latitude : "",
                longitude: (request.longitude != undefined && request.longitude != "") ? request.longitude : "",
                otp_verify: 'Pending',
                status: 'Active',
                login_status: 'Offline',
                login_type: request.login_type,
                unique_id: unique_code,
                last_login: datetime.create().format('Y-m-d H:M:S'),
                updatetime: datetime.create().format('Y-m-d H:M:S'),
                insertdate: datetime.create().format('Y-m-d H:M:S'),
                profile_image: 'default-user.png'
            }
            if (request.profile_image != "" && request.profile_image != undefined) {
                insert.profile_image = request.profile_image;
            }
            if (request.social_id != undefined && request.social_id != "") {
                insert.social_id = request.social_id;
                insert.otp_verify = 'Verify';
            }
            if (request.password != undefined && request.password != "") {
                insert.password = cryptoLib.encrypt(request.password, shakey, global.iv);
            }
            // insert.otp = Math.floor(1000 + Math.random() * 9000);
            // insert.otp = '1234';
            con.query("INSERT INTO tbl_user SET ?", insert, function(err, result) {
                if (!err) {
                    var updateparam = {
                        token: randtoken.generate(64, "0123456789abcdefghijklnmopqrstuvwxyz"),
                        device_token: (request.device_token != undefined && request.device_token != "") ? request.device_token : "",
                        device_type: (request.device_type != undefined && request.device_type != "") ? request.device_type : "",
                        uuid: (request.uuid != undefined && request.uuid != "") ? request.uuid : "",
                        ip: (request.ip != undefined && request.ip != "") ? request.ip : "",
                        user_type: 'user',
                        os_version: (request.os_version != undefined && request.os_version != "") ? request.os_version : "",
                        model_name: (request.model_name != undefined && request.model_name != "") ? request.model_name : "",
                    }
                    require('../../../config/common').save_user_deviceinfo(result.insertId, updateparam, function(user_id) {
                        if (user_id != null) {
                            const user_otp = {
                                user_id: result.insertId,
                                status: 'signup'
                            }
                            if (request.social_id != undefined && request.social_id != "") {
                                user.get_user_detail(user_id, function(user_data, err) {
                                    if (user_data != null) {
                                        callback(user_data, t('restapi_signup_sucess'), '1');
                                    } else {
                                        callback(null, t('restapi_globals_error'), '0')
                                    }
                                });
                            } else {
                                user.send_otp(user_otp, function(response1) {
                                    if (response1) {
                                        user.get_user_detail(user_id, function(user_data, err) {
                                            if (user_data != null) {
                                                callback(user_data, t('restapi_signup_sucess'), '1');
                                            } else {
                                                callback(null, t('restapi_globals_error'), '0')
                                            }
                                        });
                                    } else {
                                        callback(null, t('restapi_globals_error'), '0')
                                    }
                                })
                            }
                        } else {
                            callback(null, t('restapi_globals_error'), '0')
                        }
                    });
                } else {
                    callback(null, t('restapi_globals_error'), '0')
                }
            });
        })
    },

    get_user_detail: function(user_id, callback) {
      const query = `
        SELECT 
          user.*,
          user.id AS user_id,
          CONCAT(user.first_name, ' ', user.last_name) AS full_name,
          CONCAT('${global.S3_BUCKET_ROOT}${global.USER_IMAGE}', '', user.profile_image) AS profile_image,
          user.id AS user_id,
          user.profile_image AS image_name,
          GET_USER_RATING(user.id, NULL, NULL) AS rating,
          GET_USER_REVIEW_COUNT(user.id, NULL, NULL) AS review_count,
          device.token
        FROM tbl_user user
        LEFT JOIN tbl_user_deviceinfo device ON user.id = device.user_id
          AND device.user_type = 'user'
        WHERE user.id = '${user_id}'
        AND user.is_deleted = 0
      `
      con.query(query, function(err, result) {
        if (!err && result[0] != undefined && result[0] != "") {
          callback(result[0])
        } else {
          callback(null)
        }
      })
    },

    //get shop by distance
    distance: function(request, table, distance) {
        distance("ROUND((3956 * 2 * ASIN(SQRT( POWER(SIN(('" + request.latitude + "' - " + table + ".latitude) * pi()/180 / 2), 2) + COS('" + request.latitude + "' * pi()/180) * COS(" + table + ".latitude * pi()/180) * POWER(SIN(('" + request.longitude + "' - " + table + ".longitude) * pi()/180 / 2), 2) )))*2) as distance");
        /**below this will work */
        //distance("(3956 * 2 * ASIN(SQRT( POWER(SIN(('" + request.latitude + "' - " + table + ".latitude) *  pi()/180 / 2), 2) +COS( '" + request.latitude + "' * pi()/180) * COS(" + table + ".latitude * pi()/180) * POWER(SIN(( '" + request.longitude + "' - " + table + ".longitude) * pi()/180 / 2), 2) ))) as distance");
        // distance();
    },

    //user login
    check_login: function(request, response) {
        var error = "";
        if (request.social_id == undefined && (request.login_type == "F" || request.login_type == "G" || request.login_type == "A")) {
            response(null, t('rest_keywords_socialid'), '0');
        }
        if (request.login_type == "S" && request.password == undefined) {
            response(null, t('rest_keywords_password'), '0');
        }
        if (request.social_id != undefined && request.login_type != 'S') {
            var wherecondition = " social_id = '" + request.social_id + "' AND login_type = '" + request.login_type + "' AND is_deleted='0' "
        } else {
            var wherecondition = " (email = '" + request.email + "' OR phone = '" + request.phone + "')  AND is_deleted='0' "
        }
        con.query("select *,id as user_id from tbl_user where " + wherecondition + "", function(err, result) {
            if (!err) {
                if (result[0] != undefined) {
                    if (request.login_type == 'S') {
                        if (result[0].login_type != 'S') {
                            response(null, t('restapi_login_errormsg'), '0');
                            return;
                        }
                        var user_pass = result[0].password;
                        var original_pass = cryptoLib.encrypt(request.password, shakey, global.iv);
                        if (user_pass != original_pass) {
                            response(null, t('rest_keywords_invalid_emailorphone_pass_login'), '0');
                            return;
                        }
                    }
                    if (result[0].status == "Inactive") {
                        response(null, t('restapi_logindisactive_error'), '3');
                    } else if (result[0].otp_verify == "Pending") {
                        const user_otp = {
                            user_id: result[0].user_id,
                            status: 'signup'
                        }
                        user.send_otp(user_otp, function(response1) {
                            if (response1) {
                                user.get_user_detail(result[0].user_id, function(user_data, err) {
                                    response(user_data, t('restapi_loginotpverify_error'), '4');
                                });
                            } else {
                                response1(null, t('restapi_globals_error'), '0')
                            }
                        })
                    } else {
                        var login_details = {
                            latitude: (request.latitude != undefined && request.latitude != "") ? request.latitude : "",
                            longitude: (request.longitude != undefined && request.longitude != "") ? request.longitude : "",
                            login_status: "Online",
                            last_login: datetime.create().format('Y-m-d H:M:S')
                        }
                        var query = con.query("UPDATE tbl_user SET ? WHERE id = " + result[0].id + " ", login_details, function(err, result, fields) {});
                        //add update user device information
                        var updateparam = {
                            token: randtoken.generate(64, "0123456789abcdefghijklnmopqrstuvwxyz"),
                            device_token: request.device_token,
                            user_type: 'user',
                            device_type: request.device_type,
                            uuid: (request.uuid != undefined && request.uuid != "") ? request.uuid : "",
                            ip: (request.ip != undefined && request.ip != "") ? request.ip : "",
                            os_version: (request.os_version != undefined && request.os_version != "") ? request.os_version : "",
                            model_name: (request.model_name != undefined && request.model_name != "") ? request.model_name : "",
                        }
                        require('../../../config/common').save_user_deviceinfo(result[0].id, updateparam, function(callback) {
                            if (callback != null) {
                                require("../chat/customtwilio").removeUserBindings('customer'+result[0].id).then((is_removed)=>{
                                    user.get_user_detail(result[0].id, function(user_data, err) {
                                        if (user_data != null) {
                                            response(user_data, t('restapi_login_sucess'), '1');
                                        } else {
                                            response(user_data, t('restapi_login_errormsg'), '0');
                                        }
                                    });
                                });
                            } else {
                                response(null, t('restapi_login_errormsg'), '0');
                            }
                        });
                    }
                } else {
                    if (request.social_id != undefined && request.login_type != 'S') {
                        response(null, t('rest_keywords_user_fb_not_found'), '11');
                    } else {
                        response(null, t('restapi_login_errormsg'), '0');
                    }
                }
            } else {
                response(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /** send otp  to user*/
    send_otp: function(request, callback) {
        var query = con.query("SELECT * FROM tbl_user where id='" + request.user_id + "' AND is_deleted = 0", function(err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else {
                if (result[0] != undefined) {
                    if (request.status == 'signup') {
                        var userdata = result[0];
                        var otp = generateOTP(6, 'numeric');
                        // var otp = 1234;
                        userdata.forgot_otp = otp;
                        template.send_otp(userdata, function(result1) {
                            var subject = global.APP_NAME + ' One Time Password (OTP)';
                            common.send_email(subject, result[0].email, result1, function(is_send) {
                                var phone = result[0].country_code + result[0].phone;
                                // var phone = result[0].phone;
                                // var message = "Your New OTP Code is " + otp;
                                var message = t('rest_keywords_customerapp_otpcode', {
                                    app_name: global.APP_NAME,
                                    otp_code: otp
                                });
                                require('../../../config/common').sendSMS(phone, message, function(response) {
                                    if (response) {
                                        var otp_update = {
                                            otp_verify: 'Pending',
                                            otp: otp,
                                        }
                                        var query = con.query("UPDATE tbl_user SET ? WHERE id = '" + result[0].id + "' ", otp_update, function(err, res) {
                                            if (!err) {
                                                user.get_user_detail(result[0].id, function(user_data, err) {
                                                    if (user_data != null) {
                                                        callback('1', t('restapi_otpsent_success'), user_data);
                                                    } else {
                                                        callback('0', t('restapi_sentotp_error'), null);
                                                    }
                                                });
                                            } else {
                                                callback('0', t('restapi_globals_error'), null);
                                            }
                                        });
                                    } else {
                                        callback('0', t('restapi_sentotp_error'), null);
                                    }
                                });
                            }); // Send Email
                        }); //end template
                    } else if (request.status == 'forgotpassword') {
                        var forgot_email = {
                            status: 'E',
                            email: result[0].email
                        }
                        user.check_email_phone(forgot_email, function(response) {
                            if (response == null) {
                                callback(t('restapi_loginemail_error'));
                            } else {
                                var userdata = response;
                                var rancode = generateOTP(6, 'numeric')
                                var forgotpass_otp = {
                                    forgot_otp: rancode,
                                    forgot_otp_verify: 'Pending',
                                    forgotpwd_datetime: datetime.create().format('Y-m-d H:M:S')
                                };
                                //update user forgot password data
                                user.update_user(userdata.id, forgotpass_otp, function(response) {
                                    if (response) {
                                        userdata.forgot_otp = forgotpass_otp.forgot_otp;
                                        userdata.forgotpwd_datetime = forgotpass_otp.forgotpwd_datetime;
                                        //get forgot password template
                                        template.send_otp(userdata, function(result) {
                                            var subject = global.APP_NAME + ' Forgot Password';
                                            common.send_email(subject, userdata.email, result, function(result) {
                                                if (!result) {
                                                    callback('0', t('restapi_mail_error'), null);
                                                } else {
                                                    // var phone = userdata.phone;
                                                    // var message = "Your Forgot Password OTP Code is " + forgotpass_otp.forgot_otp;
                                                    // common.sendSMS(phone, message, function(responce) {
                                                    //     if (responce) {
                                                    callback('1', t('restapi_forgotpassword_success'), userdata);
                                                    //     } else {
                                                    //         common.send_response(req, res, '0', t('restapi_forgotpass_error'), null);
                                                    //     }
                                                    // }); //send otp to mobile
                                                }
                                            }); // Send Email
                                        }); //end template
                                    } else {
                                        common.send_response("0", t('restapi_forgotpass_error'), null);
                                    }
                                });
                            }
                        });
                    } else {
                        callback('0', t('restapi_globals_error'), null);
                    }
                } else {
                    callback('0', t('restapi_user_notfound'), null);
                }
            }
        });
    },

    //verify user otp
    verify_otp: function(request, response) {
        // verify otp
        var condition = " id = '" + request.user_id + "' AND is_deleted = '0' ";
        if (request.status == "signup") {
            condition += " AND otp = '" + request.otp + "' ";
            var otp_status = {
                otp: '',
                otp_verify: 'Verify',
                last_login: datetime.create().format('Y-m-d H:M:S')
            }
        }
        if (request.status == "forgotpassword") {
            condition += " AND forgot_otp = '" + request.otp + "'";
            var otp_status = {
                forgot_otp: '',
                forgot_otp_verify: 'Verify',
                last_login: datetime.create().format('Y-m-d H:M:S')
            }
        }
        con.query("SELECT * FROM tbl_user where  " + condition + " ", function(err, result) {
            if (err) {
                response(null, t('restapi_globals_error') + err, '0');
            } else {
                if (result[0] != "" && result[0] != undefined) {
                    con.query("UPDATE tbl_user SET ? WHERE id = '" + request.user_id + "' ", { ...otp_status, status: 'Active' } , function(err, res) {
                        if (!err) {
                            var updateparam = {
                                token: randtoken.generate(64, "0123456789abcdefghijklnmopqrstuvwxyz"),
                                user_type: 'user'
                            }
                            require('../../../config/common').save_user_deviceinfo(request.user_id, updateparam, function() {
                                user.get_user_detail(request.user_id, function(userdata, err) {
                                    response(userdata, t('restapi_verifyotp_success'), '1');
                                });
                            });
                        } else {
                            response(null, t('restapi_globals_error'), '0');
                        }
                    });
                } else {
                    response(null, t('restapi_verifyotp_error'), '0');
                }
            }
        });
    },

    //reset password api
    resetpassword: function(request, callback) {
        var password = cryptoLib.encrypt(request.password, shakey, global.iv);
        var update_customer = {
            password: password
        };
        user.update_user(request.user_id, update_customer, function(error) {
            if (!error) {
                callback('0', t('restapi_globals_error') + error, null);
            } else {
                user.get_user_detail(request.user_id, function(userdetails) {
                    if (userdetails) {
                        callback('1', t('restapi_passwordreset_success'), userdetails);
                    } else {
                        callback('0', t('restapi_globals_error'), null);
                    }
                })
            }
        });
    },

    //check email exist or not
    check_email_phone: function(data, callback) {
        var condition = "";
        if (data.status == 'E') {
            condition += " AND email = '" + data.email + "'";
        }
        if (data.status == 'P') {
            condition += " AND phone = '" + data.phone + "'";
        }
        con.query("SELECT *,id as user_id FROM tbl_user where is_deleted='0' " + condition + "", function(err, result, fields) {
            if (!err) {
                if (result != '') {
                    callback(result[0]);
                } else {
                    callback(null);
                }
            } else {
                callback(null);
            }
        });
    },

    //update user
    update_user: function(user_id, update, callback) {
        con.query("UPDATE tbl_user SET ? WHERE id = '" + user_id + "'", update, function(err, result, fields) {
            if (!err) {
                callback(true);
            } else {
                callback(false);
            }
        });
    },

    //check unique email and phone
    checkuser_unique: function(user_id, unique_key, callback) {
        var len = Object.keys(unique_key).length;
        var i = 1;
        asyncloop(unique_key, function(item, next) {
            con.query("SELECT * FROM tbl_user WHERE id!='" + user_id + "' AND is_deleted='0' AND " + item.key + " = ?", item.value, function(err, result, fields) {
                if (!err) {
                    if (result == '') {
                        if (len == i) {
                            callback(true);
                            return;
                        }
                    } else {
                        let message = ((item.key == 'email') ? t('restapi_emailalreadyexists_error') : t('restapi_phonealreadyexists_error'))
                        callback(false, message);
                        return;
                    }
                } else {
                    callback(false, t('restapi_globals_error'));
                    return;
                }
                i++;
                next();
            });
        }, function() {
            callback();
        });
    },

    //get country list
    country_list: function(request, callback) {
        var condition = " is_delete = '0' ";
        if (request.country_id != undefined && request.country_id != "") {
            condition += "AND id = '" + request.country_id + "' ";
        }
        con.query("SELECT id,name,dial_code from tbl_country where " + condition + " AND is_active = 1", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback(result, t('restapi_country_found'), '1');
                } else {
                    callback(null, t('restapi_country_notfound'), '1');
                }
            } else {
                callback(null, t('restapi_globals_error') + err, '0');
            }
        })
    },

    //change password
    change_password: function(req, callback) {
        con.query("select * from tbl_user where id = '" + req.user_id + "' and is_deleted = 0 ", function(err, result, fields) {
            if (!err) {
                var decrypt_password = cryptoLib.decrypt(result[0].password, shakey, global.iv);
                if (decrypt_password == req.old_password) {
                    if (req.old_password == req.new_password) {
                        callback(null, t('reatapi_user_old_new_password_same'), '0');
                    } else {
                        var password = {
                            password: cryptoLib.encrypt(req.new_password, shakey, global.iv)
                        };
                        con.query("UPDATE tbl_user SET ? WHERE id = '" + req.user_id + "'", password, function(err, result, fields) {
                            if (!err) {
                                callback(true, t('restapi_user_change_password_success'), '1');
                            } else {
                                callback(null, t('restapi_globals_error'), '0');
                            }
                        });
                    }
                } else {
                    callback(null, t('restapi_user_change_password_fail'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    //get product category
    category_list: function(request, responce) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        /*var query = con.query("select tc.id as category_id,tc.name,tc.status,tc.is_deleted,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',tc.image_name) as category_image,'Product' as type from tbl_product_category tc where tc.status = 'Active' AND tc.is_deleted = 0 order by tc.id DESC", function(err, result) {
            if (!err) {
                if (result[0] != undefined) {*/
                    con.query("select tc.id as category_id,tc.name,tc.status,tc.is_deleted,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',tc.image_name) as category_image,'Service' as type from tbl_service_category tc where is_deleted = '0' AND status = 'Active' order by tc.id DESC", function(err1, result1) {
                        if (!err1 && result1[0] != "" && result1[0] != undefined) {
                            //var category = result.concat(result1);
                            responce(result1, t('restapi_productcateoryfound'), '1')
                        } else {
                            responce(null, t('restapi_productcateoryfound'), '1')
                        }
                    });
                /*} else {
                    responce(null, t('restapi_cateorynotfound'), '0')
                }
            } else {
                responce(null, t('restapi_globals_error'), '0');
            }
        });*/
    },

  service_provider: function(request, callback) {
    let page = request.page - 1
    let limit = page * global.PER_PAGE
    let per_page = global.PER_PAGE
    let condition = ' tsp.status = \'Active\' AND tsp.is_deleted = \'0\''
    let radius = ''
    if (request.word != undefined && request.word != '') {
      condition += ' AND (LOWER(tsp.first_name) LIKE \'%' + request.word.toLowerCase() + '%\' OR LOWER(tsp.last_name) LIKE \'%' + request.word.toLowerCase() + '%\' OR LOWER(CONCAT(tsp.first_name, \' \', tsp.last_name)) LIKE \'%' + request.word.toLowerCase() + '%\' OR LOWER(tsp.email) LIKE \'%' + request.word.toLowerCase() + '%\' OR LOWER(ts.service_name) LIKE \'%' + request.word.toLowerCase() + '%\' OR LOWER(tp.product_name) LIKE \'%' + request.word.toLowerCase() + '%\') '
    }
    if (request.category_id != undefined && request.category_id != '') {
      condition += ' AND (tp.category_id = \'' + request.category_id + '\' OR ts.category_id = \'' + request.category_id + '\')'
    }
    if (request.start_price != undefined && parseInt(request.start_price) >= 0 && request.end_price != undefined && parseInt(request.end_price) >= 0) {
      condition += ' AND ((ts.price BETWEEN ' + request.start_price + ' AND ' + request.end_price + ') OR (tsubp.price BETWEEN ' + request.start_price + ' AND ' + request.end_price + '))'
    }
    if (request.time != undefined && request.time != 'null' && request.time != null && request.time != '') {
      let moment = require('moment-timezone')
      let selectedtime = moment.utc(moment().format('YYYY-MM-DD') + ' ' + request.time, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format('HH:mm')
      condition += ' AND tsp.id IN (SELECT service_provider_id FROM tbl_service_provider_available_slot WHERE \'' + selectedtime + '\' BETWEEN DATE_FORMAT(CONVERT_TZ(CONCAT(current_date(),\' \',from_time),\'+00:00\',\'' + request.timezone_diff + '\'),\'%H:%i\') AND DATE_FORMAT(CONVERT_TZ(CONCAT(current_date(),\' \',to_time),\'+00:00\',\'' + request.timezone_diff + '\'),\'%H:%i\')) '
    }

    if (request.start_radius != undefined && request.start_radius >= 0 && request.end_radius != undefined && request.end_radius != '') {
      radius = 'distance between ' + request.start_radius + ' AND ' + request.end_radius + ''
    } else {
      radius = 'distance <= 50'
    }

    let orderby = ' ORDER BY tsp.id DESC'
    if (request.alphabetical != undefined && request.alphabetical != '') {
      orderby = ' ORDER BY concat(tsp.first_name,\'\',tsp.last_name) ' + request.alphabetical
    }
    if (request.ratting != undefined && request.ratting != '') {
      orderby = ' ORDER BY ratting ' + request.ratting
    }
    if (request.distance_sort != undefined && request.distance_sort != '') {
      orderby = ' ORDER BY distance ASC'
    }
    if (request.price_sort != undefined && request.price_sort != '') {
      orderby = ' ORDER BY ts.price ' + request.price_sort + ',tsubp.price ' + request.time_sort
    }
    if (request.time_sort != undefined && request.time_sort != '') {
      orderby = ' ORDER BY tspas.from_time ' + request.time_sort
    }
    let latlong = {
      latitude: request.latitude,
      longitude: request.longitude,
    }
    let groupby = ''
    if (request.screen_type != undefined && request.screen_type == 'Map') {
      groupby = 'GROUP BY tbl.id'
    } else {
      groupby = 'GROUP BY tbl.service_provider_id'
    }
    user.distance(latlong, 'tbl', function(distance) {
      let promoqueru = ''
      if (request.min_discount != undefined && request.min_discount != '' && request.max_discount != undefined && request.max_discount != '') {
        promoqueru = ',IFNULL((SELECT discount_amount FROM tbl_occational_promo WHERE is_deleted = \'0\' AND service_provider_id  = tsp.id AND valid_till >= CURDATE() AND discount_type = \'Percentage\' AND (is_clientspecific_promo = \'0\' OR (is_clientspecific_promo = \'1\' AND id IN (SELECT promo_id FROM tbl_occational_promo_clients WHERE user_id = \'' + request.user_id + '\' GROUP BY promo_id))) AND discount_amount BETWEEN \'' + request.min_discount + '\' AND \'' + request.max_discount + '\' GROUP BY service_provider_id),0) as discount_amount'
        radius += ' AND discount_amount > 0'
      }
      con.query('SELECT value FROM tbl_settings WHERE id = \'4\'', function(err1, settings) {
        let recommended_max_avgrating = (!err1 && settings[0] != undefined) ? settings[0].value : '3.5'
        let distancequery = 'IFNULL(ROUND(3959 * 2 * ASIN(SQRT( POWER(SIN((' + request.latitude + ' - tbl.latitude) * pi()/180 / 2), 2) + COS(' + request.latitude + ' * pi()/180) * COS(tbl.latitude * pi()/180) *POWER(SIN((' + request.longitude + ' - tbl.longitude) * pi()/180 / 2), 2) ))),\'0\')'
        let sql = 'SELECT DISTINCT(tsp.id),tsp.*,tbl.id AS business_id,tbl.name AS business_address,IFNULL((SELECT status FROM tbl_bookmark WHERE service_provider_id = tsp.id AND user_id = ' + request.user_id + '),0) AS bookmark_status,CONCAT(\'' + Global.S3_BUCKET_ROOT + Global.SP_IMAGE + '\',\'\',tsp.profile_image) AS vendor_image,' + distancequery + ' as distance,GET_SERVICE_PROVIDER_RATING(tsp.id, null) AS ratting,GET_SERVICE_PROVIDER_REVIEW_COUNT(tsp.id, null) AS review,tbl.latitude,tbl.longitude,IFNULL((SELECT COUNT(id) FROM tbl_service WHERE service_provider_id = tbl.service_provider_id AND is_deleted = \'0\' GROUP BY service_provider_id LIMIT 1),0) as total_services,IFNULL((SELECT COUNT(id) FROM tbl_product WHERE service_provider_id = tbl.service_provider_id AND publish = \'Yes\' AND is_deleted = \'0\' GROUP BY service_provider_id LIMIT 1),0) as total_products,IF(tsp.is_recommended = \'No\',IF(GET_SERVICE_PROVIDER_RATING(tsp.id, null) >= ' + recommended_max_avgrating + ',\'Yes\',\'No\'), tsp.is_recommended) as is_recommended' + promoqueru + ' FROM tbl_business_location as tbl INNER JOIN tbl_service_provider as tsp ON tsp.id = tbl.service_provider_id LEFT JOIN tbl_service_provider_available_slot as tspas ON tsp.id = tspas.service_provider_id LEFT JOIN tbl_service as ts ON tsp.id = ts.service_provider_id AND ts.is_deleted = \'0\' LEFT JOIN tbl_product as tp ON tsp.id = tp.service_provider_id AND tp.is_deleted = \'0\' AND tp.publish = \'Yes\' LEFT JOIN tbl_subproduct as tsubp ON tp.id = tsubp.product_id AND tsubp.is_deleted = \'0\' WHERE ' + condition + ' ' + groupby + ' HAVING (total_services >= 1 OR total_products >= 1) AND ' + radius + orderby + ' LIMIT ' + limit + ', ' + per_page + ''
        con.query(sql, function(err, result) {
          if (!err) {
            if (result.length > 0) {
              let rows = 0
              asyncloop(result, function(item, next) {
                require('../service_provider/service_provider_model').getspsliderimages(item.id, function(rescode, resmessage, sliderimages) {
                  if (rescode == '1' && sliderimages != null) {
                    result[rows].sliderimages = sliderimages
                  } else {
                    result[rows].sliderimages = []
                  }
                  rows++
                  next()
                })
              }, function() {
                callback(result, 'Service provider list found successfully', '1')
              })
            } else {
              callback([], 'No professionals found in your area', '0')
            }
          } else {
            console.log(err)
            callback([], t('restapi_globals_error'), '0')
          }
        })
      })
    })
  },

  /** home screen data */
  HomescreenData: function(request, callback) {
    let homescreen = {}
    user.category_list(request, function(responce) {
      homescreen.categories = responce
      user.service_provider(
        request,
        function(responsedata) {
          homescreen.service_provider = responsedata
          con.query(`
            SELECT COUNT(id) AS total_notification
            FROM tbl_notification
            WHERE receiver_id = ${ request.user_id }
            AND receiver_type = 'user'
            AND status = 'Unread'
            GROUP BY receiver_id
          `, function(err, notifications) {
            homescreen.total_unread_notification = (!err && notifications[0] != undefined) ? notifications[0].total_notification : 0
            con.query(`
              SELECT COUNT(id) AS total_processing_booking
              FROM tbl_appointment_booking
              WHERE user_id = ${ request.user_id }
              AND is_deleted = '0'
              AND booking_status IN ('In Progress')
              GROUP BY user_id
            `, function(err1, result1) {
              homescreen.total_processing_booking = (!err1 && result1[0] != undefined) ? result1[0].total_processing_booking : 0
              callback('1', t('restapi_homescreen_found'), homescreen)
            })
          })
      })
    })
  },

    /** make quick booking payment */
    makequickbookingpayment:function(request,user_detail,callback){
        if (request.total_amount <= 0) {
          return callback(null, "no payment amount - assume pay later", "1")
        }

        if(request.wallet_amount != undefined && parseFloat(request.wallet_amount) > 0){
            if(parseFloat(user_detail.wallet_amount) >= parseFloat(request.wallet_amount)){
                callback({transaction_id:"BLOOKD"+moment().utc().format("YYYYMMDDHHiiss"),balance_transaction: request.wallet_amount,receipt_url:""},"Payment make successfully","1");
            } else {
                callback(null, 'Insufficient fund in your Wallet. wallet payment is not possible.', '0');
            }
        } else {
            if(request.payment_from == 'ApplePay' || request.payment_from == 'GooglePay'){
                callback({transaction_id: request.transaction_id,balance_transaction: "",receipt_url:""},"Payment make successfully","1");
            } else {
                con.query("select * from tbl_card_details where id = " + request.card_id + "", function(err, result) {
                    if (err) {
                        callback(null, t('restapi_globals_error'), '0');
                    } else if (result[0] == "" || result[0] == undefined) {
                        callback(null, 'User card detail not found', '0');
                    } else {
                        let payment_obj = {
                            amount: Math.round(request.total_amount * 100),
                            currency: "USD",
                            capture:(request.payment_mode == 'pay_later') ? false : true,
                            application_fee_amount : Math.round(request.admin_earning * 100),
                            destination : request.merchant_account_id,
                            // source: card.card_token,
                            customer: result[0]['customer_id'],
                            description: 'For user booking',
                        }
                        require("../../../config/payment").tranferStripePlatform(payment_obj, function(code, msg, stripe_data) {
                            if (stripe_data != null) {
                                callback({transaction_id: stripe_data.transaction_id,balance_transaction: (stripe_data.balance_transaction != null && stripe_data.balance_transaction != "" ? stripe_data.balance_transaction : ""),receipt_url:(stripe_data.receipt_url != "" && stripe_data.receipt_url != undefined) ? stripe_data.receipt_url : ""},"Payment make successfully","1");
                            } else {
                                callback(stripe_data, msg, code);
                            }
                        });
                    }
                });
            }
        }
    },

    /** check products quantity*/
    checkproductsquantity:function(request,callback){
        if(request.product != undefined && request.product.length > 0){
            let valid = true;
            let productname = "";
            asyncloop(request.product,function(item1,next1){
                let sqlquery = ""
                if(item1.subproduct_id != "" && item1.subproduct_id != undefined){
                    sqlquery = "SELECT tsp.*,tp.product_name FROM tbl_subproduct as tsp LEFT JOIN tbl_product as tp ON tsp.product_id = tp.id WHERE tsp.id = '"+item1.subproduct_id+"'";
                } else {
                    sqlquery = "SELECT tsp.*,tp.product_name FROM tbl_subproduct as tsp LEFT JOIN tbl_product as tp ON tsp.product_id = tp.id WHERE tsp.product_id = '"+item1.product_id+"'";
                }
                con.query(sqlquery,function(err,result,fields){
                    if(!err && result[0] != undefined){
                        if(parseFloat(result[0].quantity) >= parseFloat(item1.quantity)){
                            next1();
                        } else {
                            valid = false;
                            productname = result[0].product_name;
                            next1();
                        }
                    } else {
                        valid = false;
                        productname = "";
                        next1();
                    }
                });
            },function(){
                callback(valid,productname);
            });
        } else {
            callback(true,"");
        }
    },

    /** check already book this slot or provider*/
    checkalreadybookthisslotorprovider:function(request,callback){
        if(request.date == undefined || request.date == ""){
            request.date = datetime.create().format('Y-m-d');
        }
        if(request.time_slot == undefined || request.time_slot == ""){
            request.time_slot = "00:00";
        }
        con.query("SELECT ab.* FROM tbl_appointment_booking as ab WHERE CONCAT('"+request.date+"',' ','"+request.time_slot+"') >= CONCAT(ab.date,' ',ab.slot_time) AND CONCAT('"+request.date+"',' ','"+request.time_slot+"') < ab.end_datetime AND ab.service_provider_id = '" + request.service_provider_id + "' AND ab.business_location_id = '"+request.business_location_id+"' AND ab.is_deleted = '0' AND ab.booking_status NOT IN ('Cancelled','No Show','Completed','Paid') GROUP BY ab.id ORDER BY ab.id DESC",function(error,result){
            if(!error && result[0] != undefined){
                callback(true);
            } else {
                callback(false);
            }
        });
    },

    /** make booking payment */
    make_userbookingpayment: async function (request, callback) {
        if(request.order_type == 'giftCard'){
            if(request.payment_from == 'ApplePay' || request.payment_from == 'GooglePay'){
                var payment_object = {
                    amount: Math.round(request.total_amount * 100),
                    currency: "USD".toLowerCase(),
                    confirm : true,
                    payment_method : request.transaction_id,
                    capture_method : "automatic",//"manual",
                    description: 'Blookd',
                    statement_descriptor : 'Blookd',
                    statement_descriptor_suffix : 'Blookd',
                    setup_future_usage : 'off_session',
                    confirmation_method : 'automatic',
                    use_stripe_sdk : 'true'
                };
                require("../../../config/payment").createpaymentintents(payment_object,function(respocode,responsmsg,paymentintent){
                    if(respocode == 1){
                        callback(paymentintent, responsmsg,respocode);
                    } else {
                        callback(paymentintent, "Payment has failed due to incorrect details. Please try after some time",respocode);
                    }
                });
            } else {
                callback(null, "Other Payment method is not allow in this api", '0');
            }
        } else {
            con.query("SELECT * FROM tbl_service_provider WHERE id = '"+request.service_provider_id+"'",function(error,spdata,fields){
                if(!error && spdata[0] != undefined && spdata[0].merchant_account_id != ""){
                    if(request.discount == undefined || request.discount == ""){
                        request.discount = 0;
                    }
                    if(request.payment_from != undefined && request.payment_from != "" && request.payment_from == 'Wallet'){
                        var merchant_fees_amount = parseFloat(0);
                    } else {
                        var merchant_fees_amount = (parseFloat(request.total_amount) * parseFloat(spdata[0].merchant_fees) / 100) + parseFloat(spdata[0].merchant_fees_cents);
                    }
                    var app_fees_amount = (parseFloat(request.sub_total) - parseFloat(request.discount)) * (spdata[0].app_fees) / 100;
                    var admin_earning = merchant_fees_amount + app_fees_amount;
                    user.checkproductsquantity(request,function(is_valid,productname){
                        if(is_valid){
                            user.checkalreadybookthisslotorprovider(request,async function(is_alreadybook){
                                if(is_alreadybook){
                                    callback(null, "Service provider already booked for this time duration.", '0');
                                } else {
                                    if(request.payment_from == 'ApplePay' || request.payment_from == 'GooglePay'){
                                      const payment_object = {
                                        amount: Math.round(request.total_amount * 100),
                                        application_fee_amount: Math.round(admin_earning * 100),
                                        transfer_data: { destination: spdata[0].merchant_account_id },
                                        payment_method: request.transaction_id,
                                        capture_method: 'manual',
                                      }
                                      try {
                                        const intent = await createIntent(payment_object)
                                        callback(intent, 'payment intent was created', 1)
                                      } catch (error) {
                                        callback(null, error.message, 0);
                                      }
                                    } else {
                                        callback(null, 'Other Payment method is not allow in this api', '0');
                                    }
                                }
                            });
                        } else {
                            callback(null, "You can't add more quantity for "+productname+" product", '0');
                        }
                    });
                } else {
                    callback(null, "The service provider not added his bank account.", '0');
                }
            });
        }

    },

    /** make booking after tip payment */
    make_useraftertippayment:function(request,callback){
        user.booking_details(request,function(responsecode,resmessage,bookingdetails){
            if(parseFloat(request.tip_amount) > 0){
                if(request.payment_from == 'ApplePay' || request.payment_from == 'GooglePay'){
                    if(bookingdetails.providerdata.merchant_account_id != undefined && bookingdetails.providerdata.merchant_account_id != ""){
                        var payment_object = {
                            amount: Math.round(request.tip_amount * 100),
                            currency: "USD".toLowerCase(),
                            confirm : true,
                            transfer_data:{
                                destination:bookingdetails.providerdata.merchant_account_id
                            },
                            payment_method : request.transaction_id,
                            capture_method : "automatic",//"manual",
                            description: 'Blookd',
                            statement_descriptor : 'Blookd',
                            statement_descriptor_suffix : 'Blookd',
                            setup_future_usage : 'off_session',
                            confirmation_method : 'automatic',
                            use_stripe_sdk : 'true'
                        };
                        require("../../../config/payment").createpaymentintents(payment_object,function(respocode,responsmsg,paymentintent){
                            if(respocode == 1){
                                callback(paymentintent, responsmsg, "18");
                            } else {
                                callback(paymentintent, "Payment has failed due to incorrect details. Please try after some time",respocode);
                            }
                        });
                    } else {
                        callback(null, "The service provider not added his bank account.", '0');
                    }
                } else if(request.payment_from == 'Card') {
                    if(bookingdetails.providerdata.merchant_account_id != undefined && bookingdetails.providerdata.merchant_account_id != ""){
                        con.query("select * from tbl_card_details where id = " + bookingdetails.card_id + "", function(err, result) {
                            if (err) {
                                callback(null, t('restapi_globals_error'), '0');
                            } else if (result[0] == "" || result[0] == undefined) {
                                callback(null, 'User card detail not found', '0');
                            } else {
                                let payment_obj = {
                                    amount: Math.round(request.tip_amount * 100),
                                    currency: "USD",
                                    capture:true,
                                    destination : bookingdetails.providerdata.merchant_account_id,
                                    customer: result[0]['customer_id'],
                                    description: 'For user booking',
                                }
                                require("../../../config/payment").tranferStripePlatform(payment_obj, function(code, msg, stripe_data) {

                                  logger.info({ source: "make_useraftertippayment::tranferStripePlatform", data: stripe_data })

                                    if (stripe_data != null) {
                                        var upddata = {
                                            total_amount : parseFloat(bookingdetails.total_amount) + parseFloat(request.tip_amount),
                                            provider_earning : parseFloat(bookingdetails.provider_earning) + parseFloat(request.tip_amount),
                                            tip_amount : request.tip_amount,
                                            aftertip_payment_status : 'Paid',
                                            aftertip_transaction_id: stripe_data.transaction_id,
                                        }
                                        common.update_data("tbl_appointment_booking",request.appointment_id,upddata,function(is_update){
                                            callback({transaction_id: stripe_data.transaction_id,balance_transaction: (stripe_data.balance_transaction != null && stripe_data.balance_transaction != "" ? stripe_data.balance_transaction : ""),receipt_url:(stripe_data.receipt_url != "" && stripe_data.receipt_url != undefined) ? stripe_data.receipt_url : ""},"Payment make successfully","1");
                                        });
                                    } else {
                                        callback(stripe_data, msg, code);
                                    }
                                });
                            }
                        });
                    } else {
                        callback(null, "The service provider not added his bank account.", '0');
                    }
                } else if(request.payment_from == 'Wallet') {
                    user.get_user_detail(request.user_id, function(user_detail) {
                        if(parseFloat(user_detail.wallet_amount) >= parseFloat(request.tip_amount)){
                            var wallet_insert = {
                                user_id: request.user_id,
                                card_id: 0,
                                order_id: request.appointment_id,
                                amount: request.tip_amount,
                                title: 'appointment booking after tip amount',
                                transaction_id: '',
                                status: 'debit'
                            }
                            user.insert_wallet_history(wallet_insert, function(is_wallet_insert){
                                var upddata = {
                                    total_amount : parseFloat(bookingdetails.total_amount) + parseFloat(request.tip_amount),
                                    provider_earning : parseFloat(bookingdetails.provider_earning) + parseFloat(request.tip_amount),
                                    tip_amount : request.tip_amount,
                                    aftertip_payment_status : 'Paid',
                                    aftertip_transaction_id: "BLOOKD"+moment().utc().format("YYYYMMDDHHiiss"),
                                }
                                common.update_data("tbl_appointment_booking",request.appointment_id,upddata,function(is_update){
                                    callback({transaction_id:"BLOOKD"+moment().utc().format("YYYYMMDDHHiiss"),balance_transaction: request.wallet_amount,receipt_url:""},"Payment make successfully","1");
                                });
                            });
                        } else {
                            callback(null, 'Insufficient fund in your Wallet. wallet payment is not possible.', '0');
                        }
                    });
                } else {
                    callback(null, "Other Payment method is not allow in this api", '0');
                }
            } else {
                var upddata = {
                    total_amount : parseFloat(bookingdetails.total_amount) + parseFloat(request.tip_amount),
                    provider_earning : parseFloat(bookingdetails.provider_earning) + parseFloat(request.tip_amount),
                    tip_amount : request.tip_amount,
                    aftertip_payment_status : 'Paid',
                    aftertip_transaction_id: "BLOOKD"+moment().utc().format("YYYYMMDDHHiiss"),
                }
                common.update_data("tbl_appointment_booking",request.appointment_id,upddata,function(is_update){
                    callback({transaction_id:"BLOOKD"+moment().utc().format("YYYYMMDDHHiiss"),balance_transaction: request.wallet_amount,receipt_url:""},"Payment make successfully","1");
                });
            }
        });
    },

    /** Done booking after tip payment */
    done_useraftertippayment:function(request,callback){
        user.booking_details(request,function(responsecode,resmessage,bookingdetails){
            var upddata = {
                total_amount : parseFloat(bookingdetails.total_amount) + parseFloat(request.tip_amount),
                provider_earning : parseFloat(bookingdetails.provider_earning) + parseFloat(request.tip_amount),
                tip_amount : request.tip_amount,
                aftertip_payment_status : 'Paid',
                aftertip_transaction_id: request.transaction_id,
            }
            common.update_data("tbl_appointment_booking",request.appointment_id,upddata,function(is_update){
                callback(null,"Payment make successfully","1");
            });
        });
    },

    /** Booking*/
    quick_checkout_booking: async function (request, callback) {
      con.query("SELECT * FROM tbl_service_provider WHERE id = '" + request.service_provider_id + "'", function (error, spdata, fields) {
        if (error || spdata[0] === undefined || spdata[0].merchant_account_id === "") {
          return callback(null, "The service provider not added his bank account.", '0');
        }

        user.checkproductsquantity(request, function (is_valid, productname) {
          if (!is_valid) {
            return callback(null, "You can't add more quantity for " + productname + " product", '0')
          }

          require("../service_provider/service_provider_model").generate_booking_id("tbl_appointment_booking", function (booking_id) {
            user.get_user_detail(request.user_id, function (user_detail) {
              var clientparam = {
                service_provider_id: request.service_provider_id,
                profile_image: user_detail.image_name,
                customer_name: user_detail.full_name,
                phone: user_detail.phone,
                country_code: user_detail.country_code,
                email: user_detail.email,
                is_user_app: request.user_id,
                notes: ""
              }
              var enddatetime = moment.utc(request.date + " " + request.time_slot, 'YYYY-MM-DD HH:mm:ss')
                                .add(request.total_duration, 'minutes')
                                .add(request.additional_duration || 0, 'minutes')
                                .format("YYYY-MM-DD HH:mm:ss")

              user.add_client(clientparam, function (is_client_add) {
                if (request.discount == undefined || request.discount == "") {
                  request.discount = 0
                }

                let merchant_fees_amount = 0

                // We all got confused between remaining_amount and total_amount at some point;
                // fast forward to this moment, frontend sends a remaining_amount that is basically
                // a total_amount (because we thought to do stripe payment later instead of here).
                // So now, keeping remainig_amount as is but assign it to a total_amount - this will
                // help adding new products to an existing order to differentiate between the total amount
                // value at the time of booking and post-service amount. Or something like that idk.
                if (parseFloat(request.total_amount) <= 0 && parseFloat(request.remaining_amount) > 0) {
                    request.total_amount = request.remaining_amount
                }

                if (request.payment_from != undefined && request.payment_from != "" && request.payment_from == 'Wallet') {
                  merchant_fees_amount = parseFloat(0);
                } else {
                  const amount = parseFloat(request.total_amount) > 0 ? parseFloat(request.total_amount) : parseFloat(request.remaining_amount)
                  merchant_fees_amount = (amount * parseFloat(spdata[0].merchant_fees) / 100) + parseFloat(spdata[0].merchant_fees_cents)
                }

                const app_fees_amount = (parseFloat(request.sub_total) - parseFloat(request.discount)) * (spdata[0].app_fees) / 100;
                const admin_earning = merchant_fees_amount + app_fees_amount;
                const new_booking = {
                  booking_id: booking_id,
                  service_provider_id: request.service_provider_id,
                  business_location_id: request.business_location_id,
                  customer_id: '0',
                  user_id: request.user_id,
                  customer_name: '',
                  date: request.date,
                  slot_time: request.time_slot,
                  end_datetime: enddatetime,
                  total_duration: request.total_duration,
                  additional_duration: request.additional_duration || 0,
                  description: request.description ? request.description : "",
                  booking_status: 'Accepted',
                  payment_status: request.payment_mode == 'pay_later' && request.transaction_id ? 'unpaid' : 'paid',
                  booking_type: 'online',
                  card_id: request.card_id ? request.card_id : "0",
                  is_tip_after_service: request.is_tip_after_service ? request.is_tip_after_service : 0,
                  payment_mode: request.payment_mode ? request.payment_mode : "pay_later",
                  payment_from: request.payment_from ? request.payment_from : "Card",
                  payment_intent_id: request.payment_intent_id ? request.payment_intent_id : null,
                  promocode: request.promocode ? request.promocode : "",
                  sub_total: request.sub_total,
                  wallet_amount: request.wallet_amount ? request.wallet_amount : 0,
                  total_amount: parseFloat(request.total_amount) > 0 ? parseFloat(request.total_amount) : request.wallet_amount || 0,
                  remaining_amount: (request.remaining_amount != undefined && request.remaining_amount != "") ? request.remaining_amount : null,
                  discount: (request.discount != "" && request.discount != undefined) ? request.discount : 0,
                  tip_amount: request.tip || 0,
                  tax: request.tax,
                  merchant_fees: spdata[0].merchant_fees,
                  merchant_fees_amount: merchant_fees_amount,
                  merchant_fees_cents: spdata[0].merchant_fees_cents,
                  app_fees: spdata[0].app_fees,
                  app_fees_amount: app_fees_amount,
                  admin_earning: admin_earning,
                  provider_earning: (request.total_amount != undefined && parseFloat(request.total_amount) > 0) ? parseFloat(request.total_amount) - admin_earning : parseFloat(request.wallet_amount || 0) - admin_earning,
                  is_reported: '0',
                  is_deleted: '0',
                  updatetime: datetime.create().format('Y-m-d H:M:S'),
                  insertdate: datetime.create().format('Y-m-d H:M:S'),
                }
                if (new_booking.total_amount <= 0)
                  new_booking.payment_mode = "pay_later"

                if (!new_booking.provider_earning || new_booking.provider_earning < 0)
                  new_booking.provider_earning = 0

                if (!request.service && request.product != "") {
                  new_booking.is_product = '1'
                }
                user.checkalreadybookthisslotorprovider(request, function (is_alreadybook) {
                  if (is_alreadybook) {
                    return callback(null, "Service provider already booked for this time duration.", '0')
                  }

                  common.single_insert_data('tbl_appointment_booking', new_booking, function (appointment_id) {
                    var appointmentId = appointment_id.insertId;
                    if (!appointmentId) {
                      return callback(null, t('restapi_globals_error'), '0')
                    }

                    request.merchant_account_id = spdata[0].merchant_account_id
                    request.admin_earning = admin_earning
                    user.booking_details(request, function (responsecode, resmessage, bookingdetails) {
                        return callback(bookingdetails, t('restapi_service_booked'), '1')
                    })
                  }) // common.single_insert_data
                }) // user.checkalreadybookthisslotorprovider
              }) // user.add_client
            })
          })
        })
      })
    },

    /** Function for add booking details*/
    addbookingdetails: function(request, callback) {
        if (request.service) {
            asyncloop(request.service, function(item, next) {
                if (item) {
                    var appointment_detail = {
                        appointment_id: request.appointment_id,
                        booking_id: request.booking_id,
                        service_provider_id: request.service_provider_id,
                        service_id: item.service_id,
                        price: item.price,
                        type: 'service',
                        product_id: '0',
                        quantity: '0',
                        service_status: 'Accepted',
                        is_deleted: '0',
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                    }
                    common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function(is_appointment_detail) {
                        next();
                    });
                } else {
                    next();
                }
            }, function() {
                if (request.product) {
                    asyncloop(request.product, function(item1, next1) {
                        if (item1) {
                            var appointment_detail = {
                                appointment_id: request.appointment_id,
                                booking_id: request.booking_id,
                                service_provider_id: request.service_provider_id,
                                product_id: item1.product_id,
                                subproduct_id: (item1.subproduct_id != "" && item1.subproduct_id != undefined) ? item1.subproduct_id : '0',
                                price: item1.price,
                                type: 'product',
                                service_id: '0',
                                quantity: item1.quantity,
                                service_status: 'Accepted',
                                is_deleted: '0',
                                updatetime: datetime.create().format('Y-m-d H:M:S'),
                                insertdate: datetime.create().format('Y-m-d H:M:S'),
                            }
                            common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function(is_appointment_detail) {
                                common.credit_wallet("tbl_subproduct","id = '"+appointment_detail.subproduct_id+"' AND product_id = '"+appointment_detail.product_id+"'","used_quantity",item1.quantity,function(balance){
                                    common.debit_wallet("tbl_subproduct","id = '"+appointment_detail.subproduct_id+"' AND product_id = '"+appointment_detail.product_id+"'","remaining_quantity",item1.quantity,function(balance){
                                        next1();
                                    });
                                });
                            });
                        } else {
                            next1();
                        }
                    }, function() {
                        callback(true);
                    });
                } else {
                    callback(true);
                }
            });
        } else {
            if (request.product) {
                asyncloop(request.product, function(item1, next1) {
                    if (item1) {
                        var appointment_detail = {
                            appointment_id: request.appointment_id,
                            booking_id: request.booking_id,
                            service_provider_id: request.service_provider_id,
                            product_id: item1.product_id,
                            subproduct_id: (item1.subproduct_id != "" && item1.subproduct_id != undefined) ? item1.subproduct_id : '0',
                            price: item1.price,
                            type: 'product',
                            service_id: '0',
                            quantity: item1.quantity,
                            service_status: 'Accepted',
                            is_deleted: '0',
                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                            insertdate: datetime.create().format('Y-m-d H:M:S'),
                        }
                        common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function(is_appointment_detail) {
                            common.credit_wallet("tbl_subproduct","id = '"+appointment_detail.subproduct_id+"' AND product_id = '"+appointment_detail.product_id+"'","used_quantity",item1.quantity,function(balance){
                                common.debit_wallet("tbl_subproduct","id = '"+appointment_detail.subproduct_id+"' AND product_id = '"+appointment_detail.product_id+"'","remaining_quantity",item1.quantity,function(balance){
                                    next1();
                                });
                            });
                        });
                    } else {
                        next1();
                    }
                }, function() {
                    callback(true);
                });
            } else {
                callback(true);
            }
        }
    },

    /**Function for add client */
    add_client: function(client_data, callback) {
        con.query("select * from tbl_service_provider_client where service_provider_id = " + client_data.service_provider_id + " AND is_user_app = " + client_data.is_user_app + " AND phone = '" + client_data.phone + "' AND email = '" + client_data.email + "'", function(err, result) {
            if (!err && result[0] != undefined && result[0] != "") {
                callback(true)
            } else {
                common.single_insert_data('tbl_service_provider_client', client_data, function(is_client) {
                    if (is_client) {
                        callback(true)
                    } else {
                        callback(false)
                    }
                })
            }
        })
    },
   service_provider_detail: function(request, callback) {
        con.query(`
          SELECT
            *,
            CONCAT('${global.S3_BUCKET_ROOT}${global.SP_IMAGE}', s.profile_image) AS profile_image,
            IFNULL(
              (
                SELECT b.status
                FROM tbl_bookmark b
                WHERE b.service_provider_id = ${request.service_provider_id}
                AND b.user_id = ${request.user_id}
              ),
              '0'
            ) AS bookmark_status,
            GET_SERVICE_PROVIDER_RATING(s.id, null) AS ratting,
            GET_SERVICE_PROVIDER_REVIEW_COUNT(s.id, null) AS review,
            (
              SELECT id
              FROM tbl_business_location
              WHERE service_provider_id = s.id
              AND NOT is_deleted
              ORDER BY id
              LIMIT 1
            ) AS business_id,
            (
              SELECT timezone_code
              FROM tbl_business_location
              WHERE service_provider_id = s.id
              AND NOT is_deleted
              ORDER BY id
              LIMIT 1
            ) AS business_timezone_code,
            (
              SELECT time_slot_duration_minutes
              FROM tbl_business_location
              WHERE service_provider_id = s.id
              AND NOT is_deleted
              ORDER BY id
              LIMIT 1
            ) AS business_time_slot_duration_minutes,
            CONCAT('${global.base_url_without_api}home/share-provider/', s.id) AS share_url
          FROM tbl_service_provider s
          WHERE s.id = ${request.service_provider_id}
          AND NOT s.is_deleted
        `, function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    require("../service_provider/service_provider_model").getspsliderimages(request.service_provider_id,function(rescode,resmsg,sliderimages){
                        if(rescode == '1' && sliderimages != null){
                            result[0].sliderimages = sliderimages;
                        } else {
                            result[0].sliderimages = [];
                        }
                        con.query(`
                          SELECT *
                          FROM tbl_bookmark
                          WHERE user_id = ${request.user_id}
                          AND service_provider_id = ${request.service_provider_id}
                          AND status
                        `, function(err2,result2){
                            result[0].is_follower = (!err2 && result2[0]) ? 1 : 0;
                            con.query(`
                              SELECT COUNT(b.id) AS total_following
                              FROM tbl_bookmark AS b
                              JOIN tbl_user AS tu ON b.user_id = tu.id
                              WHERE b.service_provider_id = ${request.service_provider_id}
                              AND tu.status = 'Active'
                              AND NOT tu.is_deleted
                              AND b.status
                            `, function(err1,result1){
                                result[0].followers = (!err1 && result1[0] != undefined) ? result1[0].total_following : 0;
                                result[0].following = 0;
                                require("../service_provider/service_provider_model").story_list(request.service_provider_id, function(code, msg, story) {
                                    result[0].story = story;
                                    require("../service_provider/service_provider_model").all_service(request, function(services) {
                                        result[0].service = services;
                                        require("../service_provider/service_provider_model").post_list(request, function(post_list) {
                                            result[0].post_list = post_list;
                                            con.query(`
                                              SELECT *
                                              FROM tbl_business_location
                                              WHERE service_provider_id = ${request.service_provider_id}
                                              AND is_deleted = '0'
                                            `, function(error, results) {
                                                con.query(`
                                                  SELECT
                                                    ur.*,
                                                    DATE_FORMAT(ur.insertdate, '%Y-%m-%d %H:%i:%s') AS review_date,
                                                    u.first_name,
                                                    u.last_name,
                                                    CONCAT('${global.S3_BUCKET_ROOT}${global.USER_IMAGE}', u.profile_image) AS profile_image,
                                                    IFNULL(
                                                      (
                                                        SELECT JSON_ARRAYAGG(s.service_name)
                                                        FROM tbl_appointment_booking_detail d
                                                        JOIN tbl_service s ON d.service_id = s.id
                                                        WHERE d.appointment_id = ur.order_id
                                                        AND LOWER(d.type) = 'service'
                                                      ),
                                                      '[]'
                                                    ) AS services_json
                                                  FROM tbl_user_review ur
                                                  LEFT JOIN tbl_user u ON u.id = ur.user_id
                                                  WHERE ur.service_provider_id = ${request.service_provider_id}
                                                  AND ur.ratting > 0
                                                  ORDER BY ur.id DESC
                                                `, function(err, reviewslist) {
                                                    result[0].reviews = (!err && reviewslist.length > 0) ?
                                                      reviewslist.map(it => ({ ...it, services: JSON.parse(it.services_json) })) :
                                                      [];
                                                    if (!error && results[0] != "" && results[0] != undefined) {
                                                        result[0].location = results;
                                                    } else {
                                                        result[0].location = [];
                                                    }
                                                    callback(result[0], 'Service provider detail found successfully', '1');
                                                });
                                            });
                                        });
                                    });
                                });
                            });
                        });
                    });
                } else {
                    callback(null, t('restapi_vendor_notfound'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },
  add_update_bookmark: (request, callback) => {
    con.query(`
      SELECT *
      FROM tbl_bookmark
      WHERE user_id = ${ request.user_id }
      AND service_provider_id = ${ request.service_provider_id }
    `, (err, result) => {
      if (!err) {
        if (result[0]) {
          common.update_data(
            'tbl_bookmark',
            result[0].id,
            { status: request.status },
            (is_bookmark) => {
              if (is_bookmark) {
                common.select_data(
                  'tbl_bookmark',
                  ` id = ${ result[0].id } `,
                  (bookmark_data) => {
                    if (bookmark_data) {
                      callback(bookmark_data, 'Bookmark status changed successfully', '1')
                    } else {
                      callback(null, t('restapi_globals_error'), '0')
                    }
                  })
              } else {
                callback(null, t('restapi_globals_error'), '0')
              }
            })
        } else {
          common.single_insert_data(
            'tbl_bookmark',
            {
              user_id: request.user_id,
              service_provider_id: request.service_provider_id,
              status: 1,
            },
            (is_bookmark_added) => {
              if (is_bookmark_added) {
                common.select_data(
                  'tbl_bookmark',
                  ` id = ${ is_bookmark_added.insertId } `,
                  (bookmark_data) => {
                    if (bookmark_data) {
                      callback(bookmark_data, 'Bookmark created successfully', '1')
                    } else {
                      callback(null, t('restapi_globals_error'), '0')
                    }
                  })
              } else {
                callback(null, 'Bookmark can not be added please try after some time', '0')
              }
            })
        }
      } else {
        callback(null, t('restapi_globals_error'), '0')
      }
    })
  },
    add_card: function(request, callback) {
        con.query("SELECT * FROM tbl_user WHERE id = '" + request.user_id + "' ", function(err, userprofile, fields) {
            var cardobject = {
                "name": request.holder_name,
                "number": request.number,
                "exp_month": request.expiry_month,
                "exp_year": request.expiry_year,
                "cvc": request.cvv,
            }
            require("../../../config/payment").createCardToken(cardobject, function(responsecode, responsemsg, token) {
                if (token != null) {
                    con.query("SELECT * FROM `tbl_card_details` WHERE user_id='" + request.user_id + "' AND is_deleted = '0' AND status = 'Active' AND primary_card = '1'",function(carderr,oldprimarycard){
                        request.primary_card = (!carderr && oldprimarycard[0] != undefined) ? request.primary_card : '1';
                        con.query("select * from tbl_card_details where is_deleted='0' AND user_id='" + request.user_id + "' AND fingerprint='" + token.card.fingerprint + "'", function(err, result) {
                            if (!err && result.length > 0) {
                                callback(0, t('text_card_already_exists'), result[0]);
                            } else {
                                var card_data = {
                                    holder_name: request.holder_name,
                                    user_id: request.user_id,
                                    number: token.card.last4,
                                    expiry_month: request.expiry_month,
                                    expiry_year: request.expiry_year,
                                    cvv: request.cvv,
                                    primary_card: (request.primary_card == undefined || request.primary_card == '') ? '0' : request.primary_card,
                                    fingerprint: token.card.fingerprint,
                                    card_id: token.card.id,
                                    card_token: token.id,
                                    type: token.card.brand,
                                }
                                con.query("INSERT INTO `tbl_card_details` SET ?", card_data, function(error, result, field) {
                                    if (error) {
                                        callback('0', t('rest_keywords_card_insert_failed'), null);
                                    } else {
                                        var customerObject = {
                                            source: token.id,
                                            email: userprofile[0].email,
                                            description: Global.APP_NAME + " User #" + request.user_id,
                                        }
                                        require("../../../config/payment").createCustomer(customerObject, function(customercode, customermsg, customer) {
                                            if (customer != null) {
                                                con.query("UPDATE tbl_card_details  set customer_id='" + customer.id + "' where id=" + result.insertId + " ", function(upderror, updresult, fields) {
                                                    if (!upderror) {
                                                        user.card_list({ "id": result.insertId, "user_id": request.user_id }, function(card_code, card_message, card_result) {
                                                            if (card_code == 1) {
                                                                callback('1', t("rest_keywords_card_insert_success"), card_result[0]);
                                                            } else {
                                                                callback('0', card_message, null);
                                                            }
                                                        })
                                                    } else {
                                                        common.delete_data("tbl_card_details","id='" + result.insertId + "'",function(is_remove){
                                                            callback('0', t("text_card_add_fail"), null);
                                                        });
                                                    }
                                                })
                                            } else {
                                                common.delete_data("tbl_card_details","id='" + result.insertId + "'",function(is_remove){
                                                    callback(customercode, customermsg, null);
                                                });
                                            }
                                        });
                                    }
                                });
                            }
                        });
                    });
                } else {
                    callback('0', responsemsg, token);
                }
            })
        })
    },

    /**Function get card list */
    card_list: function(request, callback) {
        const condition = request.id ? ` AND id = ${request.id} ` : '';
        const sql = "SELECT *, CONCAT('************',number) AS number FROM `tbl_card_details` WHERE is_deleted = '0' AND status = 'Active' AND user_id = ? " + condition;
        con.query(sql, request.user_id, function(error, result, fields) {
            if (!error && result.length > 0) {
                callback('1', t('rest_keywords_card_list_success'), result);
            } else if (!error && result.length == 0) {
                callback('0', t('rest_keywords_card_list_failed'), result);
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**Function for delete and update card */
    update_card: function(request, callback) {
        if (request.type == 'delete') {
            user.delete_card(request, function(code, message, response) {
                callback(code, message, response);
            })
        } else {
            var update_param = {
                primary_card: '0'
            }
            var condition = "is_deleted = '0'";
            common.update_data_condition('tbl_card_details', condition, update_param, function(is_update) {
                if (is_update) {
                    con.query("UPDATE `tbl_card_details` SET `primary_card`= '1' WHERE id = ?", request.card_id, function(error, result) {
                        if (!error) {
                            callback("1", 'Card updated successfully', null);
                        } else {
                            callback('0', t('restapi_globals_error'), null);
                        }
                    })
                } else {
                    callback('0', t('restapi_globals_error'), null);
                }
            })
        }
    },

    /**Function remove card list */
    delete_card: function(request, callback) {
        con.query("UPDATE tbl_card_details SET is_deleted= 1 WHERE id = ?", request.card_id, function(error) {
            if (!error) {
                callback("1", 'Card deleted successfully', null);
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },
    /** Function for my product booking*/
    my_product_booking: function(request, user_id, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        var condition = "ab.user_id = " + user_id + " AND ab.is_only_product = '1'";
        if (request.word != undefined && request.word != "") {
            condition += " AND (p.product_name LIKE '%" + request.word + "%') ";
        }
        if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
            condition += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"' ";
        } else {
            if(request.start_date != undefined && request.start_date != ""){
                condition += " AND '"+request.start_date+"' >= ab.date ";
            }
            if(request.end_date != undefined && request.end_date != ""){
                condition += " AND '"+request.end_date+"' <= ab.date ";
            }
        }
        con.query("select ab.id as appointment_id,p.product_name,abd.quantity,date_format(ab.date,'%Y-%m-%d') as date,date_format(ab.end_datetime, '%Y-%m-%d %H:%i:%s') as end_datetime,ab.slot_time,(select price from tbl_subproduct ts where ts.product_id = p.id LIMIT 1) as price,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',p.product_image) as product_image,(select pc.name from tbl_product_category pc where pc.id = p.category_id) as product_category from tbl_appointment_booking ab INNER JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id LEFT JOIN tbl_product p ON p.id = abd.product_id where " + condition + " group by ab.id ORDER BY DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i') DESC LIMIT " + limit + ", " + per_page + "", function(error, products) {
            if (!error) {
                if (products[0] != "" && products[0] != undefined) {
                    callback('1', t('restapi_productfound'), products);
                } else {
                    callback('0', t('restapi_productnotfound'), []);
                }
            } else {
                callback('0', t('restapi_globals_error') + error, null);
            }
        });
    },

    /** Function for favourite list*/
    favourite_list: function(request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;

        var condition = "b.user_id = " + request.user_id + " AND b.status = '1' AND s.is_deleted = '0'";
        var query = con.query("select b.service_provider_id as id,s.first_name,GET_SERVICE_PROVIDER_RATING(s.id, null) AS ratting, GET_SERVICE_PROVIDER_REVIEW_COUNT(s.id, null) AS review,s.last_name,s.id as service_provider_id,b.status as bookmark_status,CONCAT('" + global.S3_BUCKET_ROOT + global.SP_IMAGE + "','',s.profile_image) as profile_image,bl.name as business_address from tbl_bookmark b INNER JOIN tbl_service_provider s ON s.id = b.service_provider_id LEFT JOIN tbl_business_location bl ON bl.service_provider_id = s.id where " + condition + " group by id LIMIT " + limit + ", " + per_page + " ", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    var rows = 0;
                    asyncloop(result,function(item,next){
                        require("../service_provider/service_provider_model").getspsliderimages(item.service_provider_id,function(rescode,resmessage,sliderimages){
                            if(rescode == '1' && sliderimages != null){
                                result[rows].sliderimages = sliderimages;
                            } else {
                                result[rows].sliderimages = [];
                            }
                            rows++;
                            next();
                        });
                    },function(){
                        callback('1', "Favourite list found successfully", result);
                    });
                } else {
                    callback('0', "Please add product or service provider in favourite", []);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**Function for get product favourite list */
    product_favourite_list: function(request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        var condition = "b.user_id = " + request.user_id + " AND b.status = '1'";
        var sql = con.query("select b.user_id,b.type,b.type_id,b.status as bookmark_status from tbl_product_bookmark b where " + condition + " LIMIT " + limit + ", " + per_page + "", function(err1, result1) {
            if (!err1) {
                if (result1 != "" && result1 != undefined) {
                    asyncloop(result1, function(item, next) {
                        if (item) {
                            if (item.type == 'service') {
                                con.query("select *,0.0 as ratting,id as service_id,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',service_image) as service_image from tbl_service where id = " + item.type_id + "", function(error, services) {
                                    if (!error && services[0] != undefined && services[0] != "") {
                                        item.service_id = item.type_id;
                                        item.id = item.type_id;
                                        item.product_image = services[0].service_image;
                                        item.name = services[0].service_name;
                                        item.ratting = services[0].ratting;
                                        item.review = 0;
                                        item.price = services[0].price;
                                        next();
                                    } else {
                                        item.service_id = item.type_id;
                                        item.id = item.type_id;
                                        item.service_image = "";
                                        item.service_name = "";
                                        item.ratting = 0;
                                        item.review = 0;
                                        item.price = 0;
                                        next();
                                    }
                                })
                            }
                            if (item.type == 'product') {
                                con.query("select p.*,p.id as product_id,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',p.product_image) as product_image,(select price from tbl_subproduct s where s.product_id = " + item.type_id + " AND status = 'Active' LIMIT 1) as price from tbl_product p where p.publish = 'Yes' AND p.id = " + item.type_id + "", function(error, products) {
                                    if (!error && products[0] != undefined && products[0] != "") {
                                        item.product_id = item.type_id;
                                        item.id = item.type_id;
                                        item.product_image = products[0].product_image;
                                        item.name = products[0].product_name;
                                        item.ratting = 0;
                                        item.review = 0;
                                        item.price = products[0].price;
                                        next();
                                    } else {
                                        item.product_id = item.type_id;
                                        item.id = item.type_id;
                                        item.product_image = "";
                                        item.name = "";
                                        item.ratting = 0;
                                        item.review = 0;
                                        item.price = 0;
                                        next();
                                    }
                                })
                            }
                        } else {
                            next();
                        }
                    }, function() {
                        callback('1', "Favourite list found successfully", result1);

                    })
                } else {
                    callback('0', "Please add product or service provider in favourite", []);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },
    giftcard_list: function(user_id, callback) {
      con.query(`
        SELECT
          *,
          CONCAT('${ global.S3_BUCKET_ROOT }${ global.ADMIN_IMAGE }', image) AS image_path,
          CURDATE() AS today_date
        FROM tbl_giftcard
        WHERE is_active
        AND NOT is_deleted
        ORDER BY id DESC
      `,
      function(err, result) {
        if (err) {
          callback(0, t('restapi_globals_error'), null);
        } else if (result[0]) {
          callback(1, 'Gift card list found successfully', result);
        } else {
          callback(0, 'No gift card found', null);
        }
      })
    },
    my_giftcard: function(user_id, callback) {
      con.query(`
        SELECT
          go.id AS giftcard_id,
          g.name,
          go.valid_till,
          go.price,
          go.total_amount,
          g.description,
          g.image,
          go.giftcard_code,
          CONCAT('${ global.S3_BUCKET_ROOT }${ global.ADMIN_IMAGE }', g.image) AS image_path,
          CONCAT('${global.BASE_URL}', 'share-giftcard/', to_base64(go.id)) AS shareurl
        FROM tbl_giftcard_order go 
        LEFT JOIN tbl_giftcard g ON g.id = go.giftcard_id 
        WHERE go.user_id = ${user_id}
        AND NOT go.is_used
        ORDER BY go.id DESC
      `,
      function(err, result) {
        if (err) {
          callback(0, t('restapi_globals_error'), null);
        } else if (result[0]) {
          callback(1, 'Gift card list found successfully', result);
        } else {
          callback(0, 'No gift card found', null);
        }
      })
    },
    giftcard_detail: function(request, callback) {
      con.query(`
        SELECT
          id AS giftcard_id,
          purchase_date,
          transaction_id,
          giftcard_code,
          valid_till,
          price,
          total_amount
        FROM tbl_giftcard_order
        WHERE id = ${request.giftcard_id}
        ORDER BY id DESC
      `,
      function(err, result) {
        if (err) {
          callback(0, t('restapi_globals_error') + err, null);
        } else if (result[0]) {
          callback(1, 'Gift card detail found successfully', result[0]);
        } else {
          callback(0, 'No gift card found', null);
        }
      })
    },
    /**Function for redeem gift card */
    redeem_giftcard: function(request, callback) {
        var sql = con.query("select * from tbl_giftcard_order where giftcard_code = '" + request.giftcard_code + "'", function(err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else if (result[0] == "" || result[0] == undefined) {
                callback('0', 'You have entered invalid gift card code.', null);
            } else if (result[0].is_used == '1') {
                callback('0', 'You have already used this Gift Card.', null);
            } else {
                con.query("UPDATE tbl_giftcard_order SET is_used= '1' WHERE id = ?", result[0].id, function(error, result1) {
                    if (!error) {
                        var wallet_insert = {
                            user_id: request.user_id,
                            card_id: 0,
                            order_id: result[0].giftcard_id,
                            amount: result[0].price,
                            title: 'giftcard redeem',
                            transaction_id: '',
                            status: 'credit'
                        }
                        common.single_insert_data('tbl_wallet_history', wallet_insert, function(is_history) {
                            if (is_history) {
                                user.wallet_amount(request.user_id, function(walletAmount) {
                                    //UPDATE tbl_user SET wallet_amount = walletAmount + "+result[0].amount+" WHERE id = '" + req.wallet_id + "'
                                    var updated_amount = walletAmount.wallet_amount + result[0].price;
                                    con.query("UPDATE tbl_user SET wallet_amount = " + updated_amount + " WHERE id = ?", request.user_id, function(error, result) {
                                        callback('1', 'Wallet balanced updated successfully', updated_amount);
                                    })
                                })
                            } else {
                                con.query("UPDATE tbl_giftcard_order SET is_used= '0' WHERE id = ?", result[0].id, function(error, result) {
                                    callback('0', t('restapi_globals_error'), null);
                                })
                            }
                        })
                    } else {
                        callback('0', t('restapi_globals_error'), null);
                    }
                })
            }
        })
    },

    /**Function for purchase giftcard */
    purchase_giftcard: function(request, callback) {
        if(request.payment_from == 'Card'){
            var sql = con.query("select * from tbl_card_details where id = " + request.card_id + "", function(err, result) {
                if (err) {
                    callback(null, t('restapi_globals_error'), '0');
                } else if (result[0] == "" || result[0] == undefined) {
                    callback(null, 'User card detail not found', '0');
                } else {
                    let payment_obj = {
                        amount: Math.round(request.total_amount * 100),
                        currency: "USD",
                        // source: card.card_token,
                        customer: result[0]['customer_id'],
                        description: 'For purchase giftcard',
                    }
                    require("../../../config/payment").tranferStripePlatform(payment_obj, function(code, msg, stripe_data) {
                        user.giftcard_code(function(giftcardCode) {
                            if (stripe_data != "") {
                                var new_booking = {
                                    transaction_id: stripe_data.transaction_id,
                                    giftcard_id: request.giftcard_id,
                                    giftcard_code: giftcardCode,
                                    balance_transaction: (stripe_data.balance_transaction != null && stripe_data.balance_transaction != "" ? stripe_data.balance_transaction : ""),
                                    receipt_url: (stripe_data.receipt_url != "" && stripe_data.receipt_url != undefined) ? stripe_data.receipt_url : "",
                                    user_id: request.user_id,
                                    purchase_date: datetime.create().format('Y-m-d'),
                                    valid_till: moment().add(360, 'days').format('YYYY-MM-DD'),
                                    card_id: (request.card_id != "" && request.card_id != undefined) ? request.card_id : "0",
                                    price: request.sub_total,
                                    sub_total: request.sub_total,
                                    transaction_charge: request.transaction_charge,
                                    total_amount: request.total_amount,
                                    payment_from:request.payment_from,
                                    used_by_id: '0',
                                    is_used: '0',
                                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                                    insertdate: datetime.create().format('Y-m-d H:M:S'),
                                }
                                common.single_insert_data('tbl_giftcard_order', new_booking, function(appointment_id) {
                                    if (appointment_id) {
                                        callback(true, 'Giftcard purchased successfully', '1');
                                    } else {
                                        callback(null, t('restapi_globals_error'), '0');
                                    }
                                })
                            } else {
                                callback(stripe_data, msg, code);
                            }
                        })
                    })
                }
            })
        } else {
            user.giftcard_code(function(giftcardCode) {
                var new_booking = {
                    transaction_id: request.transaction_id,
                    giftcard_id: request.giftcard_id,
                    giftcard_code: giftcardCode,
                    balance_transaction:  "",
                    receipt_url: "",
                    user_id: request.user_id,
                    purchase_date: datetime.create().format('Y-m-d'),
                    valid_till: moment().add(360, 'days').format('YYYY-MM-DD'),
                    card_id: "0",
                    price: request.sub_total,
                    sub_total: request.sub_total,
                    transaction_charge: request.transaction_charge,
                    total_amount: request.total_amount,
                    payment_from:request.payment_from,
                    used_by_id: '0',
                    is_used: '0',
                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                    insertdate: datetime.create().format('Y-m-d H:M:S'),
                }
                common.single_insert_data('tbl_giftcard_order', new_booking, function(appointment_id) {
                    if (appointment_id) {
                        callback(true, 'Giftcard purchased successfully', '1');
                    } else {
                        callback(null, t('restapi_globals_error'), '0');
                    }
                })
            });
        }
    },

    /**Function for generate goftcard code */
    giftcard_code: function(callback) {
        // var code = randomstring.generate({ length: 15, charset: 'numeric' });
        common.Random_string(12, function(giftcode) {
            con.query("SELECT giftcard_code FROM tbl_giftcard_order WHERE giftcard_code = '" + giftcode + "'", function(err, result, fields) {
                if (!err) {
                    if (result.length <= 0) {
                        callback(giftcode);
                    } else {
                        user.giftcard_code(function(response) {});
                    }
                } else {
                    callback(null, t('restapi_globals_error'), '0');
                }
            });
        });
    },

    /**Function for get wallet amount */
    wallet_amount: function(user_id, callback) {
        con.query("select wallet_amount from tbl_user where id = " + user_id + "", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback({wallet_amount:result[0].wallet_amount}, 'Wallet amount found successfully', '1');
                } else {
                    callback({wallet_amount:0.00}, 'Wallet amount not found', '0');
                }
            } else {
                callback({wallet_amount:0.00}, t('restapi_globals_error'), '0');
            }
        });
    },

    /**Function for wallet history */
    wallet_history: function(request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        con.query("select *,date_format(`insertdate`, '%Y-%m-%d') as date,date_format(`insertdate`, '%H:%m') as time from tbl_wallet_history where user_id = " + request.user_id + " ORDER BY ID DESC LIMIT " + limit + ", " + per_page + "", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback(result, 'Wallet history found successfully', '1');
                } else {
                    callback([], 'Wallet history not found', '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        });
    },

    /** Function for insert wallet history*/
    insert_wallet_history: function(wallet_data, callback) {
        common.single_insert_data('tbl_wallet_history', wallet_data, function(is_history) {
            if (is_history) {
                user.wallet_amount(wallet_data.user_id, function(walletAmount,responsemsg,responsecode) {
                    //UPDATE tbl_user SET wallet_amount = walletAmount + "+result[0].amount+" WHERE id = '" + req.wallet_id + "'
                    if (wallet_data.status == 'credit') {
                        var updated_amount = parseFloat(walletAmount.wallet_amount) + parseFloat(wallet_data.amount);
                    } else {
                        var updated_amount = parseFloat(walletAmount.wallet_amount) - parseFloat(wallet_data.amount);
                    }
                    con.query("UPDATE tbl_user SET wallet_amount = " + updated_amount + " WHERE id = ?", wallet_data.user_id, function(error, result) {
                        callback(true);
                    });
                });
            } else {
                callback(false);
            }
        });
    },

    /** Fnuction for all service*/
    all_service: function(request, callback) {
        var select = "*, GET_SERVICE_PROVIDER_RATING(s.service_provider_id, NULL) AS ratting, GET_SERVICE_PROVIDER_REVIEW_COUNT(s.service_provider_id, NULL) AS review,id as service_id,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',service_image) as service_image,IFNULL((SELECT b.status FROM tbl_product_bookmark b WHERE b.type_id = s.id AND b.type = 'service' AND b.user_id = " + request.user_id + " ),0) AS bookmark_status";
        var condition = " service_provider_id = " + request.service_provider_id + " AND is_deleted = '0'";
        if (request.category_id) {
            condition += " AND category_id = " + request.category_id + "";
        }
        var query = "select " + select + " from tbl_service s where " + condition + " ORDER BY ID DESC";
        if (request.page) {
            var page = request.page - 1;
            var limit = page * global.PER_PAGE;
            var per_page = global.PER_PAGE;
            query += "LIMIT " + limit + ", " + per_page + "";
        }
        var sql = con.query(query, function(err, result) {
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    callback(result, t('restapi_servicefound'), '1');
                } else {
                    callback([], t('restapi_service_category_notfound'), '0');
                }
            } else {
                callback([], t('restapi_globals_error'), '0');
            }
        });
    },

    /**Function for get booking detail */
    booking_details: function(request, callback) {
      let query = `
        SELECT ab.*,
          DATE_FORMAT(ab.date, '%Y-%m-%d') AS date,
          DATE_FORMAT(ab.end_datetime, '%Y-%m-%d %H:%i:%s') AS end_datetime,
          ab.id AS appointment_id,
          b.name AS business_address,
          sp.first_name as sp_first_name,
          sp.phone as sp_phone,
          usr.first_name as c_first_name,
          usr.phone as c_phone
        FROM tbl_appointment_booking ab
        LEFT JOIN tbl_business_location b ON b.id = ab.business_location_id
        join tbl_service_provider as sp on sp.id = ab.service_provider_id
        join tbl_user as usr on usr.id = ab.user_id
        WHERE ab.id = ${request.appointment_id};`
        con.query(query, function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    con.query("SELECT *,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "',image_name) as service_complete_image FROM tbl_image WHERE type_id = '"+request.appointment_id+"' AND type = 'complete_appointment' AND is_deleted = '0'",function(imgerr,images){
                        result[0].complete_images = (!imgerr && images.length > 0) ? images : [];
                        user.service_provider_detail({service_provider_id:result[0].service_provider_id,user_id:result[0].user_id},function(providerdata,message,code){
                            result[0].providerdata = (providerdata != null) ? providerdata : {};
                            query = `
                              SELECT
                                abd.*,
                                s.price AS service_price,
                                s.service_name,
                                CONCAT('${global.S3_BUCKET_ROOT}${global.PRODUCT_IMAGE}', '', s.service_image) AS service_image,
                                s.duration
                              FROM tbl_appointment_booking_detail abd
                              LEFT JOIN tbl_service s
                              ON s.id = abd.service_id
                              WHERE abd.appointment_id = ${request.appointment_id}
                                AND abd.type = 'Service'
                            `;
                            con.query("select abd.*,s.price as service_price,s.service_name,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',s.service_image) as service_image,s.duration from tbl_appointment_booking_detail abd LEFT JOIN tbl_service s ON s.id = abd.service_id where abd.appointment_id = " + request.appointment_id + " AND abd.type = 'Service'", function(error, results) {
                                if (!error && results[0] != undefined && results[0] != "") {
                                    result[0].services = results;
                                } else {
                                    result[0].services = [];
                                }
                                var sql = con.query("select abd.*,s.price as product_price,p.product_name,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',p.product_image) as product_image from tbl_appointment_booking_detail abd LEFT JOIN tbl_product p ON p.id = abd.product_id LEFT JOIN tbl_subproduct s ON s.product_id = abd.product_id where abd.appointment_id = " + request.appointment_id + " AND abd.type = 'Product' group by abd.id", function(error1, product_result) {
                                    if (!error1 && product_result[0] != undefined && product_result[0] != "") {
                                        result[0].product = product_result;
                                    } else {
                                        result[0].product = [];
                                    }
                                    var sql = con.query("select *,CONCAT('************',number) AS card_number from tbl_card_details where id = " + result[0].card_id + "", function(err, result3) {
                                        if (!err && result3[0] != undefined && result3[0] != "") {
                                            result[0].card_number = result3[0].card_number;
                                            result[0].card_holder_name = result3[0].holder_name;
                                            result[0].card_type = result3[0].type;
                                        }
                                        logger.info({ type: "User Booking details", result: result[0]})
                                        callback('1', 'Booking detail found successfully', result[0]);
                                    });
                                });
                            });
                        });
                    });
                } else {
                    callback('0', 'Booking detail not found', null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        });
    },

    /**Function for user review */
    review_list: function(request, callback) {
        callback('0', t('restapi_globals_error'), null);
    },

    /**Function for get product detail */
    product_detail: function(request, responce) {
        var query = con.query("select tp.*,IFNULL((SELECT b.status FROM tbl_product_bookmark b WHERE b.type_id = " + request.product_id + " AND type = 'product' AND user_id = " + request.user_id + "),0) AS bookmark_status,GET_SERVICE_PROVIDER_RATING(tp.service_provider_id, NULL) AS ratting,GET_SERVICE_PROVIDER_REVIEW_COUNT(tp.service_provider_id, NULL) AS review,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',tp.product_image) as product_image,CONCAT('" + global.S3_BUCKET_ROOT + global.SP_IMAGE + "','',s.profile_image) as profile_image,s.bio,s.first_name,s.last_name,GET_SERVICE_PROVIDER_RATING(tp.service_provider_id, NULL) AS service_provider_ratting,tp.product_image as main_product_image,IFNULL(b.name,'') as business_address,tp.id as product_id,c.name as category_name from tbl_product tp LEFT JOIN tbl_product_category c ON c.id = tp.category_id LEFT JOIN tbl_service_provider s ON s.id = tp.service_provider_id LEFT JOIN tbl_business_location b ON b.service_provider_id = s.id where tp.id = " + request.product_id + " AND tp.is_deleted = '0' AND c.is_deleted = '0' group by tp.id", function(err, result) {
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    require("../service_provider/service_provider_model").multiple_productimage(result[0].product_id, function(image) {
                        // var image = [];
                        if (image != null) {
                            image.push({ multiple_product_image: result[0].product_image });
                            result[0].image = image.reverse();

                        } else {
                            result[0].image = [{ multiple_product_image: result[0].product_image }];
                        }
                        require("../service_provider/service_provider_model").sub_product(request.product_id, function(quantities) {
                            if (quantities != null) {
                                result[0].variant_list = quantities;
                                responce(result[0], t('restapi_productfound'), '1');
                            } else {
                                responce(null, t('restapi_quantitynotfound'), '0');
                            }
                        });
                    });
                } else {
                    responce(null, t('restapi_productnotfound'), '0');
                }
            } else {
                responce(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /** Function for cancel credit product quantity*/
    cancelcreditproductquantity:function(request,callback){
        con.query("SELECT * FROM tbl_appointment_booking_detail WHERE appointment_id = '"+request.appointment_id+"' AND type = 'Product' AND is_deleted = '0'",function(error,products,fields){
            if(!error && products[0] != undefined){
                asyncloop(products,function(item,next){
                    common.debit_wallet("tbl_subproduct","id = '"+item.subproduct_id+"' AND product_id = '"+item.product_id+"'","used_quantity",item.quantity,function(balance){
                        common.credit_wallet("tbl_subproduct","id = '"+item.subproduct_id+"' AND product_id = '"+item.product_id+"'","remaining_quantity",item.quantity,function(balance){
                            next();
                        });
                    });
                },function(){
                    callback(true);
                });
            } else {
                callback(true);
            }
        });
    },

    /**Function for cancel appointment*/
    cancel_appointment: function(request, callback) {
        con.query("SELECT tab.*,TIMESTAMPDIFF(HOUR,NOW(),CONCAT(tab.date,' ',tab.slot_time)) as hours_diff,tsp.cancellationwithin_hours,tsp.cancellation_charges,tsp.autocancellation_charges,IFNULL((SELECT SUM(price) as total_service_amount FROM tbl_appointment_booking_detail WHERE appointment_id = '" + request.appointment_id + "' AND type = 'Service' AND is_deleted = '0' GROUP BY appointment_id),0) as total_service_amount,IFNULL((SELECT SUM(price*quantity) as total_product_amount FROM tbl_appointment_booking_detail WHERE appointment_id = '" + request.appointment_id + "' AND type = 'Product' AND is_deleted = '0' GROUP BY appointment_id),0) as total_product_amount FROM tbl_appointment_booking as tab LEFT JOIN tbl_service_provider as tsp ON tab.service_provider_id = tsp.id WHERE tab.id = '" + request.appointment_id + "'", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    if(result[0].booking_status == 'Cancelled'){
                        callback('0', 'This appointment has already been cancelled', true);
                    } else if(result[0].booking_status == 'Completed'){
                        callback('0', 'This appointment has already been completed', true);
                    } else if(result[0].booking_status == 'Paid'){
                        callback('0', 'This appointment has already been completed and paid', true);
                    } else if(result[0].booking_status == 'No Show'){
                        callback('0', 'This appointment has already been cancelled and on show', true);
                    } else {
                        var notificationtitle = "Cancelled Appointment Booking";
                        var notificationmsg = "Appointment Booking has been cancelled";
                        if(result[0].is_only_product == '1'){
                            var notificationtitle = "Cancelled Product Booking";
                            var notificationmsg = "Product Booking has been cancelled";
                        }
                        if(result[0].payment_from == 'Wallet'){
                            if(moment().format("YYYY-MM-DD HH:mm") >= moment(result[0].date+" "+result[0].slot_time).format("YYYY-MM-DD HH:mm")){
                                var cancellation_charges_amount = (parseFloat(result[0].total_service_amount) * parseFloat(result[0].autocancellation_charges)) / 100;
                            } else {
                                if(parseInt(result[0].hours_diff) <= parseInt(result[0].cancellationwithin_hours)){
                                    var cancellation_charges_amount = (parseFloat(result[0].total_service_amount) * parseFloat(result[0].cancellation_charges)) / 100;
                                } else {
                                    var cancellation_charges_amount = parseFloat(result[0].total_service_amount);
                                }
                            }
                            cancellation_charges_amount += parseFloat(result[0].merchant_fees_amount) + parseFloat(result[0].app_fees_amount);
                            if(cancellation_charges_amount > parseFloat(result[0].total_amount)){
                                var refund_amount = 0;
                            } else {
                                var refund_amount = parseFloat(result[0].total_amount) - cancellation_charges_amount;
                            }
                            if(refund_amount > 0){
                                var wallet_insert = {
                                    user_id: result[0].user_id,
                                    card_id: 0,
                                    order_id: request.appointment_id,
                                    amount: refund_amount,
                                    title: 'refund cancel booking',
                                    transaction_id: '',
                                    status: 'credit'
                                }
                                require('../user/user_model').insert_wallet_history(wallet_insert, function(is_wallet_insert) {
                                    var update_param = {
                                        is_cancel: '1',
                                        booking_status: 'Cancelled',
                                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                                        is_refund: '1',
                                        payment_status:'paid',
                                        payment_datetime:datetime.create().format('Y-m-d H:M:S'),
                                        refund_amount: refund_amount,
                                        refund_id: 'Blookd'+datetime.create().format('YmdHMS'),
                                        refund_status: 'succeeded',
                                        refund_object: {},
                                        refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                    }
                                    if (request.cancel_id) {
                                        update_param.cancel_id = request.cancel_id;
                                    }
                                    if (request.cancel_reason) {
                                        update_param.cancel_reason = request.cancel_reason;
                                    }
                                    common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function(is_refuned) {
                                        user.cancelcreditproductquantity(request,function(iscredit){
                                            var message = {
                                                sender_id: result[0].user_id,
                                                sender_type: USER_TYPE.USER,
                                                receiver_id: result[0].service_provider_id,
                                                type: 'service_provider',
                                                receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                                message: notificationmsg,
                                                title: notificationtitle,
                                                isaction_id:request.appointment_id,
                                                tag: 'booking',
                                            }
                                            require('../../../config/common').prepare_notification(result[0].service_provider_id, message, function(notification) {
                                                callback('1', 'Cancel appointment successfully', true)
                                            });
                                        });
                                    });
                                });
                            } else {
                                var update_param = {
                                    is_cancel: '1',
                                    booking_status: 'Cancelled',
                                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                                    is_refund: '1',
                                    payment_status:'paid',
                                    payment_datetime:datetime.create().format('Y-m-d H:M:S'),
                                    refund_amount: refund_amount,
                                    refund_id: 'Blookd'+datetime.create().format('YmdHMS'),
                                    refund_status: 'succeeded',
                                    refund_object: {},
                                    refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                }
                                if (request.cancel_id) {
                                    update_param.cancel_id = request.cancel_id;
                                }
                                if (request.cancel_reason) {
                                    update_param.cancel_reason = request.cancel_reason;
                                }
                                common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function(is_refuned) {
                                    user.cancelcreditproductquantity(request,function(iscredit){
                                        var message = {
                                            sender_id: result[0].user_id,
                                            sender_type: USER_TYPE.USER,
                                            receiver_id: result[0].service_provider_id,
                                            type: 'service_provider',
                                            receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                            message: notificationmsg,
                                            title: notificationtitle,
                                            isaction_id:request.appointment_id,
                                            tag: 'booking',
                                        }
                                        require('../../../config/common').prepare_notification(result[0].service_provider_id, message, function(notification) {
                                            callback('1', 'Cancel appointment successfully', true)
                                        });
                                    });
                                });
                            }
                        } else {
                            if(result[0].payment_mode == 'pay_later' && result[0].payment_status == 'unpaid'){
                                if(moment().format("YYYY-MM-DD HH:mm") >= moment(result[0].date+" "+result[0].slot_time).format("YYYY-MM-DD HH:mm")){
                                    var cancellation_charges = (parseFloat(result[0].total_service_amount) * parseFloat(result[0].autocancellation_charges)) / 100;
                                } else {
                                    if(parseInt(result[0].hours_diff) <= parseInt(result[0].cancellationwithin_hours)){
                                        var cancellation_charges = (parseFloat(result[0].total_service_amount) * parseFloat(result[0].cancellation_charges)) / 100;
                                    } else {
                                        var cancellation_charges = parseFloat(result[0].total_service_amount);
                                    }
                                }
                                cancellation_charges += parseFloat(result[0].merchant_fees_amount) + parseFloat(result[0].app_fees_amount);
                                if(cancellation_charges > parseFloat(result[0].total_amount)){
                                    var refund_amount = 0;
                                } else {
                                    var refund_amount = parseFloat(result[0].total_amount) - cancellation_charges;
                                }
                                stripe.captureStripeCharge(result[0].transaction_id, cancellation_charges,function(code, message, refund) {
                                    if (code == '1') {
                                        var update_param = {
                                            is_cancel: '1',
                                            booking_status: 'Cancelled',
                                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                                            is_refund: '1',
                                            payment_status:'paid',
                                            payment_datetime:datetime.create().format('Y-m-d H:M:S'),
                                            refund_amount: refund_amount,
                                            refund_id: (refund != null) ? refund.id : "",
                                            refund_status: "succeeded",
                                            refund_object: (refund != null) ? JSON.stringify(refund) : "",
                                            refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                        }
                                        if (request.cancel_id) {
                                            update_param.cancel_id = request.cancel_id;
                                        }
                                        if (request.cancel_reason) {
                                            update_param.cancel_reason = request.cancel_reason;
                                        }
                                        common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function(is_refuned) {
                                            user.cancelcreditproductquantity(request,function(iscredit){
                                                var message = {
                                                    sender_id: result[0].user_id,
                                                    sender_type: USER_TYPE.USER,
                                                    receiver_id: result[0].service_provider_id,
                                                    type: 'service_provider',
                                                    receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                                    message: notificationmsg,
                                                    title: notificationtitle,
                                                    isaction_id:request.appointment_id,
                                                    tag: 'booking',
                                                }
                                                require('../../../config/common').prepare_notification(result[0].service_provider_id, message, function(notification) {
                                                    callback('1', 'Cancel appointment successfully', true);
                                                });
                                            });
                                        });
                                    } else {
                                        callback(code, message, refund);
                                    }
                                });
                            } else {
                                if(moment().format("YYYY-MM-DD HH:mm") >= moment(result[0].date+" "+result[0].slot_time).format("YYYY-MM-DD HH:mm")){
                                    var cancellation_charges = (parseFloat(result[0].total_service_amount) * parseFloat(result[0].autocancellation_charges)) / 100;
                                } else {
                                    if(parseInt(result[0].hours_diff) <= parseInt(result[0].cancellationwithin_hours)){
                                        var cancellation_charges = (parseFloat(result[0].total_service_amount) * parseFloat(result[0].cancellation_charges)) / 100;
                                    } else {
                                        var cancellation_charges = parseFloat(result[0].total_service_amount);
                                    }
                                }
                                cancellation_charges += parseFloat(result[0].merchant_fees_amount) + parseFloat(result[0].app_fees_amount);
                                if(cancellation_charges > parseFloat(result[0].total_amount)){
                                    var refund_amount = 0;
                                } else {
                                    var refund_amount = parseFloat(result[0].total_amount) - cancellation_charges;
                                }
                                if(refund_amount > 0){
                                    stripe.createChargeRefund(result[0].transaction_id, refund_amount, function(code, message, refund) {
                                        if (code == '1') {
                                            var update_param = {
                                                is_cancel: '1',
                                                booking_status: 'Cancelled',
                                                updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                is_refund: '1',
                                                payment_status:'paid',
                                                payment_datetime:datetime.create().format('Y-m-d H:M:S'),
                                                refund_amount: refund_amount,
                                                refund_id: (refund != null) ? refund.charge : "",
                                                refund_status: (refund != null) ? refund.status : "successed",
                                                refund_object: (refund != null) ? JSON.stringify(refund) : "",
                                                refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                            }
                                            if (request.cancel_id) {
                                                update_param.cancel_id = request.cancel_id;
                                            }
                                            if (request.cancel_reason) {
                                                update_param.cancel_reason = request.cancel_reason;
                                            }
                                            common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function(is_refuned) {
                                                user.cancelcreditproductquantity(request,function(iscredit){
                                                    var message = {
                                                        sender_id: result[0].user_id,
                                                        sender_type: USER_TYPE.USER,
                                                        receiver_id: result[0].service_provider_id,
                                                        type: 'service_provider',
                                                        receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                                        message: notificationmsg,
                                                        title: notificationtitle,
                                                        isaction_id:request.appointment_id,
                                                        tag: 'booking',
                                                    }
                                                    require('../../../config/common').prepare_notification(result[0].service_provider_id, message, function(notification) {
                                                        callback('1', 'Cancel appointment successfully', true);
                                                    });
                                                });
                                            });
                                        } else {
                                            callback(code, message, refund);
                                        }
                                    });
                                } else {
                                    var update_param = {
                                        is_cancel: '1',
                                        booking_status: 'Cancelled',
                                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                                        is_refund: '1',
                                        payment_status:'paid',
                                        payment_datetime:datetime.create().format('Y-m-d H:M:S'),
                                        refund_amount: refund_amount,
                                        refund_id: "",
                                        refund_status: "successed",
                                        refund_object: "",
                                        refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                    }
                                    if (request.cancel_id) {
                                        update_param.cancel_id = request.cancel_id;
                                    }
                                    if (request.cancel_reason) {
                                        update_param.cancel_reason = request.cancel_reason;
                                    }
                                    common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function(is_refuned) {
                                        user.cancelcreditproductquantity(request,function(iscredit){
                                            var message = {
                                                sender_id: result[0].user_id,
                                                sender_type: USER_TYPE.USER,
                                                receiver_id: result[0].service_provider_id,
                                                type: 'service_provider',
                                                receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                                message: notificationmsg,
                                                title: notificationtitle,
                                                isaction_id:request.appointment_id,
                                                tag: 'booking',
                                            }
                                            require('../../../config/common').prepare_notification(result[0].service_provider_id, message, function(notification) {
                                                callback('1', 'Cancel appointment successfully', true);
                                            });
                                        });
                                    });
                                }
                            }
                        }
                    }
                } else {
                    callback('2', "Appointment not found", null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        });
    },

    /**Function for get repoet list*/
    report_reason_list: function(request, callback) {
        con.query("select * from tbl_report where status = '1' AND is_deleted = '0'", function(err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else if (result[0] != undefined && result[0] != "") {
                callback('1', t('restapi_reportreason_detailfound'), result);
            } else {
                callback('0', t('restapi_cancelorder_notfound'), null);
            }
        });
    },

    /**Function for report appointment or product*/
    report_service: function(request, callback) {
        var report = {
            is_reported: '1',
        };
        if (request.report_id) {
            report.report_id = request.report_id;
            report.report_reason = "";
        }
        if (request.report_reason) {
            report.report_id = '0';
            report.report_reason = request.report_reason;
        }
        common.update_data('tbl_appointment_booking', request.appointment_id, report, function(reported) {
            if (reported) {
                callback('1', 'service reported successfully', true)
            } else {
                callback('0', t('restapi_globals_error'), null)
            }
        });
    },

    /**Function for get service provider list for review */
    service_provider_review_list: function(request, callback) {
        con.query("select *,id as review_id from tbl_review_list where user_type = 'user' AND status = 'Active' AND is_deleted = '0'", function(err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else if (result[0] != undefined && result[0] != "") {
                callback('1', 'Review list found successfully', result);
            } else {
                callback('0', 'Review list not found', null);
            }
        })
    },

    /**Function for get service provider detail review */
    service_provider_detail_review: function(request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        con.query("select ur.*,date_format(ur.insertdate, '%Y-%m-%d %H:%i:%s') as review_date,u.first_name,u.last_name,CONCAT('" + global.S3_BUCKET_ROOT + global.USER_IMAGE + "','',u.profile_image) as profile_image from tbl_user_review ur LEFT JOIN tbl_user u ON u.id = ur.user_id where  ur.service_provider_id = " + request.service_provider_id + " AND ur.ratting > 0 LIMIT " + limit + ", " + per_page + "", function(err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else if (result[0] != undefined && result[0] != "") {
                callback('1', 'Review List found successfully', result);
            } else {
                callback('0', 'Review list not found', null);
            }
        })
    },

  add_review: (request, callback) => {
    con.query(`
      SELECT *
      FROM tbl_user_review
      WHERE order_id = ${ request.order_id }
    `, (err, olddata) => {
      if (!err && olddata[0]) {
        user.update_review(request, (responseCode, resmessage, responsedata) => {
          callback(responseCode, resmessage, responsedata)
        })
      } else {
        const review_data = {
          service_provider_id: request.service_provider_id || 0,
          review: request.review || '',
          user_id: request.user_id,
          ratting: request.ratting,
          review_id: request.review_id || '',
          review_reply: '',
          is_deleted: 0,
          order_id: request.order_id,
          updatetime: datetime.create().format('Y-m-d H:M:S'),
          insertdate: datetime.create().format('Y-m-d H:M:S'),
          type_id: 0,
          type: 'service_provider',
        }
        common.single_insert_data('tbl_user_review', review_data, () => {
          if (request.service_provider_id) {
            con.query(`
              SELECT *
              FROM tbl_service_provider
              WHERE id = ${ request.service_provider_id }
            `, (err1, result1) => {
              if (!err1 && result1[0] && result1[0].newreview_notify === 'On') {
                con.query(`
                    SELECT *, id AS user_id
                    FROM tbl_user
                    WHERE id = ${ request.user_id }
                `, (err, result) => {
                  const message = {
                    sender_id: request.user_id,
                    sender_type: USER_TYPE.USER,
                    receiver_id: request.service_provider_id,
                    type: 'service_provider',
                    receiver_type: USER_TYPE.SERVICE_PROVIDER,
                    message: `${ result[0].first_name } ${ result[0].last_name } left you a review.`,
                    title: 'Rate & Reviews',
                    isaction_id: request.order_id,
                    tag: 'newratereview',
                  }
                  require('../../../config/common').prepare_notification(request.service_provider_id, message, function() {
                    callback('1', 'Ratting added successfully', null)
                  })
                })
              } else {
                callback('1', 'Ratting added successfully', null)
              }
            })
          } else {
            callback('1', 'Ratting added successfully', null)
          }
        })
      }
    })
  },

  update_review: function(request, callback) {
    var review_data = {
      service_provider_id: (request.service_provider_id != '' && request.service_provider_id != undefined) ? request.service_provider_id : 0,
      review: (request.review != '' && request.review != undefined) ? request.review : '',
      user_id: request.user_id,
      ratting: request.ratting,
      review_id: (request.review_id != undefined && request.review_id != '') ? request.review_id : '',
      review_reply: '',
      is_deleted: '0',
      order_id: request.order_id,
      updatetime: datetime.create().format('Y-m-d H:M:S'),
      insertdate: datetime.create().format('Y-m-d H:M:S'),
    }
    review_data.type_id = 0
    review_data.type = 'service_provider'
    common.update_data_condition('tbl_user_review', 'service_provider_id = \'' + review_data.service_provider_id + '\' AND user_id = \'' + review_data.user_id + '\' AND order_id = \'' + request.order_id + '\' AND type_id = \'' + review_data.type_id + '\' AND type = \'' + review_data.type + '\'', review_data, function(is_review_update) {
      con.query('SELECT *,id AS user_id FROM tbl_user WHERE id = \'' + request.user_id + '\'', function(err, result) {
        var message = {
          sender_id: request.user_id,
          sender_type: USER_TYPE.USER,
          receiver_id: request.service_provider_id,
          type: 'service_provider',
          receiver_type: USER_TYPE.SERVICE_PROVIDER,
          message: `${ result[0].first_name } ${ result[0].last_name } updated a review.`,
          title: 'Rate & Reviews',
          isaction_id: request.order_id,
          tag: 'updateratereview',
        }
        require('../../../config/common').prepare_notification(request.service_provider_id, message, function(notification) {
          callback('1', 'Rating updated successfully', null)
        })
      })
    })
  },

  feed_list: (request, callback) => {
    const page = request.page - 1
    const limit = page * global.PER_PAGE
    const per_page = global.PER_PAGE
    const condition = request.service_provider_id ?
      `service_provider_id = ${ request.service_provider_id } ` :
      `p.service_provider_id IN (
        SELECT service_provider_id 
        FROM tbl_bookmark 
        WHERE user_id = ${ request.user_id }
        AND status
      ) `
    con.query(`
      SELECT 
        p.id AS post_id,
        s.first_name,
        s.last_name,
        service_provider_id,
        caption,
        description,
        GET_SERVICE_PROVIDER_RATING(p.service_provider_id, NULL) AS service_provider_ratting,
        GET_SERVICE_PROVIDER_REVIEW_COUNT(p.service_provider_id, NULL) AS review,
        (
          SELECT COUNT(id)
          FROM tbl_like l
          WHERE l.post_id = p.id
        ) AS total_like,
        (
          SELECT COUNT(id)
          FROM tbl_comment c
          WHERE c.is_deleted = '0'
          AND c.post_id = p.id
        ) AS total_comment,
        CONCAT('${ global.S3_BUCKET_ROOT }${ global.POST_IMAGE }', post_images) AS post_image,
        CONCAT('${ global.S3_BUCKET_ROOT }${ global.SP_IMAGE }', s.profile_image) AS profile_image,
        IFNULL(
          (
            SELECT 1
            FROM tbl_like l
            WHERE l.user_id = ${ request.user_id }
            AND type = 'user'
            AND l.post_id = p.id
            ORDER BY ID DESC
            LIMIT 1
          ),
          0
        ) AS is_like
      FROM tbl_post p
      LEFT JOIN tbl_service_provider s ON s.id = p.service_provider_id
      WHERE NOT p.is_deleted
      AND ${ condition }
      ORDER BY p.ID DESC
      LIMIT ${ limit }, ${ per_page }
    `, function(err, result) {
      if (!err) {
        if (result[0]) {
          callback(result, t('restapi_post_found'), 1)
        } else {
          callback([], t('restapi_post_notfound'), 2)
        }
      } else {
        callback([], t('restapi_globals_error') + err, 0)
      }
    })
  },

    /**Function for post detail */
    post_detail: function(request, callback) {
        var select = ""
            // if (request.service_provider_id) {
            //     select += ",IFNULL((SELECT 1 FROM tbl_like l WHERE l.user_id = " + request.service_provider_id + " AND type = 'user' AND l.post_id = p.id ORDER BY ID DESC LIMIT 1),0) AS is_like"
            // }
            // bookmark_status
        var sql = con.query("select id post_id,service_provider_id,caption,description,CONCAT('" + global.S3_BUCKET_ROOT + global.POST_IMAGE + "','',post_images) as post_image,IFNULL((SELECT 1 FROM tbl_like l WHERE l.user_id = " + request.service_provider_id + " AND type = 'user' AND l.post_id = p.id ORDER BY ID DESC LIMIT 1),0) AS is_like from tbl_post p where id = " + request.post_id + " AND is_deleted = '0' ", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    require("../service_provider/service_provider_model").multiple_postimage(result[0].post_id, function(post_image) {
                        if (post_image != null) {
                            post_image.push({ post_image: result[0].post_image });
                            result[0].image = post_image.reverse();
                        } else {
                            result[0].image = { post_image: result[0].post_image };
                        }
                        require("../service_provider/service_provider_model").like_count(result[0].post_id, function(total_like) {
                            result[0].total_like = total_like;
                            require("../service_provider/service_provider_model").comment_count(result[0].post_id, function(total_comment) {
                                result[0].total_comment = total_comment;
                                // result[0].comment = comment;
                                callback(result[0], t('restapi_post_found'), '1');
                            });
                        });
                    });
                } else {
                    callback(null, t('restapi_post_notfound'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**Function for get occation promo */
    get_promotion: function(request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        var sql = con.query("select top.*,top.id as promo_id,CONCAT('" + global.S3_BUCKET_ROOT + global.POST_IMAGE + "','',top.image_name) as occational_promo_image from tbl_occational_promo as top LEFT JOIN tbl_service_provider tsp ON top.service_provider_id = tsp.id where top.is_deleted = '0' AND tsp.is_deleted = '0' AND tsp.status = 'Active' ORDER BY top.id DESC LIMIT " + limit + ", " + per_page + "", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback(result, t('restapi_occational_promo_found'), '1');
                } else {
                    callback(null, t('restapi_occational_promo_not_found'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error') + err, '0');
            }
        })
    },

    /** Function for occational promo detail*/
    promotion_detail: function(request, callback) {
        var sql = con.query("select top.*,top.id as occational_promo_id,CONCAT('" + global.S3_BUCKET_ROOT + global.POST_IMAGE + "','',top.image_name) as occational_promo_image,CONCAT(tsp.first_name,' ',tsp.last_name) as sp_name from tbl_occational_promo as top LEFT JOIN tbl_service_provider tsp ON top.service_provider_id = tsp.id where top.id = " + request.promotion_id + "", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback(result[0], t('restapi_occational_promo_found'), '1');
                } else {
                    callback(null, t('restapi_occational_promo_not_found'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /** Function for make product booking payment*/
    makeproductbookingpayment:function(request,user_detail,callback){
        if(request.wallet_amount != undefined && parseFloat(request.wallet_amount) > 0){
            if(parseFloat(user_detail.wallet_amount) >= parseFloat(request.wallet_amount)){
                callback({transaction_id:"BLOOKD"+moment().utc().format("YYYYMMDDHHiiss"),balance_transaction: request.wallet_amount,receipt_url:""},"Payment make successfully","1");
            } else {
                callback(null, 'Insufficient fund in your Wallet. wallet payment is not possible.', '0');
            }
        } else {
            if(request.payment_from == 'ApplePay' || request.payment_from == 'GooglePay'){
                callback({transaction_id: request.transaction_id,balance_transaction: "",receipt_url:""},"Payment make successfully","1");
            } else {
                con.query("select * from tbl_card_details where id = " + request.card_id + "", function(err, result) {
                    if (err) {
                        callback(null, t('restapi_globals_error'), '0');
                    } else if (result[0] == "" || result[0] == undefined) {
                        callback(null, 'User card detail not found', '0');
                    } else {
                        let payment_obj = {
                            amount: Math.round(request.total_amount * 100),
                            currency: "USD",
                            application_fee_amount : Math.round(request.admin_earning * 100),
                            destination : request.merchant_account_id,
                            //capture:(request.payment_mode == 'pay_later') ? false : true,
                            // source: card.card_token,
                            customer: result[0]['customer_id'],
                            description: 'For user booking',
                        }
                        require("../../../config/payment").tranferStripePlatform(payment_obj, function(code, msg, stripe_data) {
                            if (stripe_data != null) {
                                callback({transaction_id: stripe_data.transaction_id,balance_transaction: (stripe_data.balance_transaction != null && stripe_data.balance_transaction != "" ? stripe_data.balance_transaction : "") ,receipt_url:(stripe_data.receipt_url != "" && stripe_data.receipt_url != undefined) ? stripe_data.receipt_url : ""},"Payment make successfully","1");
                            } else {
                                callback(stripe_data, msg, code);
                            }
                        });
                    }
                });
            }
        }
    },

    /**Function for product booking */
    product_booking: function(request, callback) {
        con.query("SELECT * FROM tbl_service_provider WHERE id = '"+request.service_provider_id+"'",function(error,spdata,fields){
            if(!error && spdata[0] != undefined && spdata[0].merchant_account_id != ""){
                user.checkproductsquantity(request,function(is_valid,productname){
                    if(is_valid){
                        user.checkalreadybookthisslotorprovider(request,function(is_alreadybook){
                            if(is_alreadybook){
                                callback(null, "Service provider already booked for this time duration.", '0');
                            } else {
                                require("../service_provider/service_provider_model").generate_booking_id("tbl_appointment_booking", function(booking_id) {
                                    user.get_user_detail(request.user_id, function(user_detail) {
                                        var clientparam = {
                                            service_provider_id: request.service_provider_id,
                                            profile_image: user_detail.image_name,
                                            customer_name: user_detail.full_name,
                                            phone: user_detail.phone,
                                            country_code: user_detail.country_code,
                                            email: user_detail.email,
                                            is_user_app: request.user_id,
                                            notes: ""
                                        }
                                        user.add_client(clientparam, function(is_client_add) {
                                            if(request.discount == undefined || request.discount == ""){
                                                request.discount = 0;
                                            }
                                            if(request.payment_from != undefined && request.payment_from != "" && request.payment_from == 'Wallet'){
                                                var merchant_fees_amount = parseFloat(0);
                                            } else {
                                                var merchant_fees_amount = (parseFloat(request.total_amount) * parseFloat(spdata[0].merchant_fees) / 100) + parseFloat(spdata[0].merchant_fees_cents);
                                            }
                                            var app_fees_amount = (parseFloat(request.sub_total) - parseFloat(request.discount)) * (spdata[0].app_fees) / 100;
                                            var admin_earning = merchant_fees_amount + app_fees_amount;
                                            request.merchant_account_id = spdata[0].merchant_account_id;
                                            request.admin_earning = admin_earning;
                                            user.makeproductbookingpayment(request,user_detail,function(paymentdata,resmessage,rescode){
                                                if(rescode == '1'){
                                                    var new_booking = {
                                                        transaction_id: paymentdata.transaction_id,
                                                        balance_transaction: paymentdata.balance_transaction,
                                                        receipt_url: (paymentdata.receipt_url != "" && paymentdata.receipt_url != undefined) ? paymentdata.receipt_url : "",
                                                        booking_id: booking_id,
                                                        service_provider_id: request.service_provider_id,
                                                        business_location_id: request.business_location_id,
                                                        customer_id: '0',
                                                        user_id: request.user_id,
                                                        customer_name: '',
                                                        date: (request.date != undefined && request.date != "") ? request.date : datetime.create().format('Y-m-d'),
                                                        slot_time: (request.time_slot != undefined && request.time_slot != "" && request.time_slot != "00:00:00" && request.time_slot != "00:00") ? request.time_slot : datetime.create().format('H:M:S'),
                                                        end_datetime:(request.date != undefined && request.date != "" && request.time_slot != undefined && request.time_slot != "" && request.time_slot != "00:00:00" && request.time_slot != "00:00") ? request.date+" "+request.time_slot : datetime.create().format('Y-m-d H:M:S'),
                                                        total_duration:(request.total_duration != undefined && request.total_duration != "") ? request.total_duration : 0,
                                                        additional_duration: request.additional_duration || 0,
                                                        description: (request.description != undefined && request.description != "") ? request.description : "",
                                                        booking_status: 'Completed',
                                                        payment_status: 'paid',
                                                        booking_type: 'online',
                                                        card_id: (request.card_id != "" && request.card_id != undefined) ? request.card_id : "0",
                                                        is_tip_after_service: (request.is_tip_after_service != "" && request.is_tip_after_service != undefined) ? request.is_tip_after_service : 0,
                                                        payment_mode: (request.payment_mode != "" && request.payment_mode != undefined) ? request.payment_mode : "pay_later",
                                                        payment_from: (request.payment_from != "" && request.payment_from != undefined) ? request.payment_from : "Card",
                                                        payment_intent_id: (request.payment_intent_id != "" && request.payment_intent_id != undefined) ? request.payment_intent_id : null,
                                                        promocode: (request.promocode != "" && request.promocode != undefined) ? request.promocode : "",
                                                        sub_total: request.sub_total,
                                                        wallet_amount: (request.wallet_amount != "" && request.wallet_amount != undefined) ? request.wallet_amount : 0,
                                                        total_amount: (request.total_amount != undefined && parseFloat(request.total_amount) > 0) ? request.total_amount : request.wallet_amount,
                                                        discount: request.discount,
                                                        tip_amount: (request.tip != "" && request.tip != undefined) ? request.tip : 0,
                                                        merchant_fees:spdata[0].merchant_fees,
                                                        merchant_fees_amount:merchant_fees_amount,
                                                        merchant_fees_cents:spdata[0].merchant_fees_cents,
                                                        app_fees:spdata[0].app_fees,
                                                        app_fees_amount:app_fees_amount,
                                                        admin_earning:admin_earning,
                                                        provider_earning:(request.total_amount != undefined && parseFloat(request.total_amount) > 0) ? parseFloat(request.total_amount) - admin_earning : parseFloat(request.wallet_amount) - admin_earning,
                                                        tax: request.tax,
                                                        is_reported: '0',
                                                        is_deleted: '0',
                                                        payment_datetime : datetime.create().format('Y-m-d H:M:S'),
                                                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                        is_product: '1',
                                                        is_only_product: '1'
                                                    }
                                                    common.single_insert_data('tbl_appointment_booking', new_booking, function(appointment_id) {
                                                        var appointmentId = appointment_id.insertId;
                                                        if (appointmentId) {
                                                            request.appointment_id = appointmentId;
                                                            if (request.wallet_amount > 0 && request.wallet_amount != "") {
                                                                var wallet_insert = {
                                                                    user_id: request.user_id,
                                                                    card_id: 0,
                                                                    order_id: appointmentId,
                                                                    amount: request.wallet_amount,
                                                                    title: 'product booking',
                                                                    transaction_id: '',
                                                                    status: 'debit'
                                                                }
                                                                user.insert_wallet_history(wallet_insert, function(is_wallet_insert) {})
                                                            }
                                                            if (request.product) {
                                                                asyncloop(request.product, function(item1, next1) {
                                                                    if (item1) {
                                                                        var appointment_detail = {
                                                                            appointment_id: appointmentId,
                                                                            booking_id: booking_id,
                                                                            service_provider_id: request.service_provider_id,
                                                                            product_id: item1.product_id,
                                                                            subproduct_id: (item1.subproduct_id != "" && item1.subproduct_id != undefined) ? item1.subproduct_id : '0',
                                                                            price: item1.price,
                                                                            type: 'product',
                                                                            service_id: '0',
                                                                            quantity: item1.quantity,
                                                                            service_status: '',
                                                                            is_deleted: '0',
                                                                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                                            insertdate: datetime.create().format('Y-m-d H:M:S'),

                                                                        }
                                                                        common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function(is_appointment_detail) {
                                                                            common.credit_wallet("tbl_subproduct","id = '"+appointment_detail.subproduct_id+"' AND product_id = '"+appointment_detail.product_id+"'","used_quantity",item1.quantity,function(balance){
                                                                                common.debit_wallet("tbl_subproduct","id = '"+appointment_detail.subproduct_id+"' AND product_id = '"+appointment_detail.product_id+"'","remaining_quantity",item1.quantity,function(balance){
                                                                                    next1();
                                                                                });
                                                                            });
                                                                        })
                                                                    } else {
                                                                        next1();
                                                                    }
                                                                }, function() {
                                                                    user.booking_details(request,function(responsecode,resmessage,bookingdetails){
                                                                        callback(bookingdetails, t('restapi_service_booked'), '1');
                                                                    })
                                                                })
                                                            } else {
                                                                user.booking_details(request,function(responsecode,resmessage,bookingdetails){
                                                                    callback(bookingdetails, t('restapi_service_booked'), '1');
                                                                });
                                                            }
                                                        } else {
                                                            callback(null, t('restapi_globals_error'), '0');
                                                        }
                                                    });
                                                } else {
                                                    callback(paymentdata,resmessage,rescode);
                                                }
                                            });
                                        });
                                    });
                                });
                            }
                        });
                    } else {
                        callback(null, "You can't add more quantity for "+productname+" product", '0');
                    }
                });
            } else {
                callback(null, "The service provider not added his bank account.", '0');
            }
        });
    },

    /**Api for booking payment */
    booking_payment: function(request, callback) {
        con.query("select * from tbl_appointment_booking where id = " + request.appointment_id + "", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    if (result[0].payment_mode == 'pay_later') {
                        con.query("select * from tbl_card_details where id = " + request.card_id + "", function(err, result) {
                            if (err) {
                                callback(null, t('restapi_globals_error'), '0');
                            } else if (result[0] == "" || result[0] == undefined) {
                                callback(null, 'User card detail not found', '0');
                            } else {
                                con.query("SELECT * FROM tbl_service_provider WHERE id = '"+request.service_provider_id+"'",function(error,spdata,fields){
                                    if(!error && spdata[0] != undefined && spdata[0].merchant_account_id != ""){
                                        if(request.discount == undefined || request.discount == ""){
                                            request.discount = 0;
                                        }
                                        var merchant_fees_amount = (parseFloat(request.total_amount) * parseFloat(spdata[0].merchant_fees) / 100) + parseFloat(spdata[0].merchant_fees_cents);
                                        var app_fees_amount = (parseFloat(request.sub_total) - parseFloat(request.discount)) * (spdata[0].app_fees) / 100;
                                        var admin_earning = merchant_fees_amount + app_fees_amount;
                                        let payment_obj = {
                                            amount: Math.round(request.total_amount * 100),
                                            currency: "USD",
                                            application_fee_amount : Math.round(admin_earning * 100),
                                            destination : request.merchant_account_id,
                                            // source: card.card_token,
                                            customer: result[0]['customer_id'],
                                            description: 'For user booking pay later',
                                        }
                                        require("../../../config/payment").tranferStripePlatform(payment_obj, function(code, msg, stripe_data) {
                                            if (stripe_data != "") {
                                                if (request.wallet_amount > 0 && request.wallet_amount != "") {
                                                    var wallet_insert = {
                                                        user_id: request.user_id,
                                                        card_id: 0,
                                                        order_id: request.appointment_id,
                                                        amount: request.wallet_amount,
                                                        title: 'service booking',
                                                        transaction_id: '',
                                                        status: 'debit'
                                                    }
                                                    user.insert_wallet_history(wallet_insert, function(is_wallet_insert) {})
                                                    var update_booking = {
                                                        transaction_id: stripe_data.transaction_id,
                                                        balance_transaction: (stripe_data.balance_transaction != null && stripe_data.balance_transaction != "" ? stripe_data.balance_transaction : ""),
                                                        receipt_url: (stripe_data.receipt_url != "" && stripe_data.receipt_url != undefined) ? stripe_data.receipt_url : "",
                                                        user_id: request.user_id,
                                                        booking_status: 'Paid',
                                                        payment_status: 'paid',
                                                        booking_type: 'online',
                                                        card_id: (request.card_id != "" && request.card_id != undefined) ? request.card_id : "0",
                                                        promocode: (request.promocode != "" && request.promocode != undefined) ? request.promocode : "",
                                                        sub_total: request.sub_total,
                                                        wallet_amount: (request.wallet_amount != "" && request.wallet_amount != undefined) ? request.wallet_amount : 0,
                                                        total_amount: request.total_amount,
                                                        merchant_fees:spdata[0].merchant_fees,
                                                        merchant_fees_amount:merchant_fees_amount,
                                                        merchant_fees_cents:spdata[0].merchant_fees_cents,
                                                        app_fees:spdata[0].app_fees,
                                                        app_fees_amount:app_fees_amount,
                                                        admin_earning:admin_earning,
                                                        provider_earning:parseFloat(request.total_amount) - admin_earning,
                                                        discount: request.discount,
                                                        tip_amount: (request.tip != "" && request.tip != undefined) ? request.tip : 0,
                                                        tax: request.tax,
                                                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                    }
                                                    common.update_data("tbl_appointment_booking", request.appointment_id, update_booking, function(is_update_appointment) {
                                                        if (is_update_appointment) {
                                                            callback(true, 'Payment received successfully', '1');
                                                        } else {
                                                            callback(null, t('restapi_globals_error'), '0');
                                                        }
                                                    })
                                                }
                                            } else {
                                                callback(stripe_data, msg, code);
                                            }
                                        })
                                    } else {
                                        callback(null, "The service provider not added his bank account.", '0');
                                    }
                                });
                            }
                        })
                    } else {
                        callback(null, 'Payment already received for this appointment', '0');
                    }
                } else {
                    callback(null, t('restapi_appointment_notfound'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**Api for reschedule booking */
    reschedule_appointment: function(request, callback) {
        con.query("SELECT tab.* FROM tbl_appointment_booking as tab WHERE tab.id = '" + request.appointment_id + "'", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    if(moment().format("YYYY-MM-DD HH:mm") >= moment(result[0].date+" "+result[0].slot_time).format("YYYY-MM-DD HH:mm")){
                        callback('0', "You can't reschedule this appointment beacuse appointment time is pass out.", null);
                    } else {
                        var enddatetime = moment(request.date+" "+request.time_slot)
                          .add(request.total_duration,'minutes')
                          .add(request.additional_duration || 0, 'minutes')
                          .format("YYYY-MM-DD HH:mm:ss");
                        var reschedule_booking = {
                            business_location_id: request.business_location_id,
                            date: (request.date) ? request.date : datetime.create().format('Y-m-d'),
                            slot_time: (request.time_slot) ? request.time_slot : '00:00',
                            end_datetime:enddatetime,
                            total_duration:request.total_duration,
                            additional_duration: request.additional_duration || 0,
                            booking_status: 'Reschedule Request'
                        }
                        common.update_data("tbl_appointment_booking", request.appointment_id, reschedule_booking, function(is_update_appointment) {
                            if (is_update_appointment) {
                                var message = {
                                    sender_id: request.user_id,
                                    sender_type: USER_TYPE.USER,
                                    receiver_id: request.service_provider_id,
                                    type: 'service_provider',
                                    receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                    message: 'You have received reschedule request',
                                    title: 'Reschedule Booking Request',
                                    isaction_id:request.appointment_id,
                                    tag: 'reschedule_booking',
                                }
                                require('../../../config/common').prepare_notification(request.service_provider_id, message, function(notification) {
                                    callback(true, 'Appointment reschedule request send successfully', '1');
                                })
                            } else {
                                callback(null, t('restapi_globals_error'), '0');
                            }
                        });
                    }
                } else {
                    callback('2', "Appointment not found", null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        });

    },

    /**Function for get service provider list */
    service_provider_list: function(request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        var group_by = " GROUP BY v.id ";
        var condition = " v.status = 'Active' AND v.is_deleted = '0'";
        if (request.word) {
            condition += " AND (v.first_name LIKE '%" + request.word + "%' OR v.last_name LIKE '%" + request.word + "%') ";
        }
        if (request.radius) {
            var radius = request.radius;
        } else {
            var radius = 20000;
        }
        var latlong = {
            latitude: request.latitude,
            longitude: request.longitude,
        }
        user.distance(latlong, 'v', function(distance) {
            /**
             * service_provider
             */
            var sql = con.query("select v.*,'0' as ratting,'0' as review,b.id as business_id,b.name as business_address,IFNULL((SELECT b.status FROM tbl_bookmark b WHERE b.service_provider_id = v.id AND user_id = " + request.user_id + "),0) AS bookmark_status,CONCAT('" + global.S3_BUCKET_ROOT + global.SP_IMAGE + "','',v.profile_image) as vendor_image, " + distance + " from tbl_service_provider v LEFT JOIN tbl_business_location b ON b.service_provider_id = v.id where " + condition + " group by v.id HAVING distance <= " + radius + " LIMIT " + limit + ", " + per_page + "", function(err, result) {
                if (!err) {
                    if (result[0] != "") {
                        callback(result, "Service provider list found successfully", '1');
                    } else {
                        callback([], "Service provider list not found", '0');
                    }
                } else {
                    callback(null, t('restapi_globals_error'), '0');
                }
            })
        })
    },

    /**Function for post like */
    post_like: function(request, callback) {
        if (request.status == 1) {
            var like_post = {
                post_id: request.post_id,
                type: request.type,
                user_id: request.user_id,
                insertdate: datetime.create().format('Y-m-d H:M:S')
            }
            common.single_insert_data('tbl_like', like_post, function(is_like) {
                if (is_like) {
                    var sql = con.query("select count(id) as total_like from tbl_like l where l.post_id = " + request.post_id + "", function(err, result) {
                        if (!err) {
                            callback(result[0], 'Post liked successfully', '1');
                        } else {
                            callback(false, t('restapi_globals_error'), '0');
                        }
                    })
                } else {
                    callback(false, t('restapi_globals_error'), '0');
                }
            })
        } else {
            var condition = " post_id = " + request.post_id + " AND type = '" + request.type + "' AND user_id = '" + request.user_id + "' ";
            common.delete_data('tbl_like', condition, function(is_unlike) {
                if (is_unlike) {
                    var sql = con.query("select count(id) as total_like from tbl_like l where l.post_id = " + request.post_id + "", function(err, result) {
                        if (!err) {
                            callback(result[0], 'Post disliked successfully', '1');
                        } else {
                            callback(false, t('restapi_globals_error'), '0');
                        }
                    })
                } else {
                    callback(false, t('restapi_globals_error'), '0');
                }
            })
        }
    },

    user_review_list: (request, callback) => {
      const page = request.page - 1
      const limit = page * global.PER_PAGE
      const per_page = global.PER_PAGE
      con.query(`
        SELECT
          ur.id,
          ur.type,
          ur.review_id,
          ur.order_id,
          ur.ratting,
          ur.review,
          DATE_FORMAT(ur.insertdate, '%Y-%m-%d %H:%i:%s') AS review_given_date,
          DATE_FORMAT(tab.date, '%Y-%m-%d') AS order_date,
          DATE_FORMAT(tab.end_datetime, '%Y-%m-%d %H:%i:%s') AS end_datetime,
          tab.slot_time AS order_slot_time,
          GET_USER_REVIEWED_SERVICES_LIST(${request.user_id}, tab.id) AS services_json,
          IFNULL(ur.review_reply, '') AS review_reply,
          ur.service_provider_id,
          ur.user_id,
          CONCAT('${global.S3_BUCKET_ROOT}${global.SP_IMAGE}', s.profile_image) AS sp_profile_image,
          CONCAT(s.first_name, ' ', s.last_name) AS service_provider_name,
          DATE_FORMAT(ur.insertdate, '%Y-%m-%d %H:%i:%s') AS insert_date,
          (
            SELECT CONCAT(u.first_name, ' ', u.last_name)
            FROM tbl_user u
            WHERE id = ${request.user_id}
          ) AS user_name,
          GET_SERVICE_PROVIDER_RATING(ur.service_provider_id, NULL) AS avg_rating
        FROM tbl_user_review ur
        LEFT JOIN tbl_service_provider s ON s.id = ur.service_provider_id
        LEFT JOIN tbl_appointment_booking AS tab ON ur.order_id = tab.id
        WHERE ur.user_id = ${request.user_id}
        AND IF(${request.order_id} IS NULL, TRUE, ur.order_id = ${request.order_id})
        ORDER BY ur.id
        DESC LIMIT ${limit}, ${per_page}
      `, (err, result) => {
        if (!err) {
          if (result.length > 0) {
            callback(
              map(result, it => ({
                ...omit(it, ['services_json']),
                services: JSON.parse(it.services_json),
                service_name: JSON.parse(it.services_json).map((s) => s.name).join(', '),
              })),
              'Review list successfully',
              1,
            );
          } else {
            callback([], 'Review list not found', '0');
          }
        } else {
          logger.error({
            message: 'Failed to get user review list',
            error: err,
            meta: request,
          })
          callback([], t('restapi_globals_error'), '0');
        }
      });
    },

    /**Api for report post */
    report_post: function(request, callback) {
        var update_post = {
            is_report: '1',
            updatetime: datetime.create().format('Y-m-d H:M:S')
        }
        common.update_data('tbl_post', request.post_id, update_post, function(is_update) {
            if (is_update) {
                var insert_report = {
                    user_id: request.user_id,
                    post_id: request.post_id,
                    insertdate: datetime.create().format('Y-m-d H:M:S')
                }
                if (request.report_id) {
                    insert_report.report_id = request.report_id;
                }
                if (request.report_reason) {
                    insert_report.report_reason = request.report_reason;
                }
                common.single_insert_data('tbl_report_post', insert_report, function(is_add) {
                    callback(true, 'Post reported successfully', '1');
                })
            } else {
                callback(false, t('restapi_globals_error'), '0');
            }
        });
    },

    /** API for avilable now slot for filter*/
    avilable_now_slot: function(request, callback) {
        var current_time = moment().utc().add(6, 'hours').subtract(30, "minutes").format("HH:mm");
        con.query("select *,DAYNAME(CURDATE()) as day_name from tbl_service_provider_available_slot where is_deleted = '0' AND from_time > '" + current_time + "' having day = day_name", function(err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    var available_now_slot = [];
                    asyncloop(result, function(item, next) {
                        item.date = datetime.create().format('Y-m-d');
                        if (item) {
                            con.query("select * from tbl_service_provider_block_date where service_provider_id = " + item.service_provider_id + " AND business_location_id =" + item.business_location_id + " AND is_deleted = '0' AND date = CURDATE()", function(err1, block_result) {
                                var current_minute = moment().utc().format("mm");
                                const remainder = 30 - (current_minute % 30);
                                if (current_minute > 30 || item.from_time.split(':')[1] > 30) {
                                    var current_hour = parseInt(moment().utc().format("HH")) + parseInt(1);
                                    var start_time = current_hour + ':00';
                                } else {
                                    var start_time = moment().utc().format("HH") + ':' + (parseInt(current_minute) + parseInt(remainder));
                                }
                                item.timezone = request.timezone;
                                if (!err1 && block_result[0] != undefined && block_result[0] != "") {
                                    require("../service_provider/service_provider_model").get_time_slots(start_time, item.to_time, block_result[0], item, function(slots) {
                                        asyncloop(slots, function(item1, next1) {
                                            if (item1) {
                                                var is_slot = available_now_slot.includes(item1);
                                                if (is_slot) {
                                                    next1();
                                                } else {
                                                    available_now_slot.push(item1);
                                                    next1();
                                                }
                                            } else {
                                                next1();
                                            }
                                        }, function() {
                                            next();
                                        })
                                    })
                                } else {
                                    require("../service_provider/service_provider_model").get_time_slots(start_time, item.to_time, '', item, function(slots) {
                                        asyncloop(slots, function(item1, next1) {
                                            if (item1) {
                                                var is_slot = available_now_slot.includes(item1);
                                                if (is_slot) {
                                                    next1();
                                                } else {
                                                    available_now_slot.push(item1);
                                                    next1();
                                                }
                                            } else {
                                                next1();
                                            }
                                        }, function() {
                                            next();
                                        })
                                    })
                                }
                            })
                        } else {
                            next()
                        }
                    }, function() {
                        if (available_now_slot) {
                            callback([...new Set(available_now_slot)], 'Available slot found successfully', '1');
                        } else {
                            callback([], 'Available slot not found', '0');
                        }
                    })
                } else {
                    callback([], t('restapi_day_not_available'), '0');
                }
            } else {
                callback([], t('restapi_globals_error'), '0');
            }
        })
    },

    /** Function for get available dates*/
    calender_detail: async (request, callback) => {
        const days = require("../service_provider/service_provider_model").get_dates_from_month_year(request.month, request.year)
        var calender = [];
        asyncloop(days, function(item, next) {
            user.is_calender_detail(item.date, request, async (appointment) => {
                var data = {};
                if (appointment == 1) {
                    data = { date: item.date };
                    calender.push(data)
                    next();
                } else {
                    next();
                }
            })
        }, function() {
            if (calender != "") {
                callback('1', 'Calender detail found successfully', calender)
            } else {
                callback('0', 'Calender detail not found', [])
            }
        })
    },

    /**Function for is calender detail */
    is_calender_detail: async (date, req, callback) => {
        req.is_user_app = 1;
        req.date = date;
        const currentdate = moment().tz(req.timezone).format("YYYY-MM-DD");
        if (date < currentdate) {
            callback(0)
        } else {
            req = { ...req, ...req.body }
            const slots = await serviceProviderModelsV2.getAvailableSlots({ ...req, date })
            callback(slots[0] != undefined && slots.length !== 0 ? 1 : 0)
        }
    },

    /** Function for user follow and unfollow service provider*/
    followunfollow:function(request,callback){
        con.query("SELECT * FROM tbl_user_followers WHERE follow_by = '"+request.user_id+"' AND follow_to = '"+request.service_provider_id+"' AND follow_to_type = 'service_provider'",function(err,result){
            if(!err && result[0] != undefined){
                if(request.action_type == 'Unfollow'){
                    common.delete_data("tbl_user_followers","id = '"+result[0].id+"'",function(is_deleted){
                        if(is_deleted){
                            con.query("SELECT count(id) as total_followers FROM tbl_user_followers WHERE follow_to = '"+request.service_provider_id+"' AND follow_to_type = 'service_provider' GROUP BY follow_to",function(err1,result1){
                                var totalfollowers = (!err1 && result1[0] != undefined) ? result1[0].total_followers : 0;
                                callback('1',"Service Provider unfollow successfully",{"total_followers":totalfollowers,"total_following":0});
                            });
                        } else {
                            callback('0',t('restapi_globals_error'),null);
                        }
                    });
                } else {
                    callback('0',"You already follow this service provider",null);
                }
            } else {
                if(request.action_type == 'Unfollow'){
                    callback('0',"You can't unfollow this service provider. Because you are not following this service provider",null);
                } else {
                    var followparam = {
                        follow_by : request.user_id,
                        follow_to : request.service_provider_id,
                        follow_to_type : 'service_provider',
                        inserted_datetime : moment().utc().format("YYYY-MM-DD HH:ii:ss")
                    }
                    common.single_insert_data('tbl_user_followers',followparam,function(follow_id){
                        if(!follow_id){
                            callback('0',t('restapi_globals_error'),null);
                        } else {
                            con.query("SELECT * FROM tbl_user WHERE id = '"+request.user_id+"'",function(err,userdata){
                                var message = {
                                    sender_id: request.user_id,
                                    sender_type: USER_TYPE.USER,
                                    receiver_id: request.service_provider_id,
                                    type: 'service_provider',
                                    receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                    message: userdata[0].first_name+' '+userdata[0].last_name+' is now following you',
                                    title: 'Follow',
                                    isaction_id:follow_id,
                                    tag: 'unfollow',
                                }
                                require('../../../config/common').prepare_notification(request.service_provider_id, message, function(notification) {
                                    con.query("SELECT count(id) as total_followers FROM tbl_user_followers WHERE follow_to = '"+request.service_provider_id+"' AND follow_to_type = 'service_provider' GROUP BY follow_to",function(err1,result1){
                                        var totalfollowers = (!err1 && result1[0] != undefined) ? result1[0].total_followers : 0;
                                        callback('1',"Service Provider following successfully",{"total_followers":totalfollowers,"total_following":0});
                                    });
                                });
                            });
                        }
                    });
                }
            }
        });
    },
    service_history: function(request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        var condition = " ab.is_only_product = '0' AND ab.is_product = '0' AND ab.user_id = '"+request.user_id+"'";
        //AND ab.date < '"+moment().format("YYYY-MM-DD")+"'
        con.query("select date_format(ab.date, '%Y-%m-%d') as date,ab.slot_time,ab.id as appointment_id,date_format(ab.end_datetime, '%Y-%m-%d %H:%i:%s') as end_datetime,ab.booking_status,CONCAT(tsp.first_name,' ',tsp.last_name) as service_provider_name,CONCAT('" + global.S3_BUCKET_ROOT + global.SP_IMAGE + "','',tsp.profile_image) as sp_profile_image,IF((SELECT id FROM tbl_image WHERE type_id = ab.id AND type = 'complete_appointment' AND is_deleted = '0' GROUP BY type_id) IS NULL,'0','1') as is_uploaded_completeservice_image  from tbl_appointment_booking ab INNER JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id LEFT JOIN tbl_service_provider as tsp ON abd.service_provider_id = tsp.id where " + condition + " group by ab.id order by ab.id DESC LIMIT " + limit + ", " + per_page + "", function(err, result) {
            if (!err) {
                if (result.length > 0) {
                    asyncloop(result, function(item, next) {
                        if (item) {
                            con.query("select ts.id as service_id,ts.service_name,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',ts.service_image) as service_image from tbl_appointment_booking_detail abd LEFT JOIN tbl_service as ts ON abd.service_id = ts.id where abd.appointment_id = " + item.appointment_id + " AND abd.type = 'Service' ORDER BY abd.id ASC", function(err1, result1) {
                                if (!err1 && result1[0] != undefined && result1[0] != "") {
                                    item.service_images = result1;
                                } else {
                                    item.service_images = [];
                                }
                                next()
                            })
                        } else {
                            next()
                        }
                    }, function() {
                        callback('1', t('rest_keywords_service_history_found'), result);
                    })
                } else {
                    callback('2', t('rest_keywords_service_history_not_found'), null);
                }
            } else {
                callback('0', t('restapi_globals_error') + err, null);
            }
        });
    },
}

module.exports = user;
