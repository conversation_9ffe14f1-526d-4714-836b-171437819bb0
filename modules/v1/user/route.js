const common = require('../../../config/common')
const {
  decrypt,
  validate,
  encrypt,
  addUser,
  userExists,
  sendSMS,
  USER_STATUS,
  updateUser,
  sendOTP,
  verifyOTP,
  USER_LOGIN_STATUS,
  updateUserDeviceInfoForUser,
  createBooking,
  getActivePromoPageForClient,
  getPromoById,
  applyPromo,
  cancelBooking,
  checkUserLogin,
  getHomeScreenDataForUser,
  getUserBookingDetails,
  getSlotsForBooking,
  getDatesForBooking,
  rescheduleBooking,
  getBusinessLocation,
  preparePushNotification,
  getServices,
  USER_TYPE,
  getUserBookings,
  findClosestServiceProviderPage,
  getServiceProvidersFollowedByUser,
  purchaseGiftCard,
  getActiveReportReasons,
} = require('../../../src/service')
const startCase = require('lodash/startCase')
const globals = require('../../../config/constant')
const user_model = require('../user/user_model')
const datetime = require('node-datetime')
const router = require('express-promise-router')()
const template = require('../../../config/template')
const { t } = require('localizify')
const moment = require('moment')
const {
  logger,
  dateStringInTz,
  joinWithOxfordComma,
  SIMPLE_DATE_FORMAT,
} = require('../../../src/utils')
const { generateOTP } = require('../../../src/utils/otp')

router.post('/sign_up', async (req, res) => {
  const rawBody = decrypt(req.body)
  const rules = {
    first_name: 'required',
    last_name: 'required',
    email: 'required|email',
    password: 'required_if:login_type,S',
    country_code: 'required',
    login_type: 'required|in:S,F,G,A',
    social_id: 'required_unless:login_type,S',
    phone: 'required',
    device_token: 'required',
    device_type: 'required|in:A,I',
  }
  const message = {
    required: t('required'),
    required_if: t('required_if'),
    email: t('email'),
    in: t('in'),
  }
  const keyword = {
    first_name: t('keyword_firstname'),
    email: t('keyword_email'),
    password: t('keyword_password'),
    country_code: t('keyword_country_code'),
    phone: t('keyword_phone'),
    device_type: t('keyword_devicetype'),
    device_token: t('keyword_devicetoken'),
  }
  const errors = validate(rawBody, rules, message, keyword)
  if (errors) {
    sendError(res, errors)
    return
  }

  const exists = await userExists(
    rawBody.email,
    rawBody.phone,
    rawBody.social_id
  )

  if (exists) {
    sendError(res, {
      message: 'Given email, phone, or social account is already in use',
    })
    return
  }

  const data = await addUser(rawBody)
  sendSuccess(res, { data })
})

function sendSuccess(res, data = {}) {
  if (res.encryption === 'none') res.status(200).json({ code: 1, ...data })
  else res.status(200).json(encrypt({ code: 1, ...data }))
}

function sendError(res, errors = {}) {
  if (res.encryption === 'none') res.status(400).json({ code: 0, ...errors })
  else res.status(400).json(encrypt({ code: 0, ...errors }))
}

/*send and resend otp api*/
router.post('/send_otp', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    user_id: 'required',
    status: 'required|in:signup,forgotpassword',
  }
  const messages = { required: t('required') }
  const keywords = { user_id: t('keyword_userid') }
  const errors = validate(rawData, rules, messages, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await sendOTP(rawData)
  sendSuccess(res, { data })
})

router.post('/verify_otp', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    status: 'required|in:signup,forgotpassword',
    user_id: 'required',
    otp: 'required',
  }
  const messages = { required: t('required') }
  const keywords = { user_id: t('keyword_user_id'), otp: t('keyword_otpcode') }
  const errors = validate(rawData, rules, messages, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await verifyOTP(rawData)
  sendSuccess(res, { data })
})
router.post('/login', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    device_token: 'required',
    device_type: 'required|in:A,I',
    login_type: 'required|in:S,F,G,A',
    password: 'required_if:login_type,S',
    email: rawData.email && rawData.login_type === 'S' ? 'required|email' : '',
    phone: rawData.phone && rawData.login_type === 'S' ? 'required' : '',
  }
  const message = {
    required: 'The :attr field is required',
    required_if: 'The :attr field is required when :other is :value',
    email: 'The :attr must be a valid email address',
  }
  const keywords = {
    email: 'Email',
    password: 'Password',
    device_token: 'Device token',
    device_type: 'Device type',
  }
  const errors = validate(rawData, rules, message, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  sendSuccess(res, await checkUserLogin(rawData))
})
router.post('/forgot_password', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      email: 'required',
    }
    var messages = {
      required: t('required'),
      required_if: t('required_if'),
      in: t('in'),
    }
    var keywords = {
      status: t('keyword_status'),
      email: t('keyword_email'),
      phone: t('keyword_phone'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.status = 'E'
      user_model.check_email_phone(request, function (response) {
        if (response == null) {
          common.send_response(
            req,
            res,
            '0',
            t('restapi_loginemail_error'),
            null
          )
        } else {
          var userdata = response
          var rancode = generateOTP(6, 'numeric')
          var forgotpass_otp = {
            forgot_otp: rancode,
            forgot_otp_verify: 'Pending',
            forgotpwd_datetime: datetime.create().format('Y-m-d H:M:S'),
          }
          user_model.update_user(
            userdata.id,
            forgotpass_otp,
            function (response) {
              if (response) {
                userdata.forgot_otp = forgotpass_otp.forgot_otp
                userdata.forgotpwd_datetime = forgotpass_otp.forgotpwd_datetime
                template.send_otp(userdata, function (result) {
                  var subject = globals.APP_NAME + ' Forgot Password'
                  common.send_email(
                    subject,
                    userdata.email,
                    result,
                    function (result) {
                      if (!result) {
                        common.send_response(
                          req,
                          res,
                          '0',
                          t('restapi_mail_error'),
                          null
                        )
                      } else {
                        common.send_response(
                          req,
                          res,
                          '1',
                          t('restapi_forgotpassword_success'),
                          userdata
                        )
                      }
                    }
                  )
                })
              } else {
                common.send_response(
                  req,
                  res,
                  '0',
                  t('restapi_forgotpass_error'),
                  null
                )
              }
            }
          )
        }
      })
    }
  })
})

router.post('/newpassword', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      user_id: 'required',
      password: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_user_id'),
      password: t('rest_keywords_password'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      user_model.resetpassword(
        request,
        function (responsecode, responsemsg, responsedata) {
          common.send_response(
            req,
            res,
            responsecode,
            responsemsg,
            responsedata
          )
        }
      )
    }
  })
})
router.get('/logout', function (req, res) {
  var updparam = {
    login_status: 'Offline',
  }
  user_model.update_user(req.user_id, updparam, function (response) {
    if (response) {
      var update_device = {
        token: '',
        device_token: '',
        user_type: 'user',
      }
      common.save_user_deviceinfo(
        req.user_id,
        update_device,
        function (response, err) {
          if (!err) {
            common.send_response(
              req,
              res,
              '1',
              t('restapi_logout_success'),
              null
            )
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              null
            )
          }
        }
      )
    }
  })
})
router.post('/user_details', function (req, res) {
  logger.info(`Getting user details for ${req.user_id}`)
  user_model.get_user_detail(req.user_id, function (response, errobj) {
    if (response != null) {
      common.send_response(req, res, '1', t('restapi_user_detail'), response)
    } else {
      common.send_response(req, res, '2', t('restapi_user_notfound'), response)
    }
  })
})
router.post('/change_password', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      old_password: 'required',
      new_password: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      old_password: t('keyword_old_password'),
      new_password: t('keyword_new_password'),
    }
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.user_id = req.user_id
      user_model.change_password(request, function (response, Message, code) {
        common.send_response(req, res, code, Message, response)
      })
    }
  })
})
router.post('/updatedeviceinfo', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      latitude: 'required',
      longitude: 'required',
      device_token: 'required',
      device_type: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keyword = {
      latitude: t('keyword_latitude'),
      longitude: t('keyword_longitude'),
    }
    if (common.check_validation(request, res, rules, message, keyword)) {
      var user_update = {
        latitude: request.latitude,
        longitude: request.longitude,
      }
      user_model.update_user(req.user_id, user_update, function (response) {
        if (response) {
          var updateparam = {
            device_token: request.device_token,
            user_type: 'user',
            device_type: request.device_type,
            uuid:
              request.uuid != undefined && request.uuid != ''
                ? request.uuid
                : '',
            ip: request.ip != undefined && request.ip != '' ? request.ip : '',
            os_version:
              request.os_version != undefined && request.os_version != ''
                ? request.os_version
                : '',
            model_name:
              request.model_name != undefined && request.model_name != ''
                ? request.model_name
                : '',
          }
          common.save_user_deviceinfo(
            req.user_id,
            updateparam,
            function (callback) {
              if (callback != null) {
                user_model.get_user_detail(
                  req.user_id,
                  function (userdata, err) {
                    if (userdata == null) {
                      common.send_response(
                        req,
                        res,
                        '0',
                        t('restapi_user_notfound'),
                        null
                      )
                    } else {
                      common.send_response(
                        req,
                        res,
                        '1',
                        t('restapi_location_success'),
                        userdata
                      )
                    }
                  }
                )
              } else {
                response(null, t('restapi_login_errormsg'), '0')
              }
            }
          )
        } else {
          common.send_response(req, res, '2', t('restapi_user_notfound'), null)
        }
      })
    }
  })
})

router.post('/edit_profile', function (req, res) {
  var user_id = req.user_id
  common.decryption(req.body, function (request) {
    var rules = {
      first_name: 'required',
      last_name: 'required',
      email: 'required|email',
      country_code: 'required',
      phone: 'required',
      profile_image: '',
    }
    var messages = {
      required: t('required'),
      email: t('email'),
    }
    var keywords = {
      name: t('keyword_name'),
      email: t('keyword_email'),
      country_id: t('keyword_countryid'),
      phone: t('keyword_phone'),
      profile_image: t('keyword_profile_image'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      user_model.get_user_detail(user_id, function (user, err) {
        if (err == null) {
          if (user == null) {
            common.send_response(
              req,
              res,
              '2',
              t('restapi_user_notfound'),
              null
            )
          } else {
            var updparam = {
              first_name: request.first_name,
              last_name: request.last_name,
              email: request.email,
              country_code: request.country_code,
              phone: request.phone,
            }
            if (
              request.profile_image != undefined &&
              request.profile_image != ''
            ) {
              updparam.profile_image = request.profile_image
            }
            if (request.phone != user.phone && user.login_type == 'S') {
              updparam.otp_verify = 'Pending'
              var otp = generateOTP(6, 'numeric')
              updparam.otp = otp
            }
            var unique_key = { email: request.email, phone: request.phone }
            user_model.checkuser_unique(
              user_id,
              unique_key,
              function (response, error) {
                if (response == false) {
                  common.send_response(req, res, '0', error, null)
                } else {
                  user_model.update_user(
                    user_id,
                    updparam,
                    function (response) {
                      if (response) {
                        var clientparam = {
                          customer_name:
                            request.first_name + ' ' + request.last_name,
                          email: request.email,
                          country_code: request.country_code,
                          phone: request.phone,
                        }
                        if (
                          request.profile_image != undefined &&
                          request.profile_image != ''
                        ) {
                          clientparam.profile_image = request.profile_image
                        }
                        common.update_data_condition(
                          'tbl_service_provider_client',
                          "email = '" +
                            user.email +
                            "' AND country_code = '" +
                            user.country_code +
                            "' AND phone = '" +
                            user.phone +
                            "'",
                          clientparam,
                          function (is_updated) {
                            user_model.get_user_detail(
                              user_id,
                              function (userdata) {
                                if (userdata == null) {
                                  common.send_response(
                                    req,
                                    res,
                                    '2',
                                    t('restapi_user_notfound'),
                                    null
                                  )
                                } else {
                                  if (
                                    request.phone != user.phone &&
                                    user.login_type == 'S'
                                  ) {
                                    common.send_response(
                                      req,
                                      res,
                                      '4',
                                      t('restapi_loginotpverify_error'),
                                      userdata
                                    )
                                  } else {
                                    common.send_response(
                                      req,
                                      res,
                                      '1',
                                      t('restapi_editprofile_success'),
                                      userdata
                                    )
                                  }
                                }
                              }
                            )
                          }
                        )
                      } else {
                        common.send_response(
                          req,
                          res,
                          '2',
                          t('restapi_user_notfound'),
                          null
                        )
                      }
                    }
                  )
                }
              }
            )
          }
        } else {
          common.send_response(req, res, '0', t('restapi_globals_error'), null)
        }
      })
    }
  })
})

router.post(
  '/homescreen',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = {
      page: 'required',
      latitude: 'required',
      longitude: 'required',
    }
    const messages = { required: t('required') }
    const keywords = {
      latitude: t('keyword_latitude'),
      longitude: t('keyword_longitude'),
    }
    const errors = validate(rawData, rules, messages, keywords)

    if (errors) {
      return sendError(res, errors)
    }

    rawData.user_id = req.user_id
    rawData.user_type = req.user_type
    const data = await getHomeScreenDataForUser(rawData)
    return sendSuccess(res, { data })
  })

/** Api for make booking payment like GooglePay or ApplePay */
router.post('/make_userbookingpayment', function (req, res) {
  common.decryption(req.body, function (request) {
    if (request.order_type == 'giftCard') {
      var rules = {
        giftcard_id: 'required',
        sub_total: 'required',
        transaction_charge: 'required',
        total_amount: 'required',
        payment_from: 'required|in:ApplePay,GooglePay',
        transaction_id: 'required',
      }
      const messages = {
        required: t('required'),
      }
    } else {
      var rules = {
        business_location_id: 'required',
        service_provider_id: 'required',
        payment_mode: 'required|in:pay_now,pay_later',
        payment_from: 'required|in:ApplePay,GooglePay,Card',
        transaction_id: 'required',
        tax: 'required',
        sub_total: 'required',
        total_amount: 'required',
      }
      var message = {
        required: t('required'),
        in: t('in'),
        required_if: t('required_if'),
      }
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.user_id = req.user_id
      user_model.make_userbookingpayment(
        request,
        function (response, message, code) {
          common.send_response(req, res, code, message, response)
        }
      )
    }
  })
})

router.post('/user_booking', async (req, res, next) => {
  try {
    let rawData = decrypt(req.body)

    const rules = {
      business_location_id: 'required',
      service_provider_id: 'required',
      payment_mode: 'required|in:pay_now,pay_later',
      payment_from: 'required|in:Card,Wallet,ApplePay,GooglePay',
      card_id: 'required_if:payment_from,Card',
      transaction_id: 'required_if:payment_mode,ApplePay,GooglePay',
      payment_intent_id: 'required_if:payment_mode,ApplePay,GooglePay',
      service: '',
      product: '',
      tip: '',
      description: '',
      tax: 'required',
      sub_total: 'required',
      promocode: '',
      discount: '',
      wallet_amount: '',
      total_amount: 'required',
      remaining_amount: '',
      total_duration: 'required',
      additional_duration: '',
      is_tip_after_service: 'required',
      image: '',
    }
    const message = {
      required: t('required'),
      in: t('in'),
      required_if: t('required_if'),
    }
    const errors = validate(rawData, rules, message, {})

    if (errors) {
      sendError(res, {
        message: 'Failed request validation',
        data: errors.message,
      })
      return
    }

    if (req.user_id !== undefined) rawData.user_id = req.user_id

    rawData.time_slot = moment(`${rawData.date} ${rawData.time_slot}`).format(
      'HH:mm'
    )
    const data = await createBooking(rawData)
    if (!data) sendError(res, { message: 'Failed to book an appointment' })

    let apt_datetime = moment(data.date).format('YYYY-MM-DD')
    apt_datetime = moment(`${apt_datetime} ${data.slot_time}`).format(
      'MMM DD, h:mm A'
    )

    const sms_msg =
      `Hi, ${data.c_first_name}! Your appointment has been ` +
      `scheduled with ${data.sp_first_name} on ${apt_datetime}. ` +
      'To avoid any late fees, please reschedule or cancel your appointment through the Blookd App ' +
      `at ${globals.BASE_URL}/home/<USER>/${data.id}`

    logger.info({
      meessage: `Sending sms confirmation for an appointment`,
      data: sms_msg,
    })
    try {
      await sendSMS(data.c_phone, sms_msg)
    } catch (error) {
      logger.error({ message: 'Failed to sendSMS', error })
    }

    return common.send_response(req, res, 1, 'Appointment has been booked.', {
      ...data,
      date: data.date ? moment(data.date).format('YYYY-MM-DD') : data.date,
      end_datetime: data.end_datetime_formatted ?? data.end_datetime,
    })
  } catch (e) {
    logger.error('Failed to book an appointment', e)
    return sendError(res, {
      message: `Failed to book an appointment: ${e?.message}`,
    })
  }
})

router.post(
  '/service_provider_detail',
  async (req, res) => {
    const rawBody = decrypt(req.body)
    const rules = { service_provider_id: 'required', page: 'required' }
    const message = { required: t('required') }
    const errors = validate(rawBody, rules, message, {})
    if (errors) {
      return sendError(res, errors)
    }

    rawBody.user_id = req.user_id
    user_model.service_provider_detail(
      rawBody,
      async (response, message, code) => {
        const timezone = response.business_timezone_code || 'America/Denver'
        common.send_response(req, res, code, message, {
          ...response,
          nearestTimeSlot: (await getSlotsForBooking({
            service_provider_id: response.id,
            business_location_id: response.business_id,
            date: dateStringInTz(
              moment().tz(timezone).format(SIMPLE_DATE_FORMAT),
              SIMPLE_DATE_FORMAT,
              timezone,
            ).format(SIMPLE_DATE_FORMAT),
            total_duration: response.business_time_slot_duration_minutes || 30,
          }))[0] || null,
        })
      },
    )
  })

/**Api for sp book mark */
router.post('/add_update_bookmark', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      service_provider_id: 'required',
      status: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.user_id = req.user_id
      user_model.add_update_bookmark(
        request,
        function (response, message, code) {
          common.send_response(req, res, code, message, response)
        }
      )
    }
  })
})
router.post('/all_service', async (req, res) => {
  const rawBody = decrypt(req.body)
  const { service_provider_id, category_id, page } = rawBody
  logger.info({ message: 'Getting all the services', meta: { service_provider_id, category_id, page }})
  const data = await getServices(
    service_provider_id,
    category_id,
    req.user_id,
    page
  )
  sendSuccess(res, { data })
})
router.post('/get_available_slot', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    business_location_id: 'required',
    service_provider_id: 'required',
    timezone: 'required',
    date: 'required',
    total_duration: 'required',
    current_time: '',
  }
  const message = { required: t('required') }
  const keywords = {}
  const errors = validate(rawData, rules, message, keywords)
  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await getSlotsForBooking(rawData)

  sendSuccess(res, {
    message:
      data.length === 0
        ? 'This service is not available on this date'
        : 'Slot found successfully',
    data,
    code: data.length === 0 ? 2 : 1,
  })
})
router.post('/add_card', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      holder_name: 'required',
      number: 'required',
      expiry_month: 'required',
      expiry_year: 'required',
      cvv: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      holder_name: t('keyword_cardholdername'),
      number: t('keyword_cardnumber'),
      expiry_month: t('keyword_expirymonth'),
      expiry_year: t('keyword_expiryyear'),
      cvv: t('keyword_cvv'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      user_model.add_card(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/**api for get customer card list */
router.post('/customer_card_list', function (req, res) {
  var request = {
    user_id: req.user_id,
  }
  user_model.card_list(
    request,
    function (responseCode, responseMsg, responseData) {
      common.send_response(req, res, responseCode, responseMsg, responseData)
    }
  )
})

/**Api for customer delete card */
router.post('/update_card', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      card_id: 'required',
      type: 'required',
      primary_card: '',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      user_model.update_card(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

router.post('/applypromocode', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    promocode: 'required',
    sub_total: 'required',
    service_provider_id: 'required',
    service_id: '',
    product_id: '',
  }
  const messages = { required: 'required' }
  const keywords = { promocode: 'promocode', sub_total: 'Sub total' }
  const errors = validate(rawData, rules, messages, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  rawData.user_id = req.user_id
  const data = await applyPromo(rawData)
  sendSuccess(res, {
    message: `Promo '${rawData.promocode}' applied successfully!`,
    data,
  })
})
router.post('/my_booking', async (req, res) => {
  const rawBody = decrypt(req.body)
  const rules = { page: 'required' }
  const messages = { required: t('required') }
  const errors = validate(rawBody, rules, messages, {})
  if (errors) {
    return sendError(res, errors)
  }

  const data = await getUserBookings({ ...rawBody, user_id: req.user_id })
  sendSuccess(res, { data })
})

router.post('/my_product_booking', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      user_model.my_product_booking(
        request,
        req.user_id,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/**Api for get favourite list */
router.post('/favourite_list', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      user_model.favourite_list(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/**Favourite list for product */
router.post('/product_favourite_list', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      user_model.product_favourite_list(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/**Api for gift card list */
router.post('/giftcard_list', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {}
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      user_model.giftcard_list(
        req.user_id,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/**Api for get my giftcard */
router.post('/my_giftcard', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {}
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      user_model.my_giftcard(
        req.user_id,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/**Api for gift card detail */
router.post('/giftcard_detail', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      giftcard_id: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      user_model.giftcard_detail(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})
router.post('/booking_detail', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = { appointment_id: 'required' }
  const messages = { required: 'The :attr field is required' }
  const keywords = {}
  const errors = validate(rawData, rules, messages, keywords)
  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await getUserBookingDetails(rawData)
  sendSuccess(res, { data })
})

/** Api for make after tip payment like GooglePay or ApplePay */
router.post('/make_aftertippayment', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      appointment_id: 'required',
      payment_from: 'required|in:Card,Wallet,ApplePay,GooglePay',
      transaction_id: 'required_if:payment_mode,ApplePay,GooglePay',
      tip_amount: 'required',
    }
    var message = {
      required: t('required'),
      in: t('in'),
      required_if: t('required_if'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.user_id = req.user_id
      user_model.make_useraftertippayment(
        request,
        function (response, message, code) {
          common.send_response(req, res, code, message, response)
        }
      )
    }
  })
})

router.post('/done_aftertippayment', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      appointment_id: 'required',
      transaction_id: 'required',
      tip_amount: 'required',
    }
    var message = {
      required: t('required'),
      in: t('in'),
      required_if: t('required_if'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.user_id = req.user_id
      user_model.done_useraftertippayment(
        request,
        function (response, message, code) {
          common.send_response(req, res, code, message, response)
        }
      )
    }
  })
})
router.post(
  '/purchase_giftcard',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = {
      giftcard_id: 'required',
      sub_total: 'required',
      transaction_charge: 'required',
      total_amount: 'required',
      payment_from: 'required|in:Card,Wallet,ApplePay,GooglePay',
      card_id: 'required_if:payment_from,Card',
      transaction_id: 'required_if:payment_mode,ApplePay,GooglePay',
    }
    const messages = {
      required: t('required'),
      required_if: t('required_if'),
    }
    const errors = validate(rawData, rules, messages, {})
    if (errors) {
      sendError(res, errors)
      return
    }

    const data = await purchaseGiftCard({ ...rawData, user_id: req.user_id })
    sendSuccess(res, { data })
  })

/**Api for apply giftcard amount */
router.post('/redeem_giftcard', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      giftcard_code: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      user_model.redeem_giftcard(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/**Api for portfolio */
router.post('/portfolio_list', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
      service_provider_id: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      require('../service_provider/service_provider_model').post_list(
        request,
        function (responseData, responseMsg, responseCode) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})
router.post('/wallet_amount', function (req, res) {
  common.decryption(req.body, function (request) {
    user_model.wallet_amount(req.user_id, function (response, message, code) {
      common.send_response(req, res, code, message, response)
    })
  })
})
router.post('/wallet_history', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.user_id = req.user_id
      user_model.wallet_history(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

/**Api for service provider detail review */
router.post('/service_provider_detail_review', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      service_provider_id: 'required',
      page: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      user_model.service_provider_detail_review(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/**Api for user add review */
router.post('/service_provider_review_list', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {}
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      user_model.service_provider_review_list(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

router.post(
  '/add_review',
  async (req, res) => {
    const rawBody = decrypt(req.body)
    const rules = {
      ratting: 'required',
      service_provider_id: 'required',
      review_id: '',
      type_id: 'required',
      type: 'required',
      order_id: 'required',
      review: '',
    }
    const messages = { required: t('required') }
    const errors = validate(rawBody, rules, messages, {})
    if (errors) {
      return sendError(res, errors)
    }
    rawBody.user_id = req.user_id
    user_model.add_review(
      rawBody,
      (responseCode, responseMsg, responseData) => {
        common.send_response(
          req,
          res,
          responseCode,
          responseMsg,
          responseData,
        )
      },
    )
  })

router.post('/edit_review', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      ratting: 'required',
      service_provider_id: 'required',
      review_id: '',
      type_id: 'required',
      type: 'required',
      order_id: 'required',
      review: '',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      user_model.update_review(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

router.post('/cancel_reason_list', function (req, res) {
  req.service_provider_id = req.user_id
  req.user_type = 'user'
  require('../service_provider/service_provider_model').cancel_reason_list(
    req,
    function (msgcode, message, response) {
      common.send_response(req, res, msgcode, message, response)
    }
  )
})

router.post('/cancel_appointment', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = { appointment_id: 'required', cancel_reason: '', cancel_id: '' }
  const validationMessage = { required: t('required') }
  const errors = validate(rawData, rules, validationMessage, {})
  if (errors) {
    return sendError(res, errors)
  }

  const { code, message, booking } = await cancelBooking(
    rawData.appointment_id,
    req.user_id,
    rawData
  )

  try {
    const serviceNameFormatted = joinWithOxfordComma(booking.service_name)
    const aptDate = moment(
      `${moment(booking.date).format('YYYY-MM-DD')} ${booking.slot_time}`
    ).format('LL [at] h:mm A')
    const spMsg =
      `${booking.c_first_name} has canceled an appointment for ` +
      `${aptDate} for ${serviceNameFormatted}.`
    const userMsg =
      `You have canceled your appointment for ${aptDate} with ` +
      `${booking.sp_first_name} for ${serviceNameFormatted}.`
    await preparePushNotification({
      title: parseInt(booking.is_only_product ?? 0)
        ? 'Product Booking Cancelled'
        : 'Appointment Cancelled',
      message: parseInt(booking.is_only_product ?? 0)
        ? 'Product Booking has been cancelled'
        : spMsg,
      sender_id: booking.user_id,
      receiver_id: booking.service_provider_id,
      sender_type: startCase(USER_TYPE.CUSTOMER),
      type: USER_TYPE.SERVICE_PROVIDER,
      receiver_type: USER_TYPE.SERVICE_PROVIDER,
      isaction_id: booking.id,
      tag: 'booking',
    })

    await preparePushNotification({
      title: parseInt(booking.is_only_product ?? 0)
        ? 'Product Booking Cancelled'
        : 'Appointment Cancelled',
      message: parseInt(booking.is_only_product ?? 0)
        ? 'Product Booking has been cancelled'
        : userMsg,
      sender_id: 0,
      receiver_id: booking.user_id,
      sender_type: USER_TYPE.ADMIN,
      type: USER_TYPE.USER,
      receiver_type: USER_TYPE.USER,
      isaction_id: booking.id,
      tag: 'booking',
    })
  } catch (error) {
    logger.error({ function: 'cancel_appointment', error })
  }

  sendSuccess(res, { code, message })
})
router.post(
  '/report_reason_list',
  async (req, res) => {
    const data = await getActiveReportReasons()
    sendSuccess(res, { data })
  }
)

/**Api for report appointment */
router.post('/report_service', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      appointment_id: 'required',
      report_id: '',
      report_reason: '',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      appointment_id: t('keyword_appointment_id'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      user_model.report_service(request, function (code, message, responce) {
        common.send_response(req, res, code, message, responce)
      })
    }
  })
})

router.post(
  '/feed_list',
  (req, res) => {
    common.decryption(
      req.body,
      (request) => {
        const rules = {
          page: 'required',
          service_provider_id: '',
        }
        const messages = {
          required: t('required'),
        }
        const keywords = {
          appointment_id: t('keyword_appointment_id'),
        }
        if (common.check_validation(request, res, rules, messages, keywords)) {
          request.user_id = req.user_id
          user_model.feed_list(request, function(response, message, code) {
            common.send_response(req, res, code, message, response)
          })
        }
      })
  })

/**
 * Api for get post detail
 */
router.post('/post_detail', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      post_id: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      post_id: 'Post Id',
    }
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      user_model.post_detail(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

router.post('/get_promotion', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = { page: 'required', service_provider_id: '' }
  const message = { required: 'required' }
  const keywords = { page: 'page' }
  const errors = validate(rawData, rules, message, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await getActivePromoPageForClient(
    rawData.service_provider_id,
    req.user_id,
    rawData.page
  )
  sendSuccess(res, { data })
})

router.post('/promotion_detail', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = { promotion_id: 'required' }
  const message = { required: 'required' }
  const keywords = { promotion_id: 'Promotion id' }
  const errors = validate(rawData, rules, message, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await getPromoById(rawData.promotion_id)
  sendSuccess(res, { data })
})

/**Api for product booking */
router.post('/product_booking', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      business_location_id: 'required',
      service_provider_id: 'required',
      payment_mode: 'required|in:pay_now,pay_later',
      payment_from: 'required|in:Card,Wallet,Cash,ApplePay,GooglePay',
      card_id: 'required_if:payment_from,Card',
      transaction_id: 'required_if:payment_mode,ApplePay,GooglePay',
      payment_intent_id: 'required_if:payment_mode,ApplePay,GooglePay',
      product: '',
      tip: '',
      description: '',
      tax: 'required',
      sub_total: 'required',
      promocode: '',
      discount: '',
      wallet_amount: '',
      total_amount: 'required',
    }
    var message = {
      required: t('required'),
      in: t('in'),
      required_if: t('required_if'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.user_id = req.user_id
      user_model.product_booking(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

/**Api for tip after service and payment */
router.post('/booking_payment', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      appointment_id: 'required',
      card_id: 'required',
      tip: '',
      tax: 'required',
      sub_total: 'required',
      wallet_amount: '',
      total_amount: 'required',
    }
    var message = {
      required: t('required'),
      in: t('in'),
      required_if: t('required_if'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.user_id = req.user_id
      user_model.booking_payment(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})
router.post('/reschedule_appointment', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    appointment_id: 'required',
    business_location_id: 'required',
    time_slot: 'required',
    date: 'required',
    total_duration: 'required',
    additional_duration: '',
  }
  const message = {
    required: t('required'),
    in: t('in'),
    required_if: t('required_if'),
  }
  const errors = validate(rawData, rules, message, {})
  if (errors) {
    return sendError(res, errors)
  }

  const info = await rescheduleBooking({ ...rawData, user_id: req.user_id })
  sendSuccess(res, info)
})

router.post(
  '/service_provider_list',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = { latitude: 'required', longitude: 'required', page: 'required' }
    const errors = validate(rawData, rules, { required: t('required') }, {})
    if (errors) {
      return sendError(res, errors)
    }

    const providersPage = await findClosestServiceProviderPage({ ...rawData, user_id: req.user_id })
    const data = await Promise.all(
      providersPage.map(async (provider) => {
        const timezone = provider.business_timezone_code || 'America/Denver'
        return {
          ...provider,
          nearestTimeSlot: (await getSlotsForBooking({
            service_provider_id: provider.service_provider_id,
            business_location_id: provider.business_id,
            date: dateStringInTz(
              moment().tz(timezone).format(SIMPLE_DATE_FORMAT),
              SIMPLE_DATE_FORMAT,
              timezone,
            ).format(SIMPLE_DATE_FORMAT),
            total_duration: provider.business_time_slot_duration_minutes || 30,
          }))[0] || null,
        }
      })
    )
    sendSuccess(res, { data })
  })

router.post('/notification_list', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      request.receiver_type = USER_TYPE.USER
      common.get_notification(request, function (response, resmsg, msgcode) {
        common.send_response(req, res, msgcode, resmsg, response)
      })
    }
  })
})

/**Contact us api*/

router.post('/contact_us', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      subject: 'required',
      email: 'required|email',
      description: 'required',
    }

    const messages = {
      required: t('required'),
      email: t('email'),
    }

    var keywords = {
      email: t('keyword_email'),
      subject: t('keyword_subject'),
      description: t('keyword_description'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      var contact_params = {
        user_id: req.user_id,
        subject: request.subject,
        email: request.email,
        type: 'user',
        description:
          request.description != undefined && request.description != ''
            ? request.description
            : '',
        insertdatetime: datetime.create().format('Y-m-d H:M:S'),
      }
      common.single_insert_data(
        'tbl_contactus',
        contact_params,
        function (response) {
          if (response != null) {
            template.contact_us(response, function (result) {
              var subject = globals.APP_NAME + ' - Customer Contact Us'
              common.send_email(
                subject,
                globals.ADMIN_MAIL,
                result,
                function (result) {
                  if (result) {
                    common.send_response(
                      req,
                      res,
                      '1',
                      t('restapi_contactus_success'),
                      result
                    )
                  } else {
                    common.send_response(
                      req,
                      res,
                      '0',
                      t('restapi_mail_error'),
                      result
                    )
                  }
                }
              )
            })
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              response
            )
          }
        }
      )
    }
  })
})

/**
 * Api for get profile review list
 */
router.post('/user_review_list', function (req, res) {
  common.decryption(req.body, function (request) {
    const rules = {
      page: 'required',
      order_id: '',
    }
    const messages = {
      required: t('required'),
    }
    const keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      request.type = 'user'
      request.order_id = request.order_id || null
      user_model.user_review_list(
        request,
        function (response, resmsg, msgcode) {
          common.send_response(req, res, msgcode, resmsg, response)
        }
      )
    }
  })
})

router.post('/report_post', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      post_id: 'required',
      report_id: '',
      report_reason: '',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      user_model.report_post(request, function (response, resmsg, msgcode) {
        common.send_response(req, res, msgcode, resmsg, response)
      })
    }
  })
})
router.post('/product_service_category', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      service_provider_id: '',
    }
    var message = {
      required: t('required'),
      in: t('in'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, message, keywords)) {
      user_model.category_list(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})
router.post('/avilable_now_slot', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      service_provider_id: '',
      timezone: 'required',
    }
    var message = {
      required: t('required'),
      in: t('in'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, message, keywords)) {
      user_model.avilable_now_slot(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})
router.post('/available_calender_date', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    business_location_id: 'required',
    service_provider_id: 'required',
    month: 'required',
    year: 'required',
  }
  const message = { required: t('required') }
  const keywords = {}
  const errors = validate(rawData, rules, message, keywords)
  if (errors) {
    return sendError(res, errors)
  }

  const format = 'YYYY-MM-DD'
  const businessLocation = await getBusinessLocation(
    rawData.business_location_id,
    rawData.service_provider_id
  )
  const now = moment.tz(businessLocation?.timezone_code)
  const requestedMonth = dateStringInTz(
    `${rawData.year}-${rawData.month}-1`,
    format,
    businessLocation?.timezone_code
  )
  requestedMonth.set(
    'date',
    now.isSame(requestedMonth, 'months') ? now.date() : 1
  )
  const data = now.isAfter(requestedMonth, 'months')
    ? []
    : (
        await getDatesForBooking({
          ...rawData,
          date: requestedMonth.format(format),
          end_date: moment(requestedMonth).endOf('month').format(format),
        })
      ).map((it) => ({ date: it }))

  sendSuccess(res, { message: 'Availabilities found', data })
})
router.post('/followunfollow', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      service_provider_id: 'required',
      action_type: 'required|in:Follow,Unfollow',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      service_provider_id: t('keywords_service_provider_id'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      user_model.followunfollow(request, function (msgcode, resmsg, response) {
        common.send_response(req, res, msgcode, resmsg, response)
      })
    }
  })
})
router.post(
  '/getfollowinglist',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const errors = validate(rawData, { page: 'required' }, { required: t('required') }, {})
    if (errors) {
      return sendError(res, errors)
    }

    const data = await getServiceProvidersFollowedByUser(req.user_id, rawData.word, rawData.page)
    return sendSuccess(res, { data, code: data ? 1 : 2 })
  })

router.post('/deleteaccount', async (req, res) => {
  logger.info(`Deleting user ${req.user_id}`)
  await updateUser(req.user_id, {
    is_deleted: 1,
    status: USER_STATUS.INACTIVE,
    updatetime: datetime.create().format('Y-m-d H:M:S'),
    login_status: USER_LOGIN_STATUS.OFFLINE,
  })
  await updateUserDeviceInfoForUser(req.user_id, {
    token: '',
    device_token: '',
  })

  sendSuccess(res)
})

/*
 * API for set default location
 */
router.post('/setdefaultlocation', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      long_address: 'required',
      short_address: 'required',
      latitude: 'required',
      longitude: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      var addreparam = {
        long_address: request.long_address,
        short_address: request.short_address,
        fixed_latitude: request.latitude,
        fixed_longitude: request.longitude,
        updatetime: datetime.create().format('Y-m-d H:M:S'),
      }
      common.update_data_condition(
        'tbl_user',
        "id = '" + req.user_id + "'",
        addreparam,
        function (is_update) {
          if (is_update) {
            user_model.get_user_detail(
              req.user_id,
              function (response, errobj) {
                if (response != null) {
                  common.send_response(
                    req,
                    res,
                    '1',
                    'Default Location saved successfully',
                    response
                  )
                } else {
                  common.send_response(
                    req,
                    res,
                    '2',
                    t('restapi_user_notfound'),
                    response
                  )
                }
              }
            )
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              null
            )
          }
        }
      )
    }
  })
})

/**Api for service history*/
router.post('/service_history', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      user_model.service_history(request, function (msgcode, resmsg, response) {
        common.send_response(req, res, msgcode, resmsg, response)
      })
    }
  })
})
module.exports = router
