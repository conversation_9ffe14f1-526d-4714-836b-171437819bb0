var con = require('../../../config/database');
var GLOBALS = require('../../../config/constant');
var common = require('../../../config/common');
var customtwilio = require('./customtwilio');
const asyncLoop = require('node-async-loop');
var moment = require('moment');
const localizify = require('localizify');
const { t } = require('localizify');

const { request } = require('express');
const datetime = require('node-datetime');
const { USER_TYPE } = require('../../../src/service')
class Chat {

    //create chat room channel
    create_chatroom_channel(req) {
        return new Promise((resolve, reject) => {
            customtwilio.createUser(req.sender_id, req.sender_type).then(twilio_sender_id => {
                customtwilio.createUser(req.receiver_id, req.receiver_type).then(twilio_receiver_id => {
                    /**
                     * (CASE WHEN receiver_type = 'customer' THEN (select concat(first_name,' ',last_name) from tbl_user where id = receiver_id) WHEN receiver_type = 'service_provider' THEN (select concat(first_name,' ',last_name) from tbl_service_provider where id = receiver_id) END) as full_name
                     */
                    /**
                     * IF(receiver_type = 'customer',(select CONCAT('${GLOBALS.S3_BUCKET_ROOT + GLOBALS.USER_IMAGE}','',profile_image) as receiver_profile_image from tbl_user where id = receiver_id LIMIT 1),((select CONCAT('${GLOBALS.S3_BUCKET_ROOT + GLOBALS.SP_IMAGE}','',profile_image) as receiver_profile_image from tbl_service_provider where id = receiver_id LIMIT 1))) as receiver_profile_image
                     */
                    var selectcon = "";
                    if (req.receiver_type == 'customer') {
                        selectcon += "(select concat(first_name,' ',last_name) from tbl_user where id = " + req.receiver_id + ") as full_name,(select CONCAT('" + GLOBALS.S3_BUCKET_ROOT + GLOBALS.USER_IMAGE + "','',profile_image) as receiver_profile_image from tbl_user where id = " + req.receiver_id + " LIMIT 1) as receiver_profile_image";
                    }
                    if (req.receiver_type == 'service_provider') {
                        selectcon += "(select concat(first_name,' ',last_name) from tbl_service_provider where id = " + req.receiver_id + ") as full_name,(select CONCAT('" + GLOBALS.S3_BUCKET_ROOT + GLOBALS.SP_IMAGE + "','',profile_image) as receiver_profile_image from tbl_service_provider where id = " + req.receiver_id + " LIMIT 1) as receiver_profile_image";
                    }
                    con.query(`SELECT *,${selectcon} FROM tbl_chat_master WHERE ((sender_id = '${req.sender_id}' AND receiver_id = '${req.receiver_id}' AND sender_type = '${req.sender_type}' AND receiver_type = '${req.receiver_type}') OR (sender_id = '${req.receiver_id}' AND receiver_id = '${req.sender_id}' AND sender_type = '${req.receiver_type}' AND receiver_type = '${req.sender_type}'))`, function(error3, channeldata) {
                        if (!error3 && channeldata[0] != undefined) {
                            resolve(['1', 'Chat data found successfully', channeldata[0]]);
                        } else {
                            var chatparam = {
                                channel_sid: "",
                                friendlyName:(req.friendlyName != undefined && req.friendlyName != "") ? req.friendlyName : req.sender_id + "_" + req.receiver_id,
                                sender_id: req.sender_id,
                                receiver_id: req.receiver_id,
                                sender_type: req.sender_type,
                                receiver_type: req.receiver_type,
                                twilio_sender_id: twilio_sender_id,
                                twilio_receiver_id: twilio_receiver_id
                            }
                            con.query(`INSERT INTO tbl_chat_master SET ?`, chatparam, function(error4, results) {
                                if (!error4) {
                                    var channel_id = results.insertId;
                                    customtwilio.createChatChannel(channel_id, chatparam.friendlyName).then(channel_sid => {
                                        customtwilio.createParticipants(req.sender_id, channel_sid, req.sender_type).then(sender_participant_id => {
                                            customtwilio.createParticipants(req.receiver_id, channel_sid, req.receiver_type).then(receiver_participant_id => {
                                                var updparam = {
                                                    channel_sid: channel_sid,
                                                    sender_participant_id: sender_participant_id,
                                                    receiver_participant_id: receiver_participant_id
                                                }
                                                con.query(`UPDATE tbl_chat_master SET ? WHERE id = '${channel_id}'`, updparam, function(upderror, updresults) {
                                                    var selectcon = "";
                                                    if (req.receiver_type == 'customer') {
                                                        selectcon += "(select concat(first_name,' ',last_name) from tbl_user where id = " + req.receiver_id + ") as full_name,(select CONCAT('" + GLOBALS.S3_BUCKET_ROOT + GLOBALS.USER_IMAGE + "','',profile_image) as receiver_profile_image from tbl_user where id = " + req.receiver_id + " LIMIT 1) as receiver_profile_image";
                                                    }
                                                    if (req.receiver_type == 'service_provider') {
                                                        selectcon += "(select concat(first_name,' ',last_name) from tbl_service_provider where id = " + req.receiver_id + ") as full_name,(select CONCAT('" + GLOBALS.S3_BUCKET_ROOT + GLOBALS.SP_IMAGE + "','',profile_image) as receiver_profile_image from tbl_service_provider where id = " + req.receiver_id + " LIMIT 1) as receiver_profile_image";
                                                    }
                                                    con.query(`SELECT *,${selectcon}  FROM tbl_chat_master WHERE id = '${channel_id}'`, function(error8, channeldata) {
                                                        //console.log();
                                                        if (!error8 && channeldata[0] != undefined) {
                                                            resolve(['1', "Chat data found successfully", channeldata[0]]);
                                                        } else {
                                                            //console.log(error8);
                                                            reject();
                                                        }
                                                    });
                                                });
                                            }).catch((error7) => {
                                                //console.log('error7', error7);
                                                reject();
                                            });
                                        }).catch((error6) => {
                                            //console.log('error6', error6);
                                            reject();
                                        });
                                    }).catch((error5) => {
                                        //console.log(error5);
                                        reject();
                                    });
                                } else {
                                    //console.log(error4);
                                    reject();
                                }
                            });
                        }
                    });
                }).catch((error2) => {
                    //console.log(error2);
                    reject();
                });
            }).catch((error1) => {
                //console.log(error1);
                reject();
            });
        });
    }

    //get user chat access token
    getuserchataccesstoken(req) {
        return new Promise((resolve, reject) => {
            customtwilio.createUser(req.identity, req.sender_type).then(twilio_user_id => {
                customtwilio.chatAccessToken(req).then(accesstoken => {
                    resolve(['1', 'Chat token generated successfully', { token: accesstoken }]);
                }).catch((error) => {
                    //console.log(error);
                    reject();
                });
            }).catch((error1) => {
                //console.log(error1);
                reject();
            });
        });
    }

    //block and unblock user
    blockandunblockuser(req) {
        return new Promise((resolve, reject) => {
            if (req.is_block == '1') {
                var updparam = {
                    attributes: JSON.stringify({
                        is_block: req.is_block,
                        block_userid: req.block_userid,
                        block_username: req.block_username,
                    }),
                }
            } else {
                var updparam = {
                    attributes: JSON.stringify({}),
                }
            }
            customtwilio.updateChatChannel(req.channel_sid, updparam).then(is_update => {
                con.query(`SELECT tu.*,tud.device_token,tud.device_type FROM tbl_user as tu LEFT JOIN tbl_user_deviceinfo as tud ON tu.id = tud.user_id WHERE tu.id = '${req.user_id}' GROUP BY tu.id`, function(error, results) {
                    if (!error && results[0] != undefined) {
                        var notify = {
                            sender_type: results[0].role,
                            receiver_type: USER_TYPE.USER,
                            sender_id: req.user_id,
                            receiver_id: req.block_userid,
                            primary_id: req.channel_sid,
                            message: languages[req.lang][(req.is_block == '1') ? 'blockuser' : 'unblockuser'].replace('{username}', results[0].first_name + " " + results[0].last_name),
                            params: { 'username': results[0].first_name + " " + results[0].last_name },
                            title: GLOBALS.APP_NAME,
                            tag: (req.is_block == '1') ? 'blockuser' : 'unblockuser',
                            isaction_id:req.channel_sid,
                            is_read: '0',
                            is_admin_push: '0',
                            is_active: '1',
                            conversation_sid: req.channel_sid,
                        }
                        common.prepare_notification(notify, function(is_pushsendsuccess) {
                            if (req.is_block == '1') {
                                resolve(['1', languages[req.lang]['rest_keywords_blockuser_message'], null]);
                            } else {
                                resolve(['1', languages[req.lang]['rest_keywords_unblockuser_message'], null]);
                            }
                        });
                    }
                });
            }).catch((error) => {
                //console.log(error);
                reject();
            });
        });
    }


    /**
     * Generate token for audio call (VOIP)
     */

    generatetoken(request, callback) {
        const AccessToken = require('twilio').jwt.AccessToken;
        const VoiceGrant = AccessToken.VoiceGrant;

        // Used when generating any kind of tokens
        const twilioAccountSid = GLOBALS.TWILIO_ACCOUNT_SID;
        const twilioApiKey = GLOBALS.TWILIO_API_KEY;
        const twilioApiSecret = GLOBALS.TWILIO_API_SECRET;

        // Used specifically for creating Voice tokens
        const outgoingApplicationSid = GLOBALS.TWILIO_VOIPAPPSID;
        const identity = request.sender_type + request.identity;

        var pushsid = GLOBALS.VOIP_CUSTOMER_PUSH_SID_APN;
        if(request.device_type != undefined && request.device_type == 'A'){
            pushsid = GLOBALS.VOIP_PUSH_SID_FCM;
        } else {
            if(request.sender_type == 'service_provider'){
                pushsid = GLOBALS.VOIP_PROVIDER_PUSH_SID_APN;
            }
        }

        // Create a "grant" which enables a client to use Voice as a given user
        const voiceGrant = new VoiceGrant({
            outgoingApplicationSid: outgoingApplicationSid,
            incomingAllow: true, // Optional: add to allow incoming calls
            pushCredentialSid:pushsid,
        });

        // Create an access token which we will sign and return to the client,
        // containing the grant we just created
        const token = new AccessToken(
            twilioAccountSid,
            twilioApiKey,
            twilioApiSecret, { identity: identity }
        );
        token.addGrant(voiceGrant);

        // Serialize the token to a JWT string
        callback(token.toJwt());
    }

    /**
     * Function fo generate video call token
     */
    generatevideotoken(request, callback) {
        const AccessToken = require('twilio').jwt.AccessToken;
        const VideoGrant = AccessToken.VideoGrant;

        // Used when generating any kind of tokens

        const twilioAccountSid = GLOBALS.TWILIO_ACCOUNT_SID;
        const twilioApiKey = GLOBALS.TWILIO_API_KEY;
        const twilioApiSecret = GLOBALS.TWILIO_API_SECRET;
        const identity = request.sender_type + request.identity;

        // Create Video Grant
        const videoGrant = new VideoGrant({
            room: 'video call',
        });

        // Create an access token which we will sign and return to the client,
        // containing the grant we just created
        const token = new AccessToken(
            twilioAccountSid,
            twilioApiKey,
            twilioApiSecret, { identity: identity }
        );
        token.addGrant(videoGrant);

        // Serialize the token to a JWT string
        callback(token.toJwt());
    }

    /**
     * Function for create video call room
     */
    create_videocall_room(request, callback) {
        const accountSid = GLOBALS.TWILIO_ACCOUNT_SID;
        const authToken = GLOBALS.TWILIO_AUTH_TOKEN;
        const client = require('twilio')(accountSid, authToken);
        client.video.v1.rooms.create({
            statusCallback: GLOBALS.BASE_URL_WITHOUT_API + '/callback.php',
            type: 'peer-to-peer',
            uniqueName: request.unique_room_name
        })
        .then((room) => {
            //console.log('room');
            //console.log(room);
            callback(true, room);
        }).catch((error) => {
            //console.log('exception');
            //console.log(error);
            callback(false, error);
        });
    }

    /**
     * Get room detail and generate receiver token
     */
    get_room_details(request, callback) {
        var sql = con.query("select * from tbl_videocall_room where id = " + request.room_id + "", function(err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else if (result.length < 0) {
                callback('0', "Room detail not found", null);
            } else {
                callback('1', "Room detail found successfully", result[0]);
            }
        })
    }

    /*
    * Function for remove channel data
    */
    remove_channeldata(request,callback){
        return new Promise((resolve, reject) => {
            customtwilio.removeChannel(request.channel_sid).then((is_removed)=>{
                common.delete_data("tbl_chat_master","channel_sid = '"+request.channel_sid+"'",function(is_deleted){
                    resolve(is_deleted);
                });
            });
        });
    }
}
module.exports = new Chat();