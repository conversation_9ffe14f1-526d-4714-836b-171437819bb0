const router = require('express-promise-router')()
const { t } = require('localizify')
const {
  validate,
  decrypt,
  encrypt,
  upsertTwilioChat,
  getTwilioChatAccessToken,
  blockUnblockUser,
  getTwilioVoiceChatAccessToken,
  upsertTwilioVideoCall,
  createTwilioVideoCall,
  generateRoomName,
  removeAllTwilioConversations,
  removeTwilioConversation,
  getVideoCallDetails,
  endTwilioVideoCall,
} = require('../../../src/service')
const { logger } = require('../../../src/utils')

/**
 * @swagger
 * tags:
 *   name: chat-room
 *   description: The chat managing api
 * /v1/chat/createchatroom:
 *   post:
 *     tags: [chat-room]
 *     summary: Create a new chat room
 *     requestBody:
 *       required: true
 *       content:
 *        application/json:
 *          schema:
 *            type: object
 */
router.post(
  '/createchatroom',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = { receiver_id: 'required', sender_type: 'required', receiver_type: 'required' }
    const messages = { 'required': t('required') }
    const keywords = { 'receiver_id': 'Receiver Id', 'sender_type': 'Sender Type', 'receiver_type': 'Receiver Type' }
    const errors = validate(rawData, rules, messages, keywords)

    if (errors) {
      sendError(res, errors)
      return
    }

    rawData.sender_id = req.user_id ?? 0
    const data = await upsertTwilioChat(rawData)
    sendSuccess(res, { data })
  })

router.post(
  '/getaccesstoken',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = { identity: 'required', sender_type: 'required', device_type: 'required|in:A,I', device_token: 'required' }
    const messages = { 'required': t('required'), 'in': t('in') }
    const keywords = { 'identity': 'Identity', 'device_type': t('keyword_devicetype'), 'device_token': t('keyword_devicetoken') }
    const errors = validate(rawData, rules, messages, keywords)

    if (errors) {
      sendError(res, errors)
      return
    }

    rawData.user_id = req.user_id ?? 0
    rawData.identity = rawData.identity ?? 0
    const token = await getTwilioChatAccessToken(rawData)
    sendSuccess(res, { data: { token } })
  })

router.post(
  '/blockunblockuser',
  async (req, res) => {
    const rawBody = decrypt(req.body)
    const rules = { channel_sid: 'required', is_block: 'required|in:0,1', block_userid: 'required', block_username: 'required' }
    const messages = { 'required': t('required'), 'in': t('in') }
    const keywords = { 'channel_sid': t('rest_keywords_channelsid'), 'is_block': t('rest_keywords_isblock'), 'block_userid': t('rest_keywords_blockuserid') }
    const errors = validate(rawBody, rules, messages, keywords)

    if (errors) {
      sendError(res, errors)
      return
    }

    rawBody.user_id = req.user_id ?? 0
    await blockUnblockUser(rawBody)
    const data = { message: `Successfully ${ rawBody.is_block === '1' ? 'blocked' : 'unblocked' } user.` }
    sendSuccess(res, { data })
  })

router.post(
  '/generate_audio_token',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = { identity: 'required', sender_type: 'required' }
    const messages = { 'required': t('required') }
    const keywords = { 'identity': 'Identity' }
    const errors = validate(rawData, rules, messages, keywords)

    if (errors) {
      sendError(res, errors)
      return
    }

    const data = getTwilioVoiceChatAccessToken(rawData)
    sendSuccess(res, { data })
  })

router.post(
  '/generate_video_token',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = { identity: 'required', sender_type: 'required', receiver_id: 'required', receiver_type: 'required' }
    const messages = { 'required': t('required') }
    const keywords = { 'identity': 'Identity' }
    const errors = validate(rawData, rules, messages, keywords)

    if (errors) {
      sendError(res, errors)
      return
    }

    const data = await upsertTwilioVideoCall(rawData)
    sendSuccess(res, { data })
  })

router.post(
  '/create_videocall_room',
  async (req, res) => {
    const data = await createTwilioVideoCall(generateRoomName())
    sendSuccess(res, { data })
  })

router.post(
  '/get_room_details',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = { room_id: 'required' }
    const messages = { 'required': t('required') }
    const keywords = { 'identity': 'Identity' }
    const errors = validate(rawData, rules, messages, keywords)

    if (errors) {
      sendError(res, errors)
      return
    }

    const data = await getVideoCallDetails(rawData)
    sendSuccess(res, { data })
  })

router.post(
  '/end_videocall',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = { room_id: 'required', receiver_id: 'required', receiver_type: 'required' }
    const messages = { 'required': t('required') }
    const keywords = { 'identity': 'Identity' }
    const errors = validate(rawData, rules, messages, keywords)

    if (errors) {
      sendError(res, errors)
      return
    }

    rawData.user_id = req.user_id ?? 0
    logger.info(`Request data: ${JSON.stringify(rawData)}`)
    await endTwilioVideoCall(rawData)
    sendSuccess(res)
  })

router.post(
  '/removealldata',
  async (req, res) => {
    await removeAllTwilioConversations()
    sendSuccess(res)
  })

router.post(
  '/removechanneldata',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = { channel_sid: 'required' }
    const messages = { 'required': t('required') }
    const keywords = { 'channel_sid': t('rest_keywords_channelsid') }
    const errors = validate(rawData, rules, messages, keywords)

    if (errors) {
      sendError(res, errors)
      return
    }

    logger.info(`Request data: ${JSON.stringify(rawData)}`)
    await removeTwilioConversation(rawData.channel_sid)
    sendSuccess(res)
  })

function sendError(res, errors = {}) {
  res.status(400).json(encrypt({ code: 0, ...errors }))
}

function sendSuccess(res, data = {}) {
  res.status(200).json(encrypt({ code: 1, ...data }))
}

module.exports = router
