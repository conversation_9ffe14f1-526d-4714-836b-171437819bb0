var GLOBALS = require('../../../config/constant');
var con = require('../../../config/database');
var twilio = require('twilio');
var client = new twilio(GLOBALS.TWILIO_ACCOUNT_SID, GLOBALS.TWILIO_AUTH_TOKEN);
const AccessToken = require('twilio').jwt.AccessToken;
const ChatGrant = AccessToken.ChatGrant;

class customtwilio {

    /*
     ** Function to create users of chat
     ** @param {identity of user} identity 
     ** @param {Name of user} friendlyname 
     ** @param {Function} callback 
     ** 01-03-2022
     */
    createUser(user_identity, user_type) {
        return new Promise((resolve, reject) => {
            if (user_type == 'customer') {
                var table_name = "tbl_user";
                var image_path = GLOBALS.S3_BUCKET_ROOT + GLOBALS.USER_IMAGE
            }
            if (user_type == 'service_provider') {
                var table_name = "tbl_service_provider";
                var image_path = GLOBALS.S3_BUCKET_ROOT + GLOBALS.SP_IMAGE
            }
            con.query("SELECT * FROM " + table_name + " WHERE id = " + user_identity + "", function(error, userdata) {
                if (!error && userdata[0] != undefined) {
                    var user_attributes = {
                        id: userdata[0].id,
                        first_name: userdata[0].first_name,
                        last_name: userdata[0].last_name,
                        country_code: userdata[0].country_code,
                        phone: userdata[0].phone,
                        email: userdata[0].email,
                        user_type: user_type,
                        profile_image: image_path + userdata[0].profile_image,
                    };
                    client.conversations.v1.users(user_type + userdata[0].id).fetch().then(user => {
                        client.conversations.v1.users(user.sid).update({ friendlyName: userdata[0].first_name + " " + userdata[0].last_name, attributes: JSON.stringify(user_attributes) }).then(user => {
                            con.query("UPDATE " + table_name + " SET twilio_user_sid = '" + user.sid + "' WHERE id = " + userdata[0].id + "", function(upderror, updresults) {
                                resolve(user.sid);
                            });
                        }).catch(error1 => {
                            con.query("UPDATE " + table_name + " SET twilio_user_sid = '" + user.sid + "' WHERE id = " + userdata[0].id + "", function(upderror, updresults) {
                                console.log("error1", error1);
                                resolve(user.sid);
                            });
                        });
                    }).catch(error2 => {
                        console.log("error2", error2);
                        client.conversations.v1.users.create({ identity: user_type + userdata[0].id, friendlyName: userdata[0].first_name + " " + userdata[0].last_name, attributes: JSON.stringify(user_attributes) }).then(user => {
                            con.query("UPDATE " + table_name + " SET twilio_user_sid = '" + user.sid + "' WHERE id = " + userdata[0].id + "", function(upderror, updresults) {
                                resolve(user.sid);
                            });
                        }).catch(error3 => {
                            console.log("error3", error3);
                            reject();
                        });
                    });
                } else {
                    console.log("error", error);
                    reject();
                }
            });
        });
    }

    /*
    ** Function for remove all users
    */
    removeAlluser(){
        return new Promise((resolve, reject) => {
            console.log("fetch users");
            client.conversations.v1.users.list().then(users => {
                console.log("start removing user");
                users.forEach((user, key) => {
                    console.log(user.identity);
                    client.conversations.v1.users(user.sid).remove();
                    if (key == (users.length - 1)) {
                        console.log("stop removing user with send response");
                        resolve(true);
                        return;
                    }
                })
                console.log("stop removing user");
            }).catch((error) => {
                console.log("error", error);
                resolve(false);
            });
        });
    }

    /*
     ** Function to create users of chat
     ** @param {identity of user} user_identity  
     ** @param {Function} callback 
     ** 01-03-2022
     */
    removeUser(user_identity) {
        return new Promise((resolve, reject) => {
            client.conversations.v1.users(user_identity).remove();
            resolve(true);
        });
    }

    /*
     ** Function to create Participants of conversations
     ** @param {identity of user} user_identity 
     ** @param {sid of channel} channel_sid 
     ** @param {Function} callback 
     ** 01-03-2022
     */
    createParticipants(user_identity, channel_sid, user_type) {
        return new Promise((resolve, reject) => {
            if (user_type == 'customer') {
                var table_name = "tbl_user";
                var image_path = GLOBALS.S3_BUCKET_ROOT + GLOBALS.USER_IMAGE
            }
            if (user_type == 'service_provider') {
                var table_name = "tbl_service_provider";
                var image_path = GLOBALS.S3_BUCKET_ROOT + GLOBALS.SP_IMAGE
            }
            con.query("SELECT * FROM " + table_name + " WHERE id = " + user_identity + "", function(error, userdata) {

                if (!error && userdata[0] != undefined) {
                    var user_attributes = {
                        id: userdata[0].id,
                        first_name: userdata[0].first_name,
                        last_name: userdata[0].last_name,
                        country_code: userdata[0].country_code,
                        phone: userdata[0].phone,
                        email: userdata[0].email,
                        user_type: user_type,
                        profile_image: image_path + userdata[0].profile_image
                    };
                    client.conversations.v1.services(GLOBALS.TWILIO_SERVICE_SID).conversations(channel_sid).participants(user_type + userdata[0].id).fetch().then(participant => {
                        client.conversations.v1.services(GLOBALS.TWILIO_SERVICE_SID).conversations(channel_sid).participants(participant.sid).update({ friendlyName: userdata[0].first_name + " " + userdata[0].last_name, attributes: JSON.stringify(user_attributes) }).then(participant => {
                            resolve(participant.sid);
                        }).catch(error1 => {
                            console.log("error1", error1);
                            resolve(participant.sid);
                        });
                    }).catch(error2 => {
                        console.log("error2", error2);
                        client.conversations.v1.services(GLOBALS.TWILIO_SERVICE_SID).conversations(channel_sid).participants.create({ identity: user_type + userdata[0].id, friendlyName: userdata[0].first_name + " " + userdata[0].last_name, attributes: JSON.stringify(user_attributes) }).then(participant => {
                            resolve(participant.sid);
                        }).catch(error3 => {
                            console.log("error3", error3);
                            reject();
                        });
                    });
                } else {
                    console.log("error", error);
                    reject();
                }
            });
        });
    }

    /*
     ** Function to remove Participants of conversations
     ** @param {channel SID} channel_sid 
     ** @param {user identity} identity 
     ** @param {Function} callback 
     ** 01-03-2022
     */
    removeParticipants(channel_sid, participant_identity) {
        return new Promise((resolve, reject) => {
            client.conversations.v1.services(GLOBALS.TWILIO_SERVICE_SID).conversations(channel_sid).participants(participant_identity).remove();
            resolve(true);
        });
    }

    /*
     ** Get conversations all Participants list
     ** @param {channel SID} channel_sid 
     ** @param {Function} callback 
     ** 01-03-2022
     */
    getChannelAllParticipants(channel_sid) {
        return new Promise((resolve, reject) => {
            client.conversations.v1.services(GLOBALS.TWILIO_SERVICE_SID).conversations(channel_sid).participants.list().then(participants => {
                resolve(participants);
            }).catch(error => {
                console.log("error", error);
                resolve(null);
            });
        });
    }

    /*
     ** Function to Create channel for chat between two users
     ** @param {uniqueName of channel} uniqueName 
     ** @param {Name of channel} friendlyname 
     ** @param {Function} callback 
     ** 01-03-2022
     */
    createChatChannel(unique_name, friendly_name) {
        return new Promise((resolve, reject) => {
            client.conversations.conversations(unique_name).fetch().then(conversation => {
                client.conversations.conversations(conversation.sid).update({ friendlyName: friendly_name }).then(conversation => {
                    resolve(conversation.sid);
                }).catch(error => {
                    console.log("error", error);
                    resolve(conversation.sid);
                });
            }).catch(error1 => {
                console.log("error1", error1);
                client.conversations.conversations.create({ uniqueName: unique_name, friendlyName: friendly_name }).then(conversation => {
                    resolve(conversation.sid);
                }).catch(error2 => {
                    console.log("error2", error2);
                    reject();
                });
            });
        });
    }

    /*
     ** Function to Update channel for chat between two users
     ** @param {Sid of channel} channel_sid 
     ** @param {update param} updparam 
     ** @param {Function} callback 
     ** 01-03-2022
     */
    updateChatChannel(channel_sid, updparam) {
        return new Promise((resolve, reject) => {
            client.conversations.v1.conversations(channel_sid).update(updparam).then(conversation => {
                resolve(true);
            });
        });
    }

    /*
     ** Function to remove channel from twilio
     ** @param {uniqueName of channel} uniqueName
     ** @param {Function} callback 
     ** 01-03-2022
     */
    removeChannel(channel_sid) {
        return new Promise((resolve, reject) => {
            client.conversations.v1.conversations(channel_sid).remove();
            resolve(true);
        });
    }

    /*
     ** Generate chat access token 
     ** @param {request of userdata} request
     ** @param {Function} callback 
     ** 01-03-2022
     */
    chatAccessToken(request) {
        return new Promise((resolve, reject) => {
            client.conversations.v1.services(GLOBALS.TWILIO_SERVICE_SID).configuration().update({reachabilityEnabled: true}).then(configuration => {
                client.conversations.v1.services(GLOBALS.TWILIO_SERVICE_SID).configuration.notifications().update({ 'NewMessage.enabled': true, 'NewMessage.sound': 'default', 'NewMessage.template': 'You have a new message from ${PARTICIPANT}: ${MESSAGE}', 'addedToConversation.enabled': true, 'addedToConversation.sound': 'default', 'addedToConversation.template': '${MESSAGE}' }).then(notification => {
                    var device_token = (request.device_token == '' || request.device_token == '0' || request.device_token == undefined) ? "" : request.device_token;
                    if (request.device_type == 'I') {
                        var push_id = (request.sender_type == 'customer') ? GLOBALS.CUSTOMER_PUSH_SID_APN : GLOBALS.PROVIDER_PUSH_SID_APN;
                    } else {
                        var push_id = GLOBALS.PUSH_SID_FCM;;
                    }
                    const grantobject = {
                        serviceSid: GLOBALS.TWILIO_SERVICE_SID,
                        endpointId: 'Blookd' + ':' + request.sender_type + request.identity + ':' + device_token,
                        deploymentRoleSid: '',
                        pushCredentialSid: push_id,
                    };
                    const chatGrant = new ChatGrant(grantobject);
                    // Create an access token which we will sign and return to the client, containing the grant we just created
                    const token = new AccessToken(GLOBALS.TWILIO_ACCOUNT_SID, GLOBALS.TWILIO_API_KEY, GLOBALS.TWILIO_API_SECRET,{identity: request.sender_type + request.identity});
                    token.addGrant(chatGrant);
                    token.identity = request.sender_type + request.identity;
                    console.log('==>>>>>>>>>>>>>>>>sender and identity', request.sender_type + request.identity);
                    console.log('==>>>>>>>>>>>>>>>>', grantobject);
                    resolve(token.toJwt());
                }).catch((error) => {
                    console.log("error", error);
                    reject();
                });
            });
        });
    }


    /*
     ** Get all User channels 
     ** @param {user identity} identity 
     ** @param {Function} callback 
     ** 01-03-2022
     */
    getAllUserChannel(user_identity) {
        return new Promise((resolve, reject) => {
            client.conversations.v1.users(user_identity).userConversations.list({ limit: 20 }).then(userConversations => {
                resolve(userConversations);
            }).catch((error) => {
                console.log("error", error);
                resolve(null);
            });
        });
    }

    /*
     ** Get all channels 
     ** @param {Function} callback 
     ** 01-03-2022
     */
    getAllChannel() {
        return new Promise((resolve, reject) => {
            client.conversations.v1.conversations.list().then(conversations => {
                resolve(conversations);
            }).catch((error) => {
                console.log("error", error);
                resolve(null);
            });
        });
    }

    /*
     ** Remove all channels 
     ** @param {Function} callback 
     ** 01-03-2022
     */
    removeAllChannel() {
        return new Promise((resolve, reject) => {
            client.conversations.v1.conversations.list().then(conversations => {
                if(conversations.length > 0){
                    conversations.forEach((channel, key) => {
                        client.conversations.v1.conversations(channel.sid).remove();
                        if (key == (conversations.length - 1)) {
                            resolve(true);
                            return;
                        }
                    });
                } else {
                    resolve(true);
                    return;
                }
            }).catch((error) => {
                console.log("error", error);
                resolve(false);
            });
        });
    }

    /*
     ** Remove user bindings
     ** @param {user identity} identity 
     ** @param {Function} callback 
     ** 01-03-2022
     */
    removeUserBindings(user_identity) {
        return new Promise((resolve, reject) => {
            client.conversations.v1.services(GLOBALS.TWILIO_SERVICE_SID).bindings.list().then(bindings => {
                bindings.forEach((binding, key) => {
                    if (binding.identity == user_identity) {
                        client.conversations.v1.services(GLOBALS.TWILIO_SERVICE_SID).bindings(binding.sid).remove();
                    }
                    if (key == (bindings.length - 1)) {
                        resolve(true);
                        return;
                    }
                });
            }).catch((error) => {
                console.log("error", error);
                resolve(false);
            });
        });
    }
};

module.exports = new customtwilio();