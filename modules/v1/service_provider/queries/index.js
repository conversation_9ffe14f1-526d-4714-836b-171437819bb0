const global = require('../../../../config/constant');

const withCustomerInfo = `
    WITH CustomerInfo AS (
        SELECT
            id,
            CASE
                WHEN customer_id = '0' THEN (SELECT CONCAT(first_name, ' ', last_name) FROM tbl_user WHERE id = ab.user_id ORDER BY ID DESC LIMIT 1)
                ELSE (SELECT customer_name FROM tbl_service_provider_client WHERE id = ab.customer_id ORDER BY ID DESC LIMIT 1)
            END AS customer_name,
            CASE
                WHEN customer_id = '0' THEN (SELECT phone FROM tbl_user WHERE id = ab.user_id ORDER BY ID DESC LIMIT 1)
                ELSE (SELECT phone FROM tbl_service_provider_client WHERE id = ab.customer_id ORDER BY ID DESC LIMIT 1)
            END AS customer_phone,
            CASE
                WHEN customer_id = '0' THEN (SELECT email FROM tbl_user WHERE id = ab.user_id ORDER BY ID DESC LIMIT 1)
                ELSE (SELECT email FROM tbl_service_provider_client WHERE id = ab.customer_id ORDER BY ID DESC LIMIT 1)
            END AS customer_email,
            CONCAT(
              '${ global.S3_BUCKET_ROOT }',
              '${ global.USER_IMAGE }',
              IF(
                customer_id = '0',
                (SELECT profile_image FROM tbl_user WHERE id = ab.user_id ORDER BY ID DESC LIMIT 1),
                (SELECT profile_image FROM tbl_service_provider_client WHERE id = ab.customer_id ORDER BY ID DESC LIMIT 1)
              )
            ) AS customer_profile_image
        FROM tbl_appointment_booking ab
    )`;

export const selectAppointmentBooking = (
    condition="",
    having="",
    order_by="",
    booking_statuses=[]
) => {
    const booking_status_array = booking_statuses.map((val) => `'${val}'`).join(', ')

    return `
        ${withCustomerInfo}
        SELECT
            ab.*,
            COALESCE(ci.customer_name, ab.customer_name) AS customer_name,
            COALESCE(ci.customer_phone, '') AS customer_phone,
            COALESCE(ci.customer_email, '') AS customer_email,
            ci.customer_profile_image,
            DATE_FORMAT(ab.date, '%Y-%m-%d') AS date, ab.id AS appointment_id,
            b.name AS business_address,

            IF(booking_type = 'walkin', DATE_FORMAT(ab.insertdate, '%Y-%m-%d %H:%i'),
                CONCAT(ab.date, ' ', slot_time)) AS booking_date,

            DATE_FORMAT(ab.end_datetime, '%Y-%m-%d %H:%i:%s') AS end_datetime,
            ab.id AS appointment_id, s.first_name, s.last_name,
            (SELECT GROUP_CONCAT(s.service_name SEPARATOR ', ')
                FROM tbl_appointment_booking_detail abd
                LEFT JOIN tbl_service s ON s.id = abd.service_id
                WHERE abd.appointment_id = ab.id AND abd.service_id != '0'
                GROUP BY abd.appointment_id
                ORDER BY abd.id DESC LIMIT 1) AS service_name,

            IF((SELECT id FROM tbl_image WHERE type_id = ab.id AND type = 'complete_appointment'
                AND is_deleted = '0' GROUP BY type_id) IS NULL, '0', '1')
                AS is_uploaded_completeservice_image

        FROM tbl_appointment_booking ab
        LEFT JOIN tbl_business_location b ON b.id = ab.business_location_id
        LEFT JOIN tbl_service_provider s ON s.id = ab.service_provider_id
        INNER JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id
        LEFT JOIN CustomerInfo ci ON ci.id = ab.id
        WHERE ${condition} AND abd.type = 'Service' AND booking_status IN (${booking_status_array})
        GROUP BY ab.id ${having} ${order_by}`;
}


