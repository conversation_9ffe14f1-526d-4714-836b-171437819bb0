var report = {
    /**
     * Function for get report screen data
    */
    oldestreports:function(request,callback){
        var sql = con.query("select *,IFNULL((SELECT 1 FROM tbl_service_provider_graph g WHERE g.graph_id = mg.id AND g.service_provider_id = " + service_provider_id + "),0) AS is_checked from tbl_master_graph mg where mg.status = 'Active' AND mg.is_deleted = '0' having is_checked = 1", function(error, graph_result) {
            if(!error && graph_result.length > 0){
                asyncloop(graph_result,function(item1,next1){
                    if (item1) {
                        if (item1.id == 1) {
                            if (request.period == 'Daily') {
                                var query = con.query("select DAYNAME('" + request.start_date + "') as day_name,count(abd.service_id) as main_count from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where ab.service_provider_id = " + service_provider_id + " AND abd.type ='Service' AND ab.date = '" + request.start_date + "'", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined) {
                                            var query = con.query("select DAYNAME('" + request.compare_start_date + "') as day_name,count(abd.service_id) as compare_count from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where ab.service_provider_id = " + service_provider_id + " AND abd.type ='Service' AND ab.date = '" + request.compare_start_date + "'", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['Top_of_services'] = compared_value;
                                                        next1()
                                                    });
                                                } else {
                                                    result1[0].compare_count = 0
                                                    main_graph_obj['Top_of_services'] = result1;
                                                    next1()
                                                }
                                            });
                                        } else {
                                            main_graph_obj['Top_of_services'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['Top_of_services'] = [];
                                        next1()
                                    }
                                });
                            } else {
                                var query = con.query("SELECT COUNT(abd.service_id) AS main_count, DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Service' GROUP BY DAYNAME(ab.date)", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT COUNT(abd.service_id) AS compare_count, DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Service' GROUP BY DAYNAME(ab.date)", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['Top_of_services'] = compared_value;
                                                        next1()
                                                    });
                                                } else {
                                                    main_graph_obj['Top_of_services'] = result1;
                                                    next1()
                                                }
                                            });
                                        } else {
                                            main_graph_obj['Top_of_services'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['Top_of_services'] = [];
                                        next1()
                                    }
                                });
                            }
                        } else if (item1.id == 2) {
                            if (request.period == 'Daily') {
                                var query = con.query("select DAYNAME('" + request.start_date + "') as day_name,sum(abd.price * abd.quantity) as main_count from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where ab.service_provider_id = " + service_provider_id + " AND ab.date = '" + request.start_date + "' AND abd.type ='Product'", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined) {
                                            var query = con.query("select DAYNAME('" + request.compare_start_date + "') as day_name,sum(abd.price * abd.quantity) as compare_count from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where ab.service_provider_id = " + service_provider_id + " AND ab.date = '" + request.compare_start_date + "' AND abd.type ='Product'", function(err2, result2) {
                                                if (!err2 && result2[0].compare_count != undefined) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['total_retail_earnings'] = compared_value;
                                                        next1()
                                                    });
                                                } else {
                                                    result1[0].compare_count = 0
                                                    main_graph_obj['total_retail_earnings'] = result1;
                                                    next1()
                                                }
                                            });
                                        } else {
                                            main_graph_obj['total_retail_earnings'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['total_retail_earnings'] = [];
                                        next1()
                                    }
                                });
                            } else {
                                var query = con.query("SELECT sum(abd.price * abd.quantity) AS main_count, DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Product' GROUP BY DAYNAME(ab.date)", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT sum(abd.price * abd.quantity) AS compare_count, DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Product' GROUP BY DAYNAME(ab.date)", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['total_retail_earnings'] = compared_value;
                                                        next1()
                                                    });
                                                } else {
                                                    main_graph_obj['total_retail_earnings'] = result1;
                                                    next1()
                                                }
                                            });
                                        } else {
                                            main_graph_obj['total_retail_earnings'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['total_retail_earnings'] = [];
                                        next1()
                                    }
                                });
                            }
                        } else if (item1.id == 3) {
                            if (request.period == 'Daily') {
                                var query = con.query("select DAYNAME('" + request.start_date + "') as day_name,sum(abd.price) as main_count from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where ab.service_provider_id = " + service_provider_id + " AND ab.date = '" + request.start_date + "' AND abd.type ='Service'", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined) {
                                            var query = con.query("select DAYNAME('" + request.compare_start_date + "') as day_name,sum(abd.price) as compare_count from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where ab.service_provider_id = " + service_provider_id + " AND ab.date = '" + request.compare_start_date + "' AND abd.type ='Service'", function(err2, result2) {
                                                if (!err2 && result2[0].compare_count != undefined) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['total_service_earnings'] = compared_value;
                                                        next1()
                                                    });
                                                } else {
                                                    result1[0].compare_count = 0
                                                    main_graph_obj['total_service_earnings'] = result1;
                                                    next1()
                                                }
                                            });
                                        } else {
                                            main_graph_obj['total_service_earnings'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['total_service_earnings'] = [];
                                        next1()
                                    }
                                });
                            } else {
                                var query = con.query("SELECT sum(abd.price) AS main_count, DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Service' GROUP BY DAYNAME(ab.date)", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT sum(abd.price) AS compare_count, DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Service' GROUP BY DAYNAME(ab.date)", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['total_service_earnings'] = compared_value;
                                                        next1()
                                                    });
                                                } else {
                                                    main_graph_obj['total_service_earnings'] = result1;
                                                    next1()
                                                }
                                            });
                                        } else {
                                            main_graph_obj['total_service_earnings'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['total_service_earnings'] = [];
                                        next1()
                                    }
                                });
                            }
                        } else if (item1.id == 4) {
                            next1()
                        } else if (item1.id == 5) {
                            next1()
                        } else if (item1.id == 6) {
                            next1()
                        } else if (item1.id == 7) {
                            if (request.period == 'Daily') {
                                var query = con.query("SELECT round((SUM(CASE WHEN booking_type = 'online' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) AS online_booking_percentage,round((SUM(CASE WHEN booking_type = 'walkin' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) as offline_booking_percentage,DAYNAME('" + request.start_date + "') as day_name FROM tbl_appointment_booking ab WHERE ab.date = '" + request.start_date + "'  AND ab.service_provider_id =" + service_provider_id + "", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined) {
                                            var query = con.query("SELECT round((SUM(CASE WHEN booking_type = 'online' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) AS online_booking_compare_percentage,round((SUM(CASE WHEN booking_type = 'walkin' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) as offline_booking_compare_percentage,DAYNAME('" + request.compare_start_date + "') as day_name FROM tbl_appointment_booking ab WHERE ab.date = '" + request.compare_start_date + "' AND ab.service_provider_id =" + service_provider_id + "", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_multiple_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['online_bookings'] = compared_value;
                                                        next1()
                                                    });
                                                } else {
                                                    result1[0].online_booking_compare_percentage = 0;
                                                    result1[0].offline_booking_compare_percentage = 0;
                                                    main_graph_obj['online_bookings'] = result1;
                                                    next1()
                                                }
                                            });
                                        } else {
                                            main_graph_obj['online_bookings'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['online_bookings'] = [];
                                        next1()
                                    }
                                });
                            } else {
                                var query = con.query("SELECT round((SUM(CASE WHEN booking_type = 'online' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) AS online_booking_percentage,round((SUM(CASE WHEN booking_type = 'walkin' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) as offline_booking_percentage,DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking ab WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "' AND ab.service_provider_id =" + service_provider_id + "  GROUP BY DAYNAME(ab.date)", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT round((SUM(CASE WHEN booking_type = 'online' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) AS online_booking_compare_percentage,round((SUM(CASE WHEN booking_type = 'walkin' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) as offline_booking_compare_percentage,DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking ab WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "' AND ab.service_provider_id =" + service_provider_id + " GROUP BY DAYNAME(ab.date)", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_multiple_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['online_bookings'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].online_booking_compare_percentage = 0;
                                                    result1[0].offline_booking_compare_percentage = 0;
                                                    main_graph_obj['online_bookings'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['online_bookings'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['online_bookings'] = [];
                                        next1()
                                    }
                                })
                            }
                        } else if (item1.id == 8) {
                            if (request.period == 'Daily') {
                                var query = con.query("SELECT count(id) as main_count,DAYNAME('" + request.start_date + "') as day_name FROM tbl_appointment_booking ab WHERE ab.date = '" + request.start_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND booking_status ='No Show'", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined) {
                                            var query = con.query("SELECT count(id) as compare_count,DAYNAME('" + request.compare_start_date + "') as day_name FROM tbl_appointment_booking ab WHERE ab.date = '" + request.compare_start_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND booking_status ='No Show'", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['no_shows'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['no_shows'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['no_shows'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['no_shows'] = [];
                                        next1()
                                    }
                                })
                            } else {
                                var query = con.query("SELECT count(id) as main_count,DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking ab WHERE  ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND booking_status ='No Show' GROUP BY DAYNAME(ab.date)", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT count(id) as compare_count,DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking ab WHERE  ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND booking_status ='No Show' GROUP BY DAYNAME(ab.date)", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['no_shows'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['no_shows'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['no_shows'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['no_shows'] = [];
                                        next1()
                                    }
                                })
                            }
                        } else if (item1.id == 9) {
                            if (request.period == 'Daily') {
                                var query = con.query("SELECT count(id) as main_count,DAYNAME('" + request.start_date + "') as day_name FROM tbl_appointment_booking ab WHERE ab.date = '" + request.start_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND booking_status ='Cancelled'", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined) {
                                            var query = con.query("SELECT count(id) as compare_count,DAYNAME('" + request.compare_start_date + "') as day_name FROM tbl_appointment_booking ab WHERE ab.date = '" + request.compare_start_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND booking_status ='Cancelled'", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['cancellations'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['cancellations'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['cancellations'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['cancellations'] = [];
                                        next1()
                                    }
                                })
                            } else {
                                var query = con.query("SELECT count(id) as main_count,DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking ab WHERE  ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND booking_status ='Cancelled' GROUP BY DAYNAME(ab.date)", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT count(id) as compare_count,DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking ab WHERE  ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND booking_status ='Cancelled' GROUP BY DAYNAME(ab.date)", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['cancellations'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['cancellations'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['cancellations'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['cancellations'] = [];
                                        next1()
                                    }
                                })
                            }
                        } else if (item1.id == 10) {
                            if (request.period == 'Daily') {
                                var query = con.query("SELECT COUNT(DISTINCT customer_id) as main_count,DAYNAME('" + request.start_date + "') as day_name FROM tbl_appointment_booking ab WHERE ab.date = '" + request.start_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND customer_id != '0'", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined) {
                                            var query = con.query("SELECT COUNT(DISTINCT customer_id) as compare_count,DAYNAME('" + request.compare_start_date + "') as day_name FROM tbl_appointment_booking ab WHERE ab.date = '" + request.start_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND customer_id != '0'", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['all_clients'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['all_clients'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['all_clients'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['all_clients'] = [];
                                        next1()
                                    }
                                })
                            } else {
                                var query = con.query("SELECT COUNT(DISTINCT customer_id) as main_count,DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking ab WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND customer_id != '0' GROUP BY DAYNAME(ab.date)", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT COUNT(DISTINCT customer_id) as compare_count,DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking ab WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "'  AND ab.service_provider_id =" + service_provider_id + " AND customer_id != '0' GROUP BY DAYNAME(ab.date)", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['all_clients'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['all_clients'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['all_clients'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['all_clients'] = [];
                                        next1()
                                    }
                                })
                            }
                        } else if (item1.id == 11) {
                            if (request.period == 'Daily') {
                                var query = con.query("select DAYNAME('" + request.start_date + "') as day_name,sum(abd.quantity) as main_count from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where ab.service_provider_id = " + service_provider_id + " AND ab.date = '" + request.start_date + "' AND abd.type ='Product'", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined) {
                                            var query = con.query("select DAYNAME('" + request.compare_start_date + "') as day_name,sum(abd.quantity) as compare_count from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where ab.service_provider_id = " + service_provider_id + " AND ab.date = '" + request.compare_start_date + "' AND abd.type ='Product'", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['all_retail_products'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['all_retail_products'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['all_retail_products'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['all_retail_products'] = [];
                                        next1()
                                    }
                                })
                            } else if (request.period == 'Weekly') {
                                var query = con.query("SELECT sum(abd.quantity) AS main_count,DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Product' GROUP BY DAYNAME(ab.date) ORDER BY FIELD(DAYNAME(ab.date), 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT sum(abd.quantity) AS compare_count, DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Product' GROUP BY DAYNAME(ab.date) ORDER BY FIELD(DAYNAME(ab.date), 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['all_retail_products'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['all_retail_products'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['all_retail_products'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['all_retail_products'] = [];
                                        next1()
                                    }
                                })
                            } else if (request.period == 'Monthly') {
                                var query = con.query("SELECT sum(abd.quantity) AS main_count FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Product' GROUP BY  ab.date ORDER BY ab.date ASC", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT sum(abd.quantity) AS compare_count FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Product' GROUP BY ab.date ORDER BY ab.date ASC", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['all_retail_products'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['all_retail_products'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['all_retail_products'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['all_retail_products'] = [];
                                        next1()
                                    }
                                })
                            } else if (request.period == "Custom") {
                                var query = con.query("SELECT sum(abd.quantity) AS main_count,DATE_FORMAT(ab.date, '%M') AS month_name,DATE_FORMAT(ab.date, '%m') AS month_number FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Product' GROUP BY DATE_FORMAT(ab.date, '%M'), DATE_FORMAT(ab.date, '%m') ORDER BY ab.date ASC", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT sum(abd.quantity) AS compare_count,DATE_FORMAT(ab.date, '%M') AS month_name,DATE_FORMAT(ab.date, '%m') AS month_number FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Product' GROUP BY DATE_FORMAT(ab.date, '%M'), DATE_FORMAT(ab.date, '%m') ORDER BY ab.date ASC", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['all_retail_products'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['all_retail_products'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['all_retail_products'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['all_retail_products'] = [];
                                        next1()
                                    }
                                })
                            } else {
                                main_graph_obj['all_retail_products'] = [];
                                next1()
                            }
                        } else if (item1.id == 12) {
                            if (request.period == 'Daily') {
                                var query = con.query("select DAYNAME('" + request.start_date + "') as day_name,count(abd.service_id) as main_count from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where ab.service_provider_id = " + service_provider_id + " AND ab.date = '" + request.start_date + "' AND abd.type ='Service'", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined) {
                                            var query = con.query("select DAYNAME('" + request.compare_start_date + "') as day_name,count(abd.service_id) as compare_count from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where ab.service_provider_id = " + service_provider_id + " AND ab.date = '" + request.compare_start_date + "' AND abd.type ='Service'", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['all_services'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['all_services'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['all_services'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['all_services'] = [];
                                        next1()
                                    }
                                })
                            } else if (request.period == 'Weekly') {
                                var query = con.query("SELECT count(abd.service_id) AS main_count,DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Service' GROUP BY DAYNAME(ab.date) ORDER BY FIELD(DAYNAME(ab.date), 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT count(abd.service_id) AS compare_count, DAYNAME(ab.date) AS day_name FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Service' GROUP BY DAYNAME(ab.date) ORDER BY FIELD(DAYNAME(ab.date), 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['all_services'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['all_services'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['all_services'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['all_services'] = [];
                                        next1()
                                    }
                                })
                            } else if (request.period == 'Monthly') {
                                var query = con.query("SELECT count(abd.service_id) AS main_count FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Service' GROUP BY  ab.date ORDER BY ab.date ASC", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT count(abd.service_id) AS compare_count FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Service' GROUP BY ab.date ORDER BY ab.date ASC", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['all_services'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['all_services'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['all_services'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['all_services'] = [];
                                        next1()
                                    }
                                })
                            } else if (request.period == "Custom") {
                                var query = con.query("SELECT count(abd.service_id) AS main_count,DATE_FORMAT(ab.date, '%M') AS month_name,DATE_FORMAT(ab.date, '%m') AS month_number FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.start_date + "' AND '" + request.end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Service' GROUP BY DATE_FORMAT(ab.date, '%M'), DATE_FORMAT(ab.date, '%m') ORDER BY ab.date ASC", function(err1, result1) {
                                    if (!err1 && result1.length > 0) {
                                        if (request.compare_start_date != undefined && request.compare_end_date != undefined) {
                                            var query = con.query("SELECT count(abd.service_id) AS compare_count,DATE_FORMAT(ab.date, '%M') AS month_name,DATE_FORMAT(ab.date, '%m') AS month_number FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE ab.date BETWEEN '" + request.compare_start_date + "' AND '" + request.compare_end_date + "' AND abd.service_provider_id =" + service_provider_id + " AND abd.type ='Service' GROUP BY DATE_FORMAT(ab.date, '%M'), DATE_FORMAT(ab.date, '%m') ORDER BY ab.date ASC", function(err2, result2) {
                                                if (!err2 && result2.length > 0) {
                                                    service_provider.compare_value(result1, result2, function(compared_value) {
                                                        main_graph_obj['all_services'] = compared_value;
                                                        next1()
                                                    })
                                                } else {
                                                    result1[0].compare_count = 0;
                                                    main_graph_obj['all_services'] = result1;
                                                    next1()
                                                }
                                            })
                                        } else {
                                            main_graph_obj['all_services'] = result1;
                                            next1()
                                        }
                                    } else {
                                        main_graph_obj['all_services'] = [];
                                        next1()
                                    }
                                })
                            } else {
                                main_graph_obj['all_services'] = [];
                                next1();
                            }
                        } else {
                            next1();
                        }
                    } else {
                        next1();
                    }
                }, function() {
                    callback('1', "Report found successfully", { tiles: finaltiles, graph: main_graph_obj });
                })
            } else {
                callback('1', "Report found successfully", { tiles: finaltiles, graph: main_graph_obj });
            }
        });
    },

    /**
     * Function for get customer retention ratio
    */
    getCustomerRetentionRatio:function(service_provider_id,request,callback){
        var wherecon = "service_provider_id = '"+service_provider_id+"'";
        var wherecon1 = "service_provider_id = '"+service_provider_id+"'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
                wherecon1 += " AND date BETWEEN DATE_SUB(DATE('"+request.start_date+"'),INTERVAL DATEDIFF('"+request.end_date+"','"+request.start_date+"') DAY) AND '"+request.start_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
                wherecon1 += " AND date BETWEEN DATE_SUB(DATE('"+request.start_date+"'),INTERVAL 30 DAY) AND '"+request.start_date+"'";
            } else {
                wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
                wherecon1 += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 60 DAY) AND DATE_SUB(DATE(NOW()), INTERVAL 30 DAY)";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
                wherecon1 += " AND date BETWEEN DATE_SUB(DATE('"+request.start_date+"'),INTERVAL 7 DAY) AND '"+request.start_date+"'";
            } else {
                wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
                wherecon1 += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 14 DAY) AND DATE_SUB(DATE(NOW()), INTERVAL 7 DAY)";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND date = '"+request.date+"'";
                wherecon1 += " AND date = DATE_SUB(DATE('"+request.date+"'),INTERVAL 1 DAY)";
            } else {
                wherecon += " AND date = current_date()";
                wherecon1 += " AND date = DATE_SUB(DATE(NOW()),INTERVAL 1 DAY)";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            //wherecon1 += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 60 DAY) AND DATE_SUB(DATE(NOW()), INTERVAL 30 DAY)";
        }
        //SELECT((SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE "+wherecon+") - (SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE "+wherecon+" AND user_id NOT IN(SELECT user_id FROM tbl_appointment_booking WHERE "+wherecon+"))) / (SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE "+wherecon+") * 100 as CRR
        con.query("SELECT (((IFNULL((SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE "+wherecon+" GROUP BY service_provider_id),0) - IFNULL((SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE service_provider_id = '"+service_provider_id+"' AND id IN (SELECT id FROM tbl_appointment_booking WHERE "+wherecon+" GROUP BY user_id,customer_id HAVING count(id) <= 1) GROUP BY service_provider_id),0)) / IFNULL((SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE "+wherecon1+" GROUP BY service_provider_id),0))*100) as CRR",function(err1, result1){
            if(err1){
                console.log("customer retention ratio",err1);
            }
            if (!err1 && result1[0] != undefined && result1[0].CRR != undefined) {
                callback(parseFloat(result1[0].CRR).toFixed(2));
            } else {
                callback(parseFloat(0).toFixed(2));
            }
        });
    },

    /**
     * Function for get earning per hour
     */
    getEarningPerHour:function(service_provider_id,request,callback){
        var wherecon = "s.service_provider_id = '"+service_provider_id+"' AND s.is_deleted = '0' AND ab.service_provider_id = '" + service_provider_id + "'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"' AND s.day = DAYNAME('"+request.date+"')";
            } else {
                wherecon += " AND ab.date = current_date() AND s.day = DAYNAME(current_date())";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select s.*,IFNULL(SUM(HOUR(TIMEDIFF(s.from_time, s.to_time))),0) as total_hour,IFNULL(SUM(total_amount),0) as  total_earning from tbl_appointment_booking ab LEFT JOIN tbl_service_provider_available_slot s ON ab.service_provider_id = s.service_provider_id where "+wherecon+" GROUP BY ab.service_provider_id", function(err1, result1) {
            if(err1){
                console.log("earning per hour",err1);
            }
            if (!err1 && result1[0] != undefined && result1[0].total_earning != undefined) {
                callback(parseFloat(result1[0].total_earning / result1[0].total_hour).toFixed(2))
            } else {
                callback(parseFloat(0).toFixed(2));
            }
        });
    },

    /**
     * Function for average ticket(booking) amount
     */
    getAverageTicketAmount:function(service_provider_id,request,callback){
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.is_refund = '0'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select IFNULL(AVG(ab.total_amount),0) as average_ticket_amount from tbl_appointment_booking ab where "+wherecon+" GROUP BY ab.service_provider_id",function(err1,result1){
            if(err1){
                console.log("average ticket(booking) amount",err1);
            }
            if (!err1 && result1[0] != undefined && result1[0].average_ticket_amount != undefined) {
                callback(parseFloat(result1[0].average_ticket_amount).toFixed(2));
            } else {
                callback(parseFloat(0).toFixed(2));
            }
        });
    },

    /**
     * Function for get retail percentage of sales
     */
    getRetailPercentageofsales:function(service_provider_id,request,callback){
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        //SELECT ((select sum(total_amount) from tbl_appointment_booking where service_provider_id = " + service_provider_id + ") / ((select sum(total_amount) from tbl_appointment_booking))) * 100 as retail_percentage_of_sales FROM tbl_appointment_booking ab where ab.date = current_date() AND ab.is_refund = 0 AND service_provider_id = " + service_provider_id + "
        con.query("SELECT (IFNULL((SELECT SUM(abd.price * abd.quantity) FROM tbl_appointment_booking_detail as abd INNER JOIN tbl_appointment_booking as ab ON abd.appointment_id = ab.id WHERE abd.type = 'Product' AND "+wherecon+" GROUP BY ab.service_provider_id),0) * 100) / IFNULL((SELECT SUM(abd.price) FROM tbl_appointment_booking_detail as abd INNER JOIN tbl_appointment_booking as ab ON abd.appointment_id = ab.id WHERE abd.type = 'Service' AND "+wherecon+" GROUP BY ab.service_provider_id),0) as retail_percentage_of_sales",function(err1,result1){
            if(err1){
                console.log("retail percentage of sales",err1);
            }
            if (!err1 && result1[0] != undefined && result1[0].retail_percentage_of_sales != undefined) {
                callback(parseFloat(result1[0].retail_percentage_of_sales).toFixed(2)+"%");
            } else {
                callback(parseFloat(0).toFixed(2)+"%");
            }
        });
    },

    /**
     * Function for get Top Client
     */
    getTopClient:function(service_provider_id,request,callback){
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("SELECT customer_id, user_id, SUM(total_amount) AS total_revenue,IF(ab.customer_name = '',IF(ab.user_id != '0', (SELECT CONCAT(first_name,' ',last_name) FROM tbl_user WHERE id = ab.user_id ORDER BY id DESC LIMIT 1),(SELECT c.customer_name FROM tbl_service_provider_client c WHERE c.id = ab.customer_id ORDER BY id DESC LIMIT 1)) , ab.customer_name) AS customer_name,IF(ab.user_id != '0',(select CONCAT(u.country_code,' ',u.phone) from tbl_user u where u.id = ab.user_id LIMIT 1),(select CONCAT(c.country_code,' ',c.phone) from tbl_service_provider_client c where c.id = ab.customer_id LIMIT 1)) as phone,IF(ab.user_id = '0',(SELECT CONCAT('" + global.S3_BUCKET_ROOT + global.USER_IMAGE + "','',c.profile_image) FROM tbl_service_provider_client c WHERE c.id = ab.client_id),(SELECT CONCAT('"+ global.S3_BUCKET_ROOT + global.USER_IMAGE +"',profile_image) FROM tbl_user WHERE id = ab.user_id ORDER BY ID DESC LIMIT 1)) AS profile_image FROM tbl_appointment_booking ab where "+wherecon+" AND (user_id != 0 OR customer_id != 0) GROUP BY customer_id, user_id ORDER BY total_revenue DESC LIMIT 20", function(err1, result1) {
            if(err1){
                console.log("Top Client",err1);
            }
            if (!err1 && result1.length > 0) {
                callback(result1);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Top Retail Products
     */
    getTopRetailProducts:function(service_provider_id,request,callback){
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND abd.type = 'Product' AND abd.product_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("SELECT p.product_name,SUM(abd.price * quantity) AS price,IFNULL((SELECT ROUND(AVG(ratting), 2) FROM tbl_user_review ur WHERE ur.type_id = abd.product_id AND ur.type = 'product' and ur.ratting > 0),0.0) AS ratting,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',p.product_image) AS product_image FROM tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id LEFT JOIN tbl_product p ON p.id = abd.product_id WHERE "+wherecon+" GROUP BY product_id order by price DESC LIMIT 10 ", function(err1, result1) {
            if(err1){
                console.log("Top Retail Products",err1);
            }
            if (!err1 && result1.length > 0) {
                callback(result1);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Top Service
     */
    getTopService:function(service_provider_id,request,callback){
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND abd.type = 'Service' AND abd.service_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("SELECT s.service_name,SUM(abd.price) AS price,IFNULL((SELECT ROUND(AVG(ratting), 2) FROM tbl_user_review ur WHERE ur.type_id = abd.service_id AND ur.type = 'Service' and ur.ratting > 0),0.0) AS ratting FROM tbl_appointment_booking_detail abd  LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id LEFT JOIN tbl_service s ON s.id = abd.service_id WHERE "+wherecon+" GROUP BY service_id ORDER BY price DESC LIMIT 5", function(err1, result1) {
            if(err1){
                console.log("Top Service",err1);
            }
            if (!err1 && result1.length > 0) {
                callback(result1);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get report tiles data
     */
    getreporttiles(service_provider_id,request,callback){
        var finaltiles = [];
        if(request.type != undefined && request.type == 'old'){
            var sqlquery = "SELECT tmt.* FROM tbl_service_provider_tiles as tspt INNER JOIN tbl_master_tiles as tmt ON tspt.tiles_id = tmt.id WHERE tspt.service_provider_id = '"+service_provider_id+"' AND tmt.status = 'Active' AND tmt.is_deleted = '0' GROUP BY tmt.id ORDER BY tmt.id ASC";
        } else {
            var sqlquery = "SELECT tmt.* FROM tbl_master_tiles as tmt WHERE tmt.status = 'Active' AND tmt.is_deleted = '0' GROUP BY tmt.id ORDER BY tmt.id ASC";
        }
        con.query(sqlquery,function(error,tiles,fields){
            if(!error && tiles.length > 0){
                asyncloop(tiles, function(item, next) {
                    var tiledata = {
                        id:item.id,
                        name:item.name,
                        compare_value:0,
                        value:0,
                        arrayvalue:[],
                    }
                    if (item.id == 1) {
                        service_provider.getCustomerRetentionRatio(service_provider_id,request,function(customer_retention_ratio){
                            tiledata["value"] = customer_retention_ratio;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 2) {
                        service_provider.getEarningPerHour(service_provider_id,request,function(earning_per_hour){
                            tiledata["value"] = earning_per_hour;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 3) {
                        service_provider.getAverageTicketAmount(service_provider_id,request,function(average_ticket_amount){
                            tiledata["value"] = average_ticket_amount;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 4) {
                        service_provider.getRetailPercentageofsales(service_provider_id,request,function(retail_percentage_of_sales){
                            tiledata["value"] = retail_percentage_of_sales;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 5) {
                        service_provider.getTopClient(service_provider_id,request,function(top_client){
                            tiledata["arrayvalue"] = top_client;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 6) {
                        service_provider.getTopRetailProducts(service_provider_id,request,function(top_retail_products){
                            tiledata["arrayvalue"] = top_retail_products;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 7) {
                        service_provider.getTopService(service_provider_id,request,function(top_service){
                            tiledata['arrayvalue'] = top_service;
                            finaltiles.push(tiledata);
                            next()
                        });
                    } else {
                        next()
                    }
                },function() {
                    callback(finaltiles);
                });
            } else {
                callback(finaltiles);
            }
        });
    },

    /**
     * Function for report calculation
     */
    report: function(request, service_provider_id, callback) {
        service_provider.getreporttiles(service_provider_id,request,function(finaltiles){
            if(finaltiles.length > 0){
                if(request.type != undefined && request.type == 'old'){
                    service_provider.getreportgraphs(service_provider_id,request,function(graphs){
                        callback('1', "Report found successfully", { tiles: finaltiles, graph: graphs });
                    });
                } else {
                    callback('1', "Report found successfully", { tiles: finaltiles, graph: [] });
                }
            } else {
                callback('0', "Please add tiles to dashboard", null);
            }
        });
    },

    /** 
     * Function for get Top of Services graph data
    */
    getTopofServicesgraph:function(service_provider_id,request,callback){
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND abd.type = 'Service' AND abd.service_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select COUNT(abd.service_id) as main_count,DAYNAME(ab.date) as day_name,date_format(ab.date,'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where "+wherecon+" GROUP BY ab.date ORDER BY ab.date ASC",function(error,results){
            if(error){
                console.log("getTopofServicesgraph",error);
            }
            if (!error && results.length > 0) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Total Retail Earnings graph data
     */
    getTotalRetailEarningsgraph(service_provider_id,request,callback){
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND abd.type = 'Product' AND abd.product_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        var query = con.query("SELECT sum(abd.price * abd.quantity) AS main_count, DAYNAME(ab.date) AS day_name,date_format(ab.date,'%Y-%m-%d') as date FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE "+wherecon+" GROUP BY ab.date ORDER BY ab.date ASC", function(error, results) {
            if(error){
                console.log("getTotalRetailEarningsgraph",error);
            }
            if (!error && results.length > 0) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Total Service Earnings graph data
     */
    getTotalServiceEarningsgraph:function(service_provider_id,request,callback){
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND abd.type = 'Service' AND abd.service_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select DAYNAME(ab.date) as day_name,sum(abd.price) as main_count,date_format(ab.date,'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where "+wherecon+" GROUP BY ab.date ORDER BY ab.date ASC", function(error, results) {
            if(error){
                console.log("getTotalServiceEarningsgraph",error);
            }
            if (!error && results.length > 0) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Busiest Hours of the day graph data
     */
    getBusiestHoursofthedaygraph:function(service_provider_id,request,callback){
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"' AND s.day = DAYNAME('"+request.date+"')";
            } else {
                wherecon += " AND ab.date = current_date() AND s.day = DAYNAME(current_date())";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:%i') as day_name,date_format(ab.date,'%Y-%m-%d') as date,IFNULL(count(id),0) as main_count from tbl_appointment_booking ab WHERE "+wherecon+" GROUP BY ab.slot_time ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function(error, results) {
            if(error){
                console.log("Busiest Hours of the day graph data",error);
            }
            if (!error && results[0] != undefined) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
    * Function for get Busiest Days in a Week graph data
    */
    getBusiestDaysinaWeekgraph:function(service_provider_id,request,callback){
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"' AND s.day = DAYNAME('"+request.date+"')";
            } else {
                wherecon += " AND ab.date = current_date() AND s.day = DAYNAME(current_date())";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select DAYNAME(ab.date) as day_name,date_format(ab.date,'%Y-%m-%d') as date,IFNULL(count(id),0) as main_count from tbl_appointment_booking ab WHERE "+wherecon+" GROUP BY DAYNAME(ab.date) ORDER BY ab.date ASC", function(error, results) {
            if(error){
                console.log("Busiest Days in a Week graph data",error);
            }
            if (!error && results[0] != undefined) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
    * Function for get Busiest Weeks of the Year graph data
    */
    getBusiestWeeksoftheYeargraph:function(service_provider_id,request,callback){
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"' AND s.day = DAYNAME('"+request.date+"')";
            } else {
                wherecon += " AND ab.date = current_date() AND s.day = DAYNAME(current_date())";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select WEEKOFYEAR(date) as week_number,DAYNAME(ab.date) as day_name,date_format(ab.date,'%Y-%m-%d') as date,IFNULL(count(id),0) as main_count from tbl_appointment_booking ab WHERE "+wherecon+" GROUP BY DAYNAME(ab.date) ORDER BY ab.date ASC", function(error, results) {
            if(error){
                console.log("Busiest Days in a Week graph data",error);
            }
            if (!error && results[0] != undefined) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
    * Function for online bookings graph data
    */
    getOnlineBookingsgraph:function(service_provider_id,request,callback){
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("SELECT round((SUM(CASE WHEN booking_type = 'online' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) AS online_booking_percentage,round((SUM(CASE WHEN booking_type = 'walkin' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) as offline_booking_percentage,DAYNAME(ab.date) as day_name,date_format(ab.date,'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE "+wherecon+" GROUP BY ab.date ORDER BY ab.date ASC",function(error,results){
            if(error){
                console.log("getOnlineBookingsgraph",error);
            }
            if(!error && results.length > 0){
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
    * Function for no shows graph data
    */
    getNoShowsgraph:function(service_provider_id,request,callback){
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.booking_status ='No Show'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        } 
        con.query("SELECT count(id) as main_count,DAYNAME(ab.date) as day_name,date_format(ab.date,'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE "+wherecon+" GROUP BY ab.date ORDER BY ab.date ASC",function(error,results){
            if(error){
                console.log("getNoShowsgraph",error);
            }
            if(!error && results.length > 0){
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
    * Function for cancellations graph data
    */
    getCancellationsgraph:function(service_provider_id,request,callback){
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.booking_status ='Cancelled'";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("SELECT count(id) as main_count,DAYNAME(ab.date) as day_name,date_format(ab.date,'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE "+wherecon+" GROUP BY ab.date ORDER BY ab.date ASC",function(error,results){
            if(error){
                console.log("getCancellationsgraph",error);
            }
            if (!error && results.length > 0) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
    * Function for All Clients graph data
    */
    getAllClientsgraph:function(service_provider_id,request,callback){
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND (ab.customer_id != '0' OR ab.user_id != '0')";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("SELECT COUNT(DISTINCT IF(user_id = 0,customer_id,user_id)) as main_count,DAYNAME(ab.date) as day_name,date_format(ab.date,'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE "+wherecon+" GROUP BY ab.date ORDER BY ab.date ASC",function(error,results){
            if(error){
                console.log("getAllClientsgraph",error);
            }
            if (!error && results.length > 0) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
    * Function for All Retail Products graph data
    */
    getAllRetailProductsgraph:function(service_provider_id,request,callback){
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND abd.type = 'Product' AND abd.product_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select DAYNAME(ab.date) as day_name,sum(abd.quantity) as main_count,date_format(ab.date,'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where "+wherecon+" GROUP BY ab.date ORDER BY ab.date ASC",function(error,results) {
            if(error){
                console.log("getAllRetailProductsgraph",error);
            }
            if (!error && results.length > 0) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
    * Function for All Services graph data
    */
    getAllServicesgraph:function(service_provider_id,request,callback){
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND abd.type = 'Service' AND abd.service_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if(request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != ""){
                wherecon += " AND ab.date BETWEEN '"+request.start_date+"' AND '"+request.end_date+"'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if(request.date != undefined && request.date != ""){
                wherecon += " AND ab.date = '"+request.date+"'";
            } else {
                wherecon += " AND ab.date = current_date()";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select DAYNAME(ab.date) as day_name,count(abd.service_id) as main_count,date_format(ab.date,'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where "+wherecon+" GROUP BY ab.date ORDER BY ab.date ASC",function(error,results){
            if(error){
                console.log("getAllServicesgraph",error);
            }
            if (!error && results.length > 0) {
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
    * Function for get report graphs data
    */
    getreportgraphs(service_provider_id,request,callback){
        var finalgraphs = [];

        var comparerequest = {};
        if(request.period != undefined && request.period != ""){
            comparerequest["period"] = request.period;
        }
        if(request.compare_date != undefined && request.compare_date != ""){
            comparerequest["date"] = request.compare_date;
        }
        if(request.compare_start_date != undefined && request.compare_start_date != "" && request.compare_end_date != undefined && request.compare_end_date != ""){
            comparerequest["start_date"] = request.compare_start_date;
            comparerequest["end_date"] = request.compare_end_date;
        }
        con.query("SELECT tmg.* FROM tbl_service_provider_graph as tspg INNER JOIN tbl_master_graph as tmg ON tspg.graph_id = tmg.id WHERE tspg.service_provider_id = '"+service_provider_id+"' AND tmg.status = 'Active' AND tmg.is_deleted = '0' GROUP BY tmg.id ORDER BY tmg.id ASC",function(error,graphs,fields){
            if(!error && graphs.length > 0){
                asyncloop(graphs, function(item,next){
                    var tiledata = {
                        id:item.id,
                        name:item.name,
                        values:[],
                    }
                    if (item.id == 1) {
                        service_provider.getTopofServicesgraph(service_provider_id,request,function(Top_of_services){
                            if(Top_of_services.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getTopofServicesgraph(service_provider_id,comparerequest,function(compare_Top_of_services){
                                        if(compare_Top_of_services.length > 0){
                                            service_provider.compare_value(Top_of_services, compare_Top_of_services, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = Top_of_services;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = Top_of_services;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 2){
                        service_provider.getTotalRetailEarningsgraph(service_provider_id,request,function(total_retail_earnings){
                            if(total_retail_earnings.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getTotalRetailEarningsgraph(service_provider_id,comparerequest,function(compare_total_retail_earnings){
                                        if(compare_total_retail_earnings.length > 0){
                                            service_provider.compare_value(total_retail_earnings, compare_total_retail_earnings, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = total_retail_earnings;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = total_retail_earnings;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 3){
                        service_provider.getTotalServiceEarningsgraph(service_provider_id,request,function(total_service_earnings){
                            if(total_service_earnings.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getTotalServiceEarningsgraph(service_provider_id,comparerequest,function(compare_total_service_earnings){
                                        if(compare_total_service_earnings.length > 0){
                                            service_provider.compare_value(total_service_earnings, compare_total_service_earnings, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = total_service_earnings;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = total_service_earnings;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 4){
                        service_provider.getBusiestHoursofthedaygraph(service_provider_id,request,function(hoursofthe_day){
                            if(hoursofthe_day.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getBusiestHoursofthedaygraph(service_provider_id,comparerequest,function(compare_hoursofthe_day){
                                        if(compare_hoursofthe_day.length > 0){
                                            service_provider.compare_multiple_value(hoursofthe_day, compare_hoursofthe_day, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = hoursofthe_day;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = hoursofthe_day;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 5){
                        service_provider.getBusiestDaysinaWeekgraph(service_provider_id,request,function(daysofthe_week){
                            if(daysofthe_week.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getBusiestDaysinaWeekgraph(service_provider_id,comparerequest,function(compare_daysofthe_week){
                                        if(compare_daysofthe_week.length > 0){
                                            service_provider.compare_multiple_value(daysofthe_week, compare_daysofthe_week, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = daysofthe_week;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = daysofthe_week;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 6){
                        service_provider.getBusiestWeeksoftheYeargraph(service_provider_id,request,function(weekofthe_year){
                            if(weekofthe_year.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getBusiestWeeksoftheYeargraph(service_provider_id,comparerequest,function(compare_weekofthe_year){
                                        if(compare_weekofthe_year.length > 0){
                                            service_provider.compare_multiple_value(weekofthe_year, compare_weekofthe_year, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = weekofthe_year;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = weekofthe_year;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 7){
                        service_provider.getOnlineBookingsgraph(service_provider_id,request,function(online_bookings){
                            if(online_bookings.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getOnlineBookingsgraph(service_provider_id,comparerequest,function(compare_online_bookings){
                                        if(compare_online_bookings.length > 0){
                                            service_provider.compare_multiple_value(online_bookings, compare_online_bookings, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = online_bookings;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = online_bookings;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 8){
                        service_provider.getNoShowsgraph(service_provider_id,request,function(no_shows){
                            if(no_shows.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getNoShowsgraph(service_provider_id,comparerequest,function(compare_no_shows){
                                        if(compare_no_shows.length > 0){
                                            service_provider.compare_value(no_shows, compare_no_shows, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = no_shows;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = no_shows;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 9){
                        service_provider.getCancellationsgraph(service_provider_id,request,function(cancellations){
                            if(cancellations.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getCancellationsgraph(service_provider_id,comparerequest,function(compare_cancellations){
                                        if(compare_cancellations.length > 0){
                                            service_provider.compare_value(cancellations, compare_cancellations, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = cancellations;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = cancellations;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 10){
                        service_provider.getAllClientsgraph(service_provider_id,request,function(all_clients){
                            if(all_clients.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getAllClientsgraph(service_provider_id,comparerequest,function(compare_all_clients){
                                        if(compare_all_clients.length > 0){
                                            service_provider.compare_value(all_clients, compare_all_clients, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = all_clients;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = all_clients;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 11){
                        service_provider.getAllRetailProductsgraph(service_provider_id,request,function(all_retail_products){
                            if(all_retail_products.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getAllRetailProductsgraph(service_provider_id,comparerequest,function(compare_all_retail_products){
                                        if(compare_all_retail_products.length > 0){
                                            service_provider.compare_value(all_retail_products, compare_all_retail_products, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = all_retail_products;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = all_retail_products;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if(item.id == 12){
                        service_provider.getAllServicesgraph(service_provider_id,request,function(all_services){
                            if(all_services.length > 0){
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getAllServicesgraph(service_provider_id,comparerequest,function(compare_all_services){
                                        if(compare_all_services.length > 0){
                                            service_provider.compare_value(all_services, compare_all_services, function(compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = all_services;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = all_services;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else {
                        next();
                    }
                },function(){
                    callback(finalgraphs);
                });
            } else {
                callback(finalgraphs);
            }
        });
    },

    /**
    * Function for compare value
    */
    compare_value: function(result1, result2, callback) {
        let result = {};
        result1.forEach(item => {
            if (result[item.day_name]) {
                result[item.day_name].main_count += item.main_count;
            } else {
                result[item.day_name] = { main_count: item.main_count,date:item.date };
            }
        });
        result2.forEach(item => {
            if (result[item.day_name]) {
                result[item.day_name].compare_count = item.main_count;
                result[item.day_name].compare_date = item.date;
            } else {
                result[item.day_name] = { compare_count: item.main_count,date:item.date,compare_date:item.date };
            }
        });
        let finalResult = Object.keys(result).map(key => {
            return { day_name: key,date:result[key].date,compare_date:(result[key].compare_date != undefined) ? result[key].compare_date : '', main_count: (result[key].main_count != undefined) ? result[key].main_count : 0, compare_count: (result[key].compare_count != undefined) ? result[key].compare_count : 0 };
        });
        callback(finalResult);
    },

    /**
    * Function for compare multiple values
    */
    compare_multiple_value: function(result1, result2, callback) {
        let result = {};
        result1.forEach(item => {
            if (result[item.day_name]) {
                result[item.day_name].online_booking_percentage = item.online_booking_percentage;
                result[item.day_name].offline_booking_percentage = item.offline_booking_percentage;
            } else {
                result[item.day_name] = { online_booking_percentage: item.online_booking_percentage, offline_booking_percentage: item.offline_booking_percentage,date:item.date };
            }
        });
        result2.forEach(item => {
            if (result[item.day_name]) {
                result[item.day_name].online_booking_compare_percentage = item.online_booking_percentage;
                result[item.day_name].offline_booking_compare_percentage = item.offline_booking_percentage;
                result[item.day_name].compare_date = item.date;
            } else {
                result[item.day_name] = { online_booking_compare_percentage: item.online_booking_percentage, offline_booking_compare_percentage: item.offline_booking_compare_percentage,date:item.date };
            }
        });
        let finalResult = Object.keys(result).map(key => {
            return { day_name: key,date:result[key].date, online_booking_percentage: (result[key].online_booking_percentage != undefined) ? result[key].online_booking_percentage : 0, offline_booking_percentage: (result[key].offline_booking_percentage != undefined) ? result[key].offline_booking_percentage : 0, online_booking_compare_percentage: (result[key].online_booking_compare_percentage != undefined) ? result[key].online_booking_compare_percentage : 0, offline_booking_compare_percentage: (result[key].offline_booking_compare_percentage != undefined) ? result[key].offline_booking_compare_percentage : 0 };
        });
        callback(finalResult);
    },
};