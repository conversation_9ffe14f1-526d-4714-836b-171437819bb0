var common = require('../../../config/common')
var globals = require('../../../config/constant')
var sp_model = require('./service_provider_model')
var datetime = require('node-datetime')
var asyncLoop = require('node-async-loop')
var router = require('express-promise-router')()
var template = require('../../../config/template')
const moment = require('moment')
const { t } = require('localizify')
const { request } = require('express')
const serviceProviderModelsV2 = require('../../../src/service/serviceProvider')
const { logger, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_WITH_SECONDS } = require('../../../src/utils')
const {
  encrypt,
  decrypt,
  validate,
  addBankAccountOrCard,
  createPromo,
  serviceProviderExists,
  addServiceProvider,
  verifyServiceProviderOTP,
  updateServiceProvider,
  updateUserDeviceInfo,
  getBookingById,
  updateBooking,
  USER_LOGIN_STATUS,
  USER_STATUS,
  softDeletePromo,
  getPromoPage,
  getPromoById,
  checkServiceProviderLogin,
  getHomeScreenDataForServiceProvider,
  getServiceProviderBookingDetails,
  APPOINTMENT_STATUS,
  getBlockAvailability,
  issueBookingRefund,
  changeServiceProviderPassword,
  applyBookingPayment,
  BOOKING_PAYMENT_STATUS,
  cancelBookingByServiceProvider,
  USER_TYPE,
  generateClientReport,
  generateRevenueReport,
  generateBookingsReport,
  getServices,
  getService,
  getServiceProviderByEmailShallow,
  insertImage,
  IMAGE_TYPE,
} = require('../../../src/service')
const startCase = require('lodash/startCase')
const filter = require('lodash/filter')
const { generateOTP } = require('../../../src/utils/otp')

router.post('/sign_up', async (req, res) => {
  const rawBody = decrypt(req.body)
  const rules = {
    first_name: 'required',
    last_name: 'required',
    email: 'required|email',
    password: 'required_if:login_type,S',
    country_code: 'required',
    login_type: 'required|in:S,F,G,A',
    social_id: 'required_unless:login_type,S',
    phone: 'required',
    device_token: 'required',
    device_type: 'required|in:A,I',
    account_type: 'required|in:professional,booth_renter,both',
  }
  const message = {
    required: t('required'),
    required_if: t('required_if'),
    email: t('email'),
    in: t('in'),
  }
  const keyword = {
    first_name: t('keyword_firstname'),
    email: t('keyword_email'),
    password: t('keyword_password'),
    country_code: t('keyword_country_code'),
    phone: t('keyword_phone'),
    device_type: t('keyword_devicetype'),
    device_token: t('keyword_devicetoken'),
  }
  const errors = validate(rawBody, rules, message, keyword)
  if (errors) {
    sendError(res, errors)
    return
  }

  const exists = await serviceProviderExists(
    rawBody.email,
    rawBody.phone,
    rawBody.social_id
  )

  if (exists) {
    sendError(res, {
      message: 'Given email, phone, or social account is already in use',
    })
    return
  }

  const data = await addServiceProvider(rawBody)
  sendSuccess(res, { data })
})

router.post('/verify_otp', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    status: 'required|in:signup,forgotpassword',
    user_id: 'required',
    otp: 'required',
  }
  const messages = { required: t('required') }
  const keywords = { user_id: t('keyword_user_id'), otp: t('keyword_otpcode') }
  const errors = validate(rawData, rules, messages, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await verifyServiceProviderOTP(rawData)
  sendSuccess(res, { data })
})

/**
 * complete profile
 */
router.post('/complete_profile', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      service_provider_id: 'required',
      account_type: 'required|in:professional,booth_renter,both',
      profile_image: 'required',
      bio: 'required',
      state_issue_id: '',
    }
    /*if (request.account_type == 'booth_renter' || request.account_type == 'both') {
            rules.signed_lease = 'required'
        }*/
    if (request.account_type == 'professional') {
      rules.experience = 'required'
      rules.professional_license = ''
      rules.business_location = 'required'
    }
    if (
      request.account_type == 'professional' ||
      request.account_type == 'both'
    ) {
      rules.tax = 'required'
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      sp_model.complete_profile(request, function (response, message, msgcode) {
        common.send_response(req, res, msgcode, message, response)
      })
    }
  })
})

router.post(
  '/forgot_password',
  async (req, res) => {
    const rawBody = decrypt(req.body)
    const rules = { email: 'required|email' }
    const messages = {
      required: t('required'),
      required_if: t('required_if'),
      in: t('in'),
    }
    const keywords = {
      status: t('keyword_status'),
      email: t('keyword_email'),
      phone: t('keyword_phone'),
    }
    const errors = validate(rawBody, rules, messages, keywords)
    if (errors) {
      return sendError(res, errors)
    }

    const sp = await getServiceProviderByEmailShallow(rawBody.email)
    if (sp) {
      switch (sp.login_type) {
        case 'G':
          common.send_response(req, res, '0', 'Please login with google', null)
          break
        case 'F':
          common.send_response(req, res, '0', 'Please login with facebook', null)
          break
        case 'A':
          common.send_response(req, res, '0', 'Please login with apple', null)
          break
        case 'S':
          const forgotpass_otp = {
            forgot_otp: generateOTP(6),
            forgotpwd_datetime: datetime.create().format('Y-m-d H:M:S'),
          }
          await updateServiceProvider(sp.id, forgotpass_otp)
          sp.forgot_otp = forgotpass_otp.forgot_otp
          sp.forgotpwd_datetime = forgotpass_otp.forgotpwd_datetime
          sp.url = `${ globals.app_url }v1/service_provider/forgot_password/${ sp.forgot_otp }/${ sp.id }`
          template.forgot_password(
            sp,
            (result) => {
              const subject = `${ globals.APP_NAME } Forgot Password`
              common.send_email(
                subject,
                sp.email,
                result,
                (result) => {
                  if (!result) {
                    common.send_response(req, res, 0, t('restapi_mail_error'), null)
                  } else {
                    common.send_response(req, res, 1, 'Password reset link has been sent to your registered email.', sp)
                  }
                },
              )
            })
          break
      }
    } else {
      common.send_response(req, res, '0', t('restapi_loginemail_error'), null)
    }
  })

router.post('/add_bank_details', async (req, res, next) => {
  try {
    const rawData = decrypt(req.body)
    const rules = {
      service_provider_id: 'required',
      bank_image: 'required',
      bank_name: 'required',
      account_holder_name: 'required',
      account_number: 'required',
      routing_number: 'required',
      SSN: 'required',
    }
    const messages = { required: t('required') }
    const keywords = {
      bank_name: t('keyword_bank_name'),
      account_holder_name: t('keyword_account_holder_name'),
      account_number: t('keyword_account_number'),
      routing_number: t('keyword_routing_number'),
      SSN: t('keyword_ssn'),
    }

    const validationIssues = validate(rawData, rules, messages, keywords)

    if (validationIssues) {
      logger.error('Failed request validation.', validationIssues)
      res.status(400).json(encrypt({ code: 0, ...validationIssues }))
      return
    }

    const tos_ip =
      req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress
    const data = await addBankDetail({ ...rawData, tos_ip })
    res.status(200).json(encrypt({ data, code: 1 }))
  } catch (e) {
    next(e)
  }
})

router.post('/add_new_account', async (req, res) => {
  const rawData = req.body
  console.debug(`req.user_id: ${req.user_id}`)
  console.debug(`rawData: ${JSON.stringify(rawData)}`)
  await addBankAccountOrCard(req.user_id, rawData)
  res.status(200).json({ data, code: 1 })
})

/**
 * Edit profile
 */
router.post('/edit_profile', function (req, res) {
  var user_id = req.user_id
  common.decryption(req.body, function (request) {
    var rules = {
      first_name: 'required',
      last_name: 'required',
      email: 'required|email',
      country_code: 'required',
      phone: 'required',
      account_type: 'required|in:professional,booth_renter,both',
    }
    if (
      request.account_type == 'professional' ||
      request.account_type == 'both'
    ) {
      rules.tax = 'required'
    }
    var messages = {
      required: t('required'),
      email: t('email'),
    }
    var keywords = {
      name: t('keyword_name'),
      email: t('keyword_email'),
      country_id: t('keyword_countryid'),
      phone: t('keyword_phone'),
      profile_image: t('keyword_profile_image'),
    }
    // checks all validation rules
    if (common.check_validation(request, res, rules, messages, keywords)) {
      sp_model.get_user_detail(user_id, function (user) {
        if (user == null) {
          common.send_response(req, res, '2', t('restapi_user_notfound'), null)
        } else {
          var updparam = {
            first_name: request.first_name,
            last_name: request.last_name,
            // email: request.email,
            country_code: request.country_code,
            phone: request.phone,
            account_type: request.account_type,
          }
          if (
            request.account_type == 'professional' ||
            request.account_type == 'both'
          ) {
            updparam.tax = request.tax
          }
          if (request.email != user.email) {
            updparam.email_verify = 'Pending'
            updparam.email = request.email
            var userdata1 = {}
            userdata1.name = request.first_name + request.last_name
            userdata1.url =
              globals.app_url +
              'home/verify_email/service_provoder/' +
              userdata1.id
            //get forgot password template
            template.send_email_verify(userdata1, function (result) {
              var subject = globals.APP_NAME + ' Verify Account'
              common.send_email(
                subject,
                request.email,
                result,
                function (result1) {}
              ) // Send Email
            }) //end template
          }
          if (request.phone != user.phone && user.login_type == 'S') {
            updparam.otp_verify = 'Pending'
            //var otp = Math.floor(1000 + Math.random() * 9000);
            var otp = generateOTP(6)
            updparam.otp = otp
          }
          var unique_key = { email: request.email, phone: request.phone }
          //check unique email and phone
          sp_model.checkuser_unique(
            user_id,
            unique_key,
            function (response, error) {
              if (response == false) {
                common.send_response(req, res, '0', error, null)
              } else {
                sp_model.update_user(user_id, updparam, function (response) {
                  if (response) {
                    sp_model.get_user_detail(user_id, function (userdata) {
                      if (userdata == null) {
                        common.send_response(
                          req,
                          res,
                          '0',
                          t('restapi_user_notfound'),
                          null
                        )
                      } else {
                        if (request.phone !== user.phone && user.login_type === 'S') {
                          common.send_response(
                            req,
                            res,
                            '4',
                            t('restapi_loginotpverify_error'),
                            userdata
                          )
                        } else {
                          common.send_response(
                            req,
                            res,
                            '1',
                            t('restapi_editprofile_success'),
                            userdata
                          )
                        }
                      }
                    })
                  } else {
                    common.send_response(
                      req,
                      res,
                      '0',
                      t('restapi_user_notfound'),
                      null
                    )
                  }
                })
              }
            }
          )
        }
      })
    }
  })
})

/**
 * Edit service
 * Possibly DEPR
 */
router.post('/edit_service_profile', function (req, res) {
  var user_id = req.user_id
  common.decryption(req.body, function (request) {
    var rules = {
      account_type: 'required|in:professional,booth_renter,both',
      profile_image: 'required',
      bio: 'required',
      state_issue_id: '',
    }
    if (
      request.account_type == 'booth_renter' ||
      request.account_type == 'both'
    ) {
      rules.signed_lease = 'required'
    }
    if (request.account_type == 'professional') {
      rules.experience = 'required'
      rules.professional_license = ''
      rules.business_location = 'required'
    }
    var messages = {
      required: t('required'),
      email: t('email'),
    }
    var keywords = {
      name: t('keyword_name'),
      email: t('keyword_email'),
      country_id: t('keyword_countryid'),
      phone: t('keyword_phone'),
      profile_image: t('keyword_profile_image'),
    }
    // checks all validation rules
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = user_id
      sp_model.get_user_detail(user_id, function (user) {
        if (user == null) {
          common.send_response(req, res, '2', t('restapi_user_notfound'), null)
        } else {
          var updparam = {
            profile_image: request.profile_image,
            bio: request.bio,
            dob:
              request.dob != undefined && request.dob != '' ? request.dob : '',
            state_issue_id:
              request.state_issue_id != undefined &&
              request.state_issue_id != ''
                ? request.state_issue_id
                : '',
            experience: request.experience,
            professional_license:
              request.professional_license != undefined &&
              request.professional_license != ''
                ? request.professional_license
                : '',
          }
          if (request.signed_lease) {
            updparam.signed_lease = request.signed_lease
          }
          sp_model.update_user(user_id, updparam, function (response) {
            if (response) {
              /*var condition = " service_provider_id = '" + user_id + "'";
                            common.delete_data('tbl_business_location', condition, function(location_delete) {
                                common.delete_data('tbl_service_provider_available_slot', condition, function(slot_delete) {
                                    common.delete_data('tbl_service_provider_block_date', condition, function(date_delete) {
                                        common.delete_data('tbl_service_provider_block_time_slot', condition, function(date_time_delete) {
                                            sp_model.add_business_location(request, function(response) {
                                                if (response) {*/
              if (
                request.remove_business_location_ids != undefined &&
                request.remove_business_location_ids != ''
              ) {
                var remove_business_location_ids =
                  request.remove_business_location_ids.split(',')
                asyncLoop(
                  remove_business_location_ids,
                  function (item, next) {
                    common.delete_data(
                      'tbl_business_location',
                      "id = '" + item + "'",
                      function (location_delete) {
                        common.delete_data(
                          'tbl_service_provider_available_slot',
                          "business_location_id = '" + item + "'",
                          function (slot_delete) {
                            common.delete_data(
                              'tbl_service_provider_block_date',
                              "business_location_id = '" + item + "'",
                              function (date_delete) {
                                common.delete_data(
                                  'tbl_service_provider_block_time_slot',
                                  "business_location_id = '" + item + "'",
                                  function (date_time_delete) {
                                    next()
                                  }
                                )
                              }
                            )
                          }
                        )
                      }
                    )
                  },
                  function () {
                    sp_model.get_user_detail(user_id, function (userdata) {
                      if (userdata == null) {
                        common.send_response(
                          req,
                          res,
                          '0',
                          t('restapi_user_notfound'),
                          null
                        )
                      } else {
                        common.send_response(
                          req,
                          res,
                          '1',
                          t('restapi_editprofile_success'),
                          userdata
                        )
                      }
                    })
                  }
                )
              } else {
                sp_model.get_user_detail(user_id, function (userdata) {
                  if (userdata == null) {
                    common.send_response(
                      req,
                      res,
                      '0',
                      t('restapi_user_notfound'),
                      null
                    )
                  } else {
                    common.send_response(
                      req,
                      res,
                      '1',
                      t('restapi_editprofile_success'),
                      userdata
                    )
                  }
                })
              }
              /*} else {
                                                    common.send_response(req, res, '0', t('restapi_globals_error'), null);
                                                }
                                            });
                                        });
                                    });
                                });
                            });*/
            } else {
              common.send_response(
                req,
                res,
                '0',
                t('restapi_globals_error'),
                null
              )
            }
          })
        }
      })
    }
  })
})

/**
 * Save location and availability data
 */
router.post('/savelocationavailability', async function (req, res) {
  const rawData = decrypt(req.body)
  const user_id = req.user_id || rawData.service_provider_id

  if (!user_id) {
    return sendError(res, { error: 'Missing service_provider_id in request!' })
  }
  const rules = {
    business_location: 'required',
  }
  const messages = {
    required: t('required'),
    email: t('email'),
  }
  const keywords = {
    business_location: t('keyword_businesslocation'),
  }
  const error = validate(rawData, rules, messages, keywords)
  if (error) {
    return sendError(res, { message: 'Failed to save a schedule', error })
  }
  logger.debug(
    `/savelocationavailability request body: `,
    JSON.stringify(rawData)
  )
  await serviceProviderModelsV2.updateAvailability(
    user_id,
    rawData.business_location
  )
  const locationIds = rawData.business_location
    .filter((location) => location.id && location.id.length)
    .map((location) => location.id)

  let hours = await serviceProviderModelsV2.getWorkSchedule(
    user_id,
    locationIds
  )

  // enforce 12h format for the time slot values, since that's what frontend expects
  hours = hours.map((item) => {
    return {
      ...item,
      from_time: item.from_time
        ? moment(`1990-10-10 ${item.from_time}`).format('h:mm A')
        : '',
      to_time: item.to_time
        ? moment(`1990-10-10 ${item.to_time}`).format('h:mm A')
        : '',
    }
  })

  const data = {
    business_location: {
      slot_available: hours,
    },
  }
  logger.info({ response: data })
  return sendSuccess(res, { message: 'Schedule saved successfully', data })
})

/**
 * @swagger
 * tags:
 *   name: service_provider
 *   description: Save work schedule for a provider.
 * /v1/{service_provider_id}/save_work_schedule:
 *   post:
 *     tags: [service_provider]
 *     summary: Save service providers availability slots for different days of the week.
 *     parameters:
 *      - in: path
 *        name: service_provider_id
 *        required: true
 *        schema:
 *          type: string
 *        description: Id of a service provider to save work schedule for
 *     requestBody:
 *       required: true
 *       content:
 *        application/json:
 *          schema:
 *            $ref: '#/components/schema/SaveWorkSchedule'
 */

/**
 * @swagger
 *  components:
 *    schema:
 *      SaveWorkSchedule:
 *        type: object
 *        properties:
 *            business_location:
 *              type: array
 *              items:
 *                type: object
 *                properties:
 *                  id:
 *                    type: string
 *                  availability:
 *                    type: array
 *                    items:
 *                      type: object
 *                      properties:
 *                        from_time:
 *                          type: string
 *                        to_time:
 *                          type: string
 *                        day:
 *                          type: string
 */
router.post('/:user_id/save_work_schedule', async (req, res) => {
  const rawData = decrypt(req.body)
  const user_id = req.params.user_id
  if (user_id !== req.user_id) {
    return sendError(res, { error: 'user not authorized' })
  }

  const rules = {
    business_location: 'required',
  }
  const messages = {
    required: t('required'),
    email: t('email'),
  }
  const keywords = {
    business_location: t('keyword_businesslocation'),
  }
  const error = validate(rawData, rules, messages, keywords)
  if (error) {
    return sendError(res, error)
  }

  await serviceProviderModelsV2.updateAvailability(
    user_id,
    rawData.business_location
  )
  const locationIds = rawData.business_location.map((location) => location.id)
  const hours = await serviceProviderModelsV2.getWorkSchedule(
    user_id,
    locationIds
  )
  return sendSuccess(res, hours)
})

/**
 * @swagger
 * tags:
 *   name: service_provider
 *   description: Get working hours per day for a service provider.
 * /v1/{service_provider_id}/work_schedule:
 *   post:
 *     tags: [service_provider]
 *     summary: Return a list of slots per day of the week for a service provider.
 *     parameters:
 *       - in: path
 *         name: service_provider_id
 *         required: true
 *         schema:
 *           type: string
 *         description: Id of a service provider to get a work schedule for.
 */
router.get('/:user_id/work_schedule', async (req, res) => {
  const rawData = decrypt(req.body)
  const user_id = req.params.user_id
  if (user_id !== req.usr_id) {
    return res.status(401)
  }

  const schedule = await serviceProviderModelsV2.getWorkSchedule(
    user_id,
    rawData.business_location_ids || []
  )

  return sendSuccess(res, schedule)
})

router.post('/change_password', async (req, res) => {
  const rawBody = decrypt(req.body)
  const rules = { old_password: 'required', new_password: 'required' }
  const message = { required: t('required') }
  const keywords = {
    old_password: t('keyword_old_password'),
    new_password: t('keyword_new_password'),
  }
  const errors = validate(rawBody, rules, message, keywords)
  if (errors) {
    return sendError(res, errors)
  }

  await changeServiceProviderPassword({ ...rawBody, user_id: req.user_id })
  sendSuccess(res, { message: 'Password changed successfully.' })
})

router.post('/login', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    device_token: 'required',
    device_type: 'required|in:A,I',
    login_type: 'required|in:S,F,G,A',
    password: 'required_if:login_type,S',
    email: rawData.email && rawData.login_type === 'S' ? 'required|email' : '',
    phone: rawData.phone && rawData.login_type === 'S' ? 'required' : '',
  }
  const message = {
    required: 'The :attr field is required',
    required_if: 'The :attr field is required when :other is :value',
    email: 'The :attr must be a valid email address',
  }
  const keywords = {
    email: 'Email',
    password: 'Password',
    device_token: 'Device token',
    device_type: 'Device type',
  }
  const errors = validate(rawData, rules, message, keywords)
  if (errors) {
    sendError(res, errors)
    return
  }

  const loginData = await checkServiceProviderLogin(rawData)
  if (loginData.data) {
    const hours = await serviceProviderModelsV2.getWorkSchedule(
      loginData.data.service_provider_id,
      []
    )
    const available_slot = hours
      .filter((item) => item.to_time && item.from_time)
      .map((item) => ({
        ...item,
        from_time: moment(`1990-10-10 ${item.from_time}`).format('h:mm A'),
        to_time: moment(`1990-10-10 ${item.to_time}`).format('h:mm A'),
      }))

    const businessLocation =
      (await Promise.all(
        loginData.data.business_location?.map(async (bl) => ({
          ...bl,
          slot_available: filter(
            available_slot,
            (slot) => slot.business_location_id === bl.id
          ),
          block_date: await getBlockAvailability(
            loginData.data.service_provider_id,
            bl.id
          ),
        })) ?? []
      )) ?? []

    loginData.data.business_location = businessLocation
  }

  sendSuccess(res, loginData)
})

/**
 * Get service provider details
 */
router.post('/service_provider_details', function (req, res) {
  sp_model.get_user_detail(req.user_id, function (response) {
    if (response != null) {
      common.send_response(req, res, '1', t('restapi_user_detail'), response)
    } else {
      common.send_response(req, res, '2', t('restapi_user_notfound'), response)
    }
  })
})

//get user details
router.post('/user_details', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      user_id: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    //checks all validation
    if (common.check_validation(request, res, rules, message, keywords)) {
      require('../user/user_model').get_user_detail(
        request.user_id,
        function (response, errobj) {
          if (response != null) {
            common.send_response(
              req,
              res,
              '1',
              t('restapi_user_detail'),
              response
            )
          } else {
            common.send_response(
              req,
              res,
              '2',
              t('restapi_user_notfound'),
              response
            )
          }
        }
      )
    }
  })
})

/**
 * Set service provider target

 */
router.post('/set_financial_target', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      amount: 'required',
      target: 'required',
      show_home_target: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      amount: t('keyword_amount'),
      target: t('keyword_target'),
      show_home_target: t('keyword_show_home_target'),
    }
    //checks all validation
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.set_fincial_target(request, function (code, Message, response) {
        common.send_response(req, res, code, Message, response)
      })
    }
  })
})

/**
 * get target list
 */
router.post('/get_financial_target', function (req, res) {
  request.service_provider_id = req.user_id
  sp_model.get_fincial_target(req.user_id, function (code, Message, response) {
    common.send_response(req, res, code, Message, response)
  })
})

/**
 * Add sp client
 */
router.post('/add_new_client', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      profile_image: '',
      customer_name: 'required',
      phone: 'required',
      country_code: 'required',
      email: 'required',
      notes: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      profile_image: t('keyword_profile_image'),
      phone: t('keyword_phone'),
      country_code: t('keyword_country_code'),
      notes: t('keyword_notes'),
      email: t('keyword_email'),
    }
    //checks all validation
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.add_client(request, function (code, message, response) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

/**
 * Edit client detail
 */
router.post('/edit_client', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      profile_image: 'required',
      customer_name: 'required',
      phone: 'required',
      country_code: 'required',
      email: 'required',
      notes: 'required',
      client_id: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      notes: t('keyword_notes'),
    }
    //checks all validation
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      common.checkclientedit_unique(request, function (result, message) {
        if (result) {
          var dataupd = {
            notes: request.notes,
            email: request.email,
            profile_image: request.profile_image,
            customer_name: request.customer_name,
            country_code: request.country_code,
            phone: request.phone,
          }
          common.update_data(
            'tbl_service_provider_client',
            request.client_id,
            dataupd,
            function (response) {
              if (response) {
                common.send_response(
                  req,
                  res,
                  '1',
                  t('restapi_client_edit_success'),
                  response
                )
              } else {
                common.send_response(
                  req,
                  res,
                  '0',
                  t('restapi_globals_error'),
                  response
                )
              }
            }
          )
        } else {
          common.send_response(req, res, '0', message, null)
        }
      })
    }
  })
})

/**
 * List of client
 */
router.post('/all_client', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
      word: '',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    //checks all validation
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.all_client(request, function (code, message, response) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

/**
 * Api for get cliet detail
 */
router.post('/get_client_detail', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      client_id: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      notes: t('keyword_notes'),
    }
    //checks all validation
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.get_client_detail(request, function (code, message, response) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

router.post(
  '/service_category_list',
  async (req, res) => {
    const rawBody = decrypt(req.body)
    rawBody.service_provider_id = req.user_id
    sp_model.service_category_list(
      rawBody,
      (code, message, response) => {
        common.send_response(req, res, code, message, response)
      })
  })

router.post(
  '/add_story',
  async (req, res) => {
    common.decryption(req.body, function(request) {
      const rules = {
        story_name: 'required',
        story_image: 'required',
      }
      const message = {
        required: t('required'),
      }
      const keywords = {
        notes: t('keyword_notes'),
      }

      if (common.check_validation(request, res, rules, message, keywords)) {
        request.service_provider_id = req.user_id
        sp_model.add_story(request, function(code, message, response) {
          common.send_response(req, res, code, message, response)
        })
      }
    })
  })

router.post(
  '/delete_story',
  async (req, res) => {
    common.decryption(req.body, function(request) {
      const rules = {
        story_id: 'required',
      }
      const message = {
        required: t('required'),
      }
      const keywords = {
        story_id: t('keyword_story_id'),
      }
      if (common.check_validation(request, res, rules, message, keywords)) {
        request.service_provider_id = req.user_id
        sp_model.delete_story(request, function(code, message, response) {
          common.send_response(req, res, code, message, response)
        })
      }
    })
  })

router.post(
  '/edit_story',
  async (req, res) => {
    common.decryption(req.body, function(request) {
      const rules = {
        story_id: 'required',
        story_name: 'required',
        story_image: 'required',
      }
      const message = {
        required: t('required'),
      }
      const keywords = {
        story_id: t('keyword_story_id'),
      }
      if (common.check_validation(request, res, rules, message, keywords)) {
        request.service_provider_id = req.user_id
        sp_model.edit_story(request, function(code, message, response) {
          common.send_response(req, res, code, message, response)
        })
      }
    })
  })

router.post(
  '/get_story_detail',
  async (req, res) => {
    common.decryption(req.body, function(request) {
      const rules = {
        story_id: 'required',
      }
      const message = {
        required: t('required'),
      }
      const keywords = {
        story_id: t('keyword_story_id'),
      }
      if (common.check_validation(request, res, rules, message, keywords)) {
        request.service_provider_id = req.user_id
        sp_model.get_story_detail(request, function(code, message, response) {
          common.send_response(req, res, code, message, response)
        })
      }
    })
  })

router.post(
  '/story_list',
  async (req, res) => {
    request.service_provider_id = req.user_id
    sp_model.story_list(req.user_id, function(code, message, response) {
      common.send_response(req, res, code, message, response)
    })
  })

router.post('/reporting/client', async (req, res) => {
  const { scope, startDate, endDate, timezone } = decrypt(req.body)

  if (req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER) {
    return sendError(res, 'Wrong persona requesting the client report')
  }

  const data = await generateClientReport({
    timezone,
    scope,
    startDate,
    endDate,
    serviceProviderId: parseInt(req.user_id),
  })
  return sendSuccess(res, { data })
})

router.post('/reporting/booking', async (req, res) => {
  const { scope, startDate, endDate, timezone } = decrypt(req.body)

  if (req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER) {
    return sendError(res, 'Wrong persona requesting the client report')
  }

  const data = await generateBookingsReport({
    timezone,
    scope,
    startDate,
    endDate,
    serviceProviderId: parseInt(req.user_id),
  })
  return sendSuccess(res, { data })
})

router.post('/reporting/revenue', async (req, res) => {
  const { scope, startDate, endDate, timezone } = decrypt(req.body)

  if (req.user_type?.toLowerCase() !== USER_TYPE.SERVICE_PROVIDER) {
    return sendError(res, 'Wrong persona requesting the client report')
  }

  const data = await generateRevenueReport({
    timezone,
    scope,
    startDate,
    endDate,
    serviceProviderId: parseInt(req.user_id),
  })
  return sendSuccess(res, { data })
})

router.post('/add_service', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      service_images: 'required',
      service_name: 'required',
      description: 'required',
      category_id: 'required',
      price: 'required',
      duration: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      service_images: t('resservice_images'),
    }
    //checks all validation

    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.add_service(request, function (code, message, response) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

/**
 * Edit service
 */
router.post('/edit_service', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      service_id: 'required',
      service_images: 'required',
      service_name: 'required',
      description: 'required',
      category_id: 'required',
      price: 'required',
      duration: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      service_images: t('resservice_images'),
    }

    //checks all validation

    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.edit_service(request, function (code, message, response) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

/**
 * delete service api
 */
router.post('/delete_service', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      service_id: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      service_id: t('keyword_service_id'),
    }

    //checks all validation

    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.delete_service(request, function (code, message, response) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

router.post(
  '/service_detail',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const errors = validate(rawData, { service_id: 'required' },  { required: t('required') }, { service_id: t('keyword_service_id') })
    if(errors) {
      return sendError(res, errors)
    }

    const data = await getService(rawData.service_id)
    sendSuccess(res, { data })
})

router.post('/all_service', async (req, res) => {
  const rawBody = decrypt(req.body)
  const { service_provider_id, category_id, page } = rawBody
  const data = await getServices(
    service_provider_id || req.user_id,
    category_id,
    null,
    page
  )
  sendSuccess(res, { data })
})

/**
 * Api for create post
 */
router.post('/create_post', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      post_images: 'required',
      caption: 'required',
      description: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      images: t('keyword_images'),
    }

    //checks all validation

    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.create_post(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

/**
 * Api for get post list
 */
router.post('/post_list', function (req, res) {
  //checks all validation

  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      page: t('page'),
    }

    //checks all validation

    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.post_list(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

/**
 * Api for get post detail
 */
router.post('/post_detail', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      post_id: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      post_id: 'Post Id',
    }

    //checks all validation

    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.post_detail(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

/**
 * Api for comment list
 */
router.post('/get_post_comments', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      post_id: 'required',
      page: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      post_id: 'Post Id',
    }

    //checks all validation

    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      request.service_provider_uuid = req.user_uuid
      request.type = req.user_type
      sp_model.get_post_comments(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

/**
 * api for delete post
 */
router.post('/delete_post', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      post_id: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {
      post_id: 'Post Id',
    }

    //checks all validation

    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.delete_post(request, function (responsecode, message, response) {
        common.send_response(req, res, responsecode, message, null)
      })
    }
  })
})

/**
 * Api for logout
 * oct 3
 */
router.get('/logout', function (req, res) {
  var updparam = {
    login_status: 'Offline',
  }
  sp_model.update_user(req.user_id, updparam, function (response) {
    if (response) {
      var update_device = {
        token: '',
        device_token: '',
        user_type: 'service_provider',
      }
      common.save_user_deviceinfo(
        req.user_id,
        update_device,
        function (response, err) {
          if (!err) {
            common.send_response(
              req,
              res,
              '1',
              t('restapi_logout_success'),
              null
            )
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              null
            )
          }
        }
      )
    }
  })
})

/**
 * Api for update device info
 * oct 3
 */
router.post('/updatedeviceinfo', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      latitude: 'required',
      longitude: 'required',
      device_token: 'required',
      device_type: 'required',
    }
    var message = {
      required: t('required'),
    }
    var keyword = {
      latitude: t('keyword_latitude'),
      longitude: t('keyword_longitude'),
    }
    if (common.check_validation(request, res, rules, message, keyword)) {
      var user_update = {
        latitude: request.latitude,
        longitude: request.longitude,
      }
      sp_model.update_user(req.user_id, user_update, function (response) {
        if (response) {
          var updateparam = {
            device_token: request.device_token,
            user_type: 'service_provider',
            device_type: request.device_type,
            uuid:
              request.uuid != undefined && request.uuid != ''
                ? request.uuid
                : '',
            ip: request.ip != undefined && request.ip != '' ? request.ip : '',
            os_version:
              request.os_version != undefined && request.os_version != ''
                ? request.os_version
                : '',
            model_name:
              request.model_name != undefined && request.model_name != ''
                ? request.model_name
                : '',
          }
          require('../../../config/common').save_user_deviceinfo(
            req.user_id,
            updateparam,
            function (callback) {
              if (callback != null) {
                sp_model.get_user_detail(req.user_id, function (userdata, err) {
                  if (userdata == null) {
                    common.send_response(
                      req,
                      res,
                      '0',
                      t('restapi_user_notfound'),
                      null
                    )
                  } else {
                    common.send_response(
                      req,
                      res,
                      '1',
                      t('restapi_location_success'),
                      userdata
                    )
                  }
                })
              } else {
                response(null, t('restapi_login_errormsg'), '0')
              }
            }
          )
        } else {
          common.send_response(req, res, '2', t('restapi_user_notfound'), null)
        }
      })
    }
  })
})

router.post('/create_occational_promo', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    discount_type: 'required|in:Percentage,Flat',
    discount_amount: 'required|min:0',
    max_discount_amount: 'required_if:discount_type,Percentage|min:0|max:100',
    description: 'required',
    promocode: 'required',
    valid_till: 'required',
    applied_on: '',
    applied_service_on: '',
    client_list: '',
  }
  const message = { required: t('required') }
  const keywords = { images: t('keyword_images') }
  const errors = validate(rawData, rules, message, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await createPromo({
    ...rawData,
    client_list: [],
    client_list_notifications: rawData.client_list,
    service_provider_id: req.user_id,
    occasional_promo_image: rawData.occational_promo_image,
  })
  sendSuccess(res, { data, message: 'Promo added successfully' })
})

function sendSuccess(res, data = {}, code = 1) {
  if (res.encryption?.toLowerCase() === 'none')
    return res.status(200).json({ code, ...data })

  res.status(200).json(encrypt({ code, ...data }))
}

function sendError(res, errors = {}, code = 0) {
  if (res.encryption?.toLowerCase() === 'none')
    return res.status(400).json(errors)
  res.status(400).json(encrypt({ code, ...errors }))
}

router.post('/get_occational_promo', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = { page: 'required' }
  const message = { required: t('required') }
  const keywords = { page: t('keyword_page') }
  const errors = validate(rawData, rules, message, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await getPromoPage(req.user_id, rawData.page ?? 1, false)
  sendSuccess(res, { data })
})

router.post('/delete_occational_promo', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = { occational_promo_id: 'required' }
  const message = { required: t('required') }
  const keywords = { occational_promo_id: 'Promo id' }
  const errors = validate(rawData, rules, message, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  await softDeletePromo(rawData.occational_promo_id, req.user_id ?? 0)
  sendSuccess(res, { message: 'Promo was deleted' })
})

router.post('/occational_promo_detail', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = { occational_promo_id: 'required' }
  const message = { required: t('required') }
  const keywords = { occational_promo_id: 'Promo id' }
  const errors = validate(rawData, rules, message, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  request.service_provider_id = req.user_id
  const data = await getPromoById(rawData.occational_promo_id)
  sendSuccess(res, { data })
})

router.post('/create_clientspecific_promo', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    discount_type: 'required|in:Percentage,Flat',
    discount_amount: 'required',
    max_discount_amount: 'required_if:discount_type,Percentage',
    promocode: 'required',
    valid_till: 'required',
    client_list: 'required',
  }
  const message = { required: t('required') }
  const keywords = { images: t('keyword_images') }
  const errors = validate(rawData, rules, message, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await createPromo({
    ...rawData,
    client_list_notifications: rawData.client_list,
    service_provider_id: req.user_id,
    occasional_promo_image: rawData.promo_image,
  })
  sendSuccess(res, { data, message: 'Promo added successfully' })
})

router.post('/get_clientspecific_promo', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = { page: 'required' }
  const message = { required: t('required') }
  const keywords = { page: t('keyword_page') }
  const errors = validate(rawData, rules, message, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await getPromoPage(req.user_id, rawData.page ?? 1, true)
  sendSuccess(res, { data })
})

/*
* Api for check time slot

*/
router.post('/get_available_slot', async (req, res) => {
  common.decryption(req.body, async (request) => {
    var rules = {
      business_location_id: 'required',
      date: 'required',
      timezone: 'required',
      service_id: '',
    }
    var message = {
      required: t('required'),
    }
    var keywords = {}

    //checks all validation

    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      const slots = await serviceProviderModelsV2.getAvailableSlots(request)
      const message =
        slots[0] == undefined || slots.length === 0
          ? 'restapi_slot_notfound'
          : 'restapi_slot_found'
      common.send_response(req, res, '1', message, slots)
    }
  })
})

router.post('/create_new_booking', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      customer_name: 'required',
      client_id: 'required',
      // business_location_id: 'required',
      business_location_id: 'required',
      // payment_mode: 'required|in:Cash,Card',
      // payment_intent_id: 'required_if:payment_mode,Card',
      service: '',
      product: '',
      date: 'required',
      time_slot: 'required',
      tip: '',
      description: '',
      tax: 'required',
      sub_total: 'required',
      promocode: '',
      discount: '',
      total_duration: 'required',
      additional_duration: '',
      wallet_amount: '',
      payment_status: '',
      total_amount: 'required',
    }
    var message = {
      required: t('required'),
      in: t('in'),
      required_if: t('required_if'),
    }
    var keywords = {}
    //checks all validation
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.create_new_booking(request, function (response, message, code) {
        common.send_response(req, res, code, message, response)
      })
    }
  })
})

router.post('/quick_checkout_booking', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      customer_id: 'required',
      business_location_id: 'required',
      payment_mode: 'required|in:Cash,Card',
      payment_intent_id: 'required_if:payment_mode,Card',
      service: '',
      product: '',
      tip: 'required',
      description: '',
      tax: 'required',
      sub_total: 'required',
      total_duration: '',
      additional_duration: '',
      promocode: '',
      discount: '',
      payment_status: '',
      wallet_amount: '',
      total_amount: 'required',
    }
    var message = {
      required: t('required'),
      in: t('in'),
      required_if: t('required_if'),
    }
    var keywords = {}
    //checks all validation
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.quick_checkout_booking(
        request,
        function (response, message, code) {
          common.send_response(req, res, code, message, response)
        }
      )
    }
  })
})

router.post('/applypromocode', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      promocode: 'required',
      sub_total: 'required',
      service_id: '',
      product_id: '',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      promocode: t('rest_keywords_promocode'),
      sub_total: 'Sub total',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      request.user_id = req.user_id
      sp_model.apply_promocode(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/**
 * Api for create token
 */
router.post('/create_payment_intent', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      payment_from: 'required|in:Card,ApplePay,GooglePay',
      transaction_id: 'required_if:payment_from,ApplePay,GooglePay',
      card_holder_name: '',
      card_number: 'required_if:payment_from,Card',
      expiry_month: 'required_if:payment_from,Card',
      expiry_year: 'required_if:payment_from,Card',
      cvv: 'required_if:payment_from,Card',
      amount: 'required',
    }
    const messages = {
      required: t('required'),
      required_if: t('required_if'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      sp_model.create_payment_intent(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/**
 * Api for appointment list
 */
router.post('/my_appointments', (req, res, next) => {
  try {
    const rawData = decrypt(req.body)
    const rules = { last_name: '', first_name: '', phone_number: '', email: '' }
    const messages = { required: t('required') }
    const keywords = { user_id: t('keyword_userid') }
    const errors = validate(rawData, rules, messages, keywords)

    if (errors) {
      common.send_response(
        req,
        res,
        0,
        `Failed request validation, ${errors.message}`
      )
      return
    }

    if (!rawData.service_provider_id) rawData.service_provider_id = req.user_id

    if (!rawData.service_provider_id)
      return common.send_response(
        req,
        res,
        '1',
        'Missing service_provider_id',
        {}
      )

    sp_model.my_appointments(
      rawData,
      rawData.service_provider_id,
      (code, msg, data) => common.send_response(req, res, code, msg, data)
    )
  } catch (e) {
    next(e)
  }
})

router.post('/homescreen', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = { date: '' }
  const messages = { required: 'The :attr field is required' }
  const keywords = { date: 'Date' }
  const errors = validate(rawData, rules, messages, keywords)

  if (errors) {
    sendError(res, errors)
    return
  }

  rawData.service_provider_id = req.user_id

  if (rawData.timezone && rawData.timezone_diff) {
    await updateServiceProvider(req.user_id, {
      timezone: rawData.timezone,
      timezone_diff: rawData.timezone_diff,
    })
  }

  const data = await getHomeScreenDataForServiceProvider(rawData)
  sendSuccess(res, { data })
})

/**Api for cancel list */
router.post('/cancel_reason_list', function (req, res) {
  req.service_provider_id = req.user_id
  req.user_type = 'service_provider'
  sp_model.cancel_reason_list(req, function (msgcode, message, response) {
    common.send_response(req, res, msgcode, message, response)
  })
})

router.post('/cancel_appointment', async (req, res) => {
  const rawBody = decrypt(req.body)
  const rules = { appointment_id: 'required', cancel_id: '', cancel_reason: '' }
  const messages = { required: t('required') }
  const keywords = { appointment_id: 'Appointment id' }
  const errors = validate(rawBody, rules, messages, keywords)

  if (errors) {
    return sendError(res, errors)
  }

  const data = await cancelBookingByServiceProvider(rawBody.appointment_id, {
    ...rawBody,
    service_provider_id: req.user_id,
  })
  return sendSuccess(res, { data })
})

router.post('/booking_details', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = { appointment_id: 'required' }
  const messages = { required: 'The :attr field is required' }
  const keywords = { appointment_id: 'Appointment id' }
  const errors = validate(rawData, rules, messages, keywords)
  if (errors) {
    sendError(res, errors)
    return
  }

  const data = await getServiceProviderBookingDetails({
    ...rawData,
    service_provider_id: req.user_id,
  })
  sendSuccess(res, { data })
})

async function _handleInProgressAppointmentStatus(props) {
  const [booking_item] = await getBookingById({
    booking_id: props.appointment_id,
    service_provider_id: props.user_id,
  })

  const booking_date = moment(booking_item[0].date).format('YYYY-MM-DD')
  const booking_date_time = moment.tz(
    `${booking_date} ${booking_item[0].slot_time}`,
    props.timezone
  )
  // Check appointment is within the acceptable time range to be started
  const time_diff = moment
    .duration(moment().tz(props.timezone).diff(booking_date_time))
    .asMinutes()
  if (time_diff < -65.0 || time_diff > 245) {
    return {
      error:
        "Can't start an appointment that is 60 minutes before or 4 hours after scheduled time.",
    }
  }

  return { message: 'Appointment has started' }
}

async function _handlePaidAppointmentStatus(props) {
  const [booking_item] = await getBookingById({
    booking_id: props.appointment_id,
    service_provider_id: props.user_id,
  })

  if (!booking_item[0]) {
    throw new Error('Failed to identify the booking for payment')
  }

  const total_amount =
    props.total_amount && props.total_amount > 0
      ? props.total_amount
      : booking_item[0]?.total_amount
  await applyBookingPayment({
    ...booking_item[0],
    total_amount,
    tip_amount: props.tip_amount || 0,
  })
  await updateBooking(booking_item[0].id, {
    booking_status: props.status || BOOKING_PAYMENT_STATUS.PAID,
  })
  return { message: 'Payment accepted' }
}

async function _handleCompletedAppointmentStatus(rawBody) {
  await Promise.all(rawBody.image?.map((it) => insertImage({
    image_name: it.image_name,
    type_id: rawBody.appointment_id,
    type: IMAGE_TYPE.COMPLETE_APPOINTMENT,
    is_deleted: 0,
    insertdate: moment().format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_WITH_SECONDS),
  })) ?? [])
  return { message: 'Appointment completed' }
}

/**
 * @swagger
 * /v2/service_provider/change_booking_status:
 *   post:
 *     tags: [service_provider, booking, status]
 *     summary: Pay for service (with or without tips) or change booking status.
 *     description: Pay for service (with or without tips) or change booking status.
 *                To pay for a status, use status = "paid";
 *                To set start an appointment, use status = "in_progress"
 *                To complete an appointment after payment has went through, use status = "completed";
 *     requestBody:
 *       required: true
 *       content:
 *        application/json:
 *          schema:
 *              type: object
 *              required:
 *                  - service_provider_id
 *                  - status
 *                  - appointment_id
 *                  - total_amount
 *                  - payment_method
 *              properties:
 *                  service_provider_id:
 *                      type: string
 *                  status:
 *                      type: string
 *                      default: "in_progress | paid | completed"
 *                  appointment_id:
 *                      type: string
 *                  total_amount:
 *                      type: string
 *                  payment_method:
 *                      type: string
 *                      example: "<cardId>"
 *                  tip_amount:
 *                      type: number
 *                      default: 0
 */
router.post('/change_booking_status', async function (req, res) {
  const rawBody = decrypt(req.body)
  const rules = {
    appointment_id: 'required',
    status: 'required',
    tip_amount: '',
    total_amount: '',
    payment_method: '',
    image: '',
  }
  const messages = { required: t('required') }
  const keywords = { appointment_id: 'Appointment id' }
  const errors = validate(rawBody, rules, messages, keywords)
  if (errors) {
    return sendError(res, errors)
  }

  rawBody.total_amount = rawBody.total_amount
    ? parseFloat(rawBody.total_amount.toString())
    : rawBody.total_amount
  rawBody.user_id = req.user_id
  rawBody.status = startCase(rawBody.status.toLowerCase())

  let handlerResp = {}
  switch (rawBody.status.toLowerCase()) {
    case APPOINTMENT_STATUS.IN_PROGRESS:
      handlerResp = await _handleInProgressAppointmentStatus(rawBody)
      if (handlerResp.error) {
        return sendError(res, { message: handlerResp.error })
      }
      break
    case APPOINTMENT_STATUS.PAID:
      handlerResp = await _handlePaidAppointmentStatus(rawBody)
      break
    case APPOINTMENT_STATUS.COMPLETED:
      handlerResp = await _handleCompletedAppointmentStatus(rawBody)
      break
    default:
      throw Error(`Couldn't complete appointment`)
  }

  await updateBooking(rawBody.appointment_id, {
    booking_status: rawBody.status,
  })

  const [booking_item] = await getBookingById({
    booking_id: rawBody.appointment_id,
    service_provider_id: req.user_id,
  })

  sendSuccess(res, {
    message: handlerResp.message,
    data: booking_item[0] ?? {},
  })
})

/**Api for change boooking status */
router.post('/uploadcustomerbio', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      appointment_id: 'required',
      description_notes: '',
      image: '',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      appointment_id: 'Appointment id',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.uploadcustomerbio(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**Api for Rent booth*/
router.post('/rent_booth', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      workspace_category_id: 'required',
      location: '',
      latitude: 'required',
      longitude: 'required',
      amenities_id: '',
      start_date: '',
      end_date: '',
      days: '',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.rent_booth(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response)
      })
    }
  })
})

/**Notification list*/
router.post('/notification_list', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      request.receiver_type =
        request.is_boothowner != undefined && request.is_boothowner == '1'
          ? 'booth_owner'
          : 'service_provider'
      common.get_notification(request, function (response, resmsg, msgcode) {
        common.send_response(req, res, msgcode, resmsg, response)
      })
    }
  })
})

/**Api for service history*/
router.post('/service_history', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      client_id: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      sp_model.service_history(request, function (msgcode, resmsg, response) {
        common.send_response(req, res, msgcode, resmsg, response)
      })
    }
  })
})

/**Api for product history */
router.post('/product_history', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      client_id: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      sp_model.product_history(request, function (msgcode, resmsg, response) {
        common.send_response(req, res, msgcode, resmsg, response)
      })
    }
  })
})

/**Api used for get calender detail */
router.post('/calender_detail', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      month: 'required',
      year: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id
      sp_model.calender_detail(request, function (msgcode, resmsg, response) {
        logger.info({
          message: 'calender_detail response',
          data: { response, resmsg, msgcode },
        })
        common.send_response(req, res, msgcode, resmsg, response)
      })
    }
  })
})

/**
 * Api for client appointment detail
 */
router.post('/client_appointment_details', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      appointment_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      appointment_id: 'Appointment id',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.client_appointment_details(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Api for product purchase detail
 */
router.post('/product_purchase_detail', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      // appointment_id: 'required',
      appointment_detail_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      appointment_id: 'Appointment id',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.product_purchase_detail(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Api for review list
 */
router.post('/review_list', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      page: 'Page',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.review_list(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response)
      })
    }
  })
})

/**
 * Api for review detail screen
 */
router.post('/review_detail', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      review_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      page: 'Page',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.review_detail(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response)
      })
    }
  })
})

/**
 * Api for review reply
 */
router.post('/review_reply', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      review_id: 'required',
      reply: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      review_id: 'Review Id',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      sp_model.review_reply(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response)
      })
    }
  })
})

/**
 * Api for service history
 */
router.post('/sales_history', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.sales_history(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response)
      })
    }
  })
})

/**
 * Api for booth renting
 */
router.post('/booth_renting', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_location_id: 'required',
      workspace_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.booth_renting(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response)
      })
    }
  })
})

/**
 * Api for booth renter available calender
 */
router.post('/available_booth_rent_date', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_location_id: 'required',
      workspace_id: 'required',
      month: 'required',
      year: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.available_booth_rent_date(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Api for get slot for booth renting
 */
router.post('/booth_rent_timeslot', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_location_id: 'required',
      workspace_id: 'required',
      date: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.booth_rent_timeslot(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Booth rent checkout page
 */
router.post('/booth_rent_payment', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_location_id: 'required',
      workspace_id: 'required',
      booth_owner_id: 'required',
      date: 'required',
      start_slot_time: 'required',
      end_date: 'required',
      end_slot_time: 'required',
      price: 'required',
      sub_total: 'required',
      tax: 'required',
      total_amount: 'required',
      payment_mode: 'required|in:Card,ApplePay,GooglePay',
      payment_intent_id: 'required',
      transaction_id: 'required_if:payment_mode,ApplePay,GooglePay',
    }
    var messages = {
      required: t('required'),
      required_if: t('required_if'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.booth_rent_payment(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Api for booth history
 */
router.post('/booth_history', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      date: '',
      page: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {
      date: 'Date',
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.booth_history(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response)
      })
    }
  })
})

router.post('/refund_amount', async (req, res) => {
  const rawData = decrypt(req.body)
  const rules = {
    amount: 'required',
    appointment_id: 'required',
  }
  const messages = { required: t('required') }
  const keywords = { amount: t('keyword_amount') }
  const errors = validate(rawData, rules, messages, keywords)
  if (errors) {
    return sendError(res, errors)
  }

  const info = await issueBookingRefund({
    ...rawData,
    service_provider_id: req.user_id,
  })
  sendSuccess(res, info)
})

/**

 * API for get booth rent cancel reason list
*/
router.post('/cancelrentboothreason', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      type: 'required|in:service_provider,booth_host',
    }
    var messages = {
      required: t('required'),
      in: t('in'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      request.user_type =
        request.type == 'service_provider' ? 'booth_user' : 'booth_host'
      sp_model.cancel_boothrent_reason_list(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * API Booth detail
 */
router.post('/booth_detail', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_rent_id: 'required',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.booth_detail(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response)
      })
    }
  })
})

/*
 * API FOR provider cancel booth rent request
 */
router.post('/cancelboothrent', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_rent_id: 'required',
      cancel_id: '',
      cancel_reason: '',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.cancelboothrentbooking(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Api for rate booth
 */
router.post('/rate_booth', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_rent_id: 'required',
      booth_owner_id: 'required',
      ratting: 'required',
      review: '',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.rate_booth(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response)
      })
    }
  })
})

/**
 * API for report list
 */
router.post('/report', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      type: '',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.report(
        request,
        req.user_id,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/**
 * Get product list name
 */
router.post('/get_product_list', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      type: '',
    }
    var messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.get_product_list(
        req.user_id,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

// get service provider followers list
router.post('/getfollowerslist', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      page: t('keyword_page'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.getserviceproviderfollowerslist(
        request,
        function (followerslist) {
          if (followerslist != null) {
            common.send_response(
              req,
              res,
              '1',
              t('rest_keywords_followerslist_success'),
              followerslist
            )
          } else {
            common.send_response(
              req,
              res,
              '2',
              t('rest_keywords_followerslist_notfound'),
              null
            )
          }
        }
      )
    }
  })
})
router.post('/removefollower', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      user_id: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      user_id: t('keyword_userid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      common.delete_data(
        'tbl_user_followers',
        "follow_by = '" +
          request.user_id +
          "' AND follow_to = '" +
          request.service_provider_id +
          "' AND follow_to_type = 'service_provider'",
        function (is_deleted) {
          if (is_deleted) {
            common.send_response(req, res, '1', 'Success', null)
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              null
            )
          }
        }
      )
    }
  })
})

router.post('/deleteaccount', async (req, res) => {
  await updateServiceProvider(req.user_id, {
    is_deleted: 1,
    updatetime: datetime.create().format('Y-m-d H:M:S'),
    login_status: USER_LOGIN_STATUS.OFFLINE,
    status: USER_STATUS.INACTIVE,
  })
  await updateUserDeviceInfo(
    req.user_id,
    { token: '', device_token: '' },
    'service_provider'
  )
  await common.update_data_condition(
    'tbl_service',
    `service_provider_id = ${req.user_id}`,
    { is_deleted: 1, updatetime: datetime.create().format('Y-m-d H:M:S') },
    () =>
      common.update_data_condition(
        'tbl_product',
        `service_provider_id = ${req.user_id}`,
        { is_deleted: 1, updatetime: datetime.create().format('Y-m-d H:M:S') },
        () =>
          common.send_response(
            req,
            res,
            1,
            'Account removed successfully',
            null
          )
      )
  )
})

/*
 * API for gives customer rate and reviews
 */
router.post('/saveratereview', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      rateto_id: 'required',
      rateto_type: 'required|in:User,Client',
      ratting: 'required',
      order_id: 'required',
    }
    const messages = {
      required: t('required'),
      in: t('in'),
    }
    var keywords = {
      user_id: t('keyword_ratetoid'),
      rateto_type: t('keyword_ratetotype'),
      ratting: t('keyword_ratting'),
      order_id: t('keyword_orderid'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.saveclientreviews(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/*
 * API for save service provider notification settings
 */
router.post('/savenotificationsetting', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      chat_notify: 'required|in:On,Off',
      incomingcall_notify: 'required|in:On,Off',
      newappointment_notify: 'required|in:On,Off',
      newreview_notify: 'required|in:On,Off',
      beforeshitreminder_notify: 'required|in:On,Off',
      beforeappointmentreminder_notify: 'required|in:On,Off',

      beforeshitreminder_time: 'required_if:beforeshitreminder_notify,On',
      beforeappointmentreminder_time:
        'required_if:beforeappointmentreminder_notify,On',
    }
    const messages = {
      required: t('required'),
      in: t('in'),
    }
    var keywords = {
      type: t('keyword_type'),
      status: t('keyword_status'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      var updparam = {
        chat_notify: request.chat_notify,
        incomingcall_notify: request.incomingcall_notify,
        newappointment_notify: request.newappointment_notify,
        newreview_notify: request.newreview_notify,
        beforeshitreminder_notify: request.beforeshitreminder_notify,
        beforeappointmentreminder_notify:
          request.beforeappointmentreminder_notify,
        updatetime: datetime.create().format('Y-m-d H:M:S'),
      }
      if (request.beforeshitreminder_notify == 'On') {
        updparam.beforeshitreminder_time = request.beforeshitreminder_time
      }
      if (request.beforeappointmentreminder_notify == 'On') {
        updparam.beforeappointmentreminder_time =
          request.beforeappointmentreminder_time
      }
      common.update_data(
        'tbl_service_provider',
        req.user_id,
        updparam,
        function (is_update) {
          if (is_update) {
            sp_model.get_user_detail(req.user_id, function (userdata, err) {
              if (userdata == null) {
                common.send_response(
                  req,
                  res,
                  '0',
                  t('restapi_user_notfound'),
                  null
                )
              } else {
                common.send_response(req, res, '1', 'Success', userdata)
              }
            })
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              null
            )
          }
        }
      )
    }
  })
})

/*
 * API for save cancellation(No Show) setting
 */
router.post('/savecancellationsetting', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      customize_cancellation_charges: 'required|in:Yes,No',
      cancellationwithin_hours:
        'required_if:customize_cancellation_charges,Yes',
      cancellation_charges: 'required_if:customize_cancellation_charges,Yes',
      autocancellation_charges:
        'required_if:customize_cancellation_charges,Yes',
    }
    const messages = {
      required: t('required'),
      in: t('in'),
    }
    var keywords = {
      type: t('keyword_type'),
      status: t('keyword_status'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      var updparam = {
        customize_cancellation_charges: request.customize_cancellation_charges,
        updatetime: datetime.create().format('Y-m-d H:M:S'),
      }
      if (request.customize_cancellation_charges == 'Yes') {
        updparam.cancellationwithin_hours = request.cancellationwithin_hours
        updparam.cancellation_charges = request.cancellation_charges
        updparam.autocancellation_charges = request.autocancellation_charges
      }
      common.update_data(
        'tbl_service_provider',
        req.user_id,
        updparam,
        function (is_update) {
          if (is_update) {
            sp_model.get_user_detail(req.user_id, function (userdata, err) {
              if (userdata == null) {
                common.send_response(
                  req,
                  res,
                  '0',
                  t('restapi_user_notfound'),
                  null
                )
              } else {
                common.send_response(req, res, '1', 'Success', userdata)
              }
            })
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              null
            )
          }
        }
      )
    }
  })
})

/*
 * API for save tip amount
 */
router.post('/savetipamount', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      tip_amount1: 'required',
      tip_amount2: 'required',
      tip_amount3: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      tip_amount1: t('keyword_tip_amount1'),
      tip_amount2: t('keyword_tip_amount2'),
      tip_amount3: t('keyword_tip_amount3'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      var updparam = {
        tip_amount1: request.tip_amount1,
        tip_amount2: request.tip_amount2,
        tip_amount3: request.tip_amount3,
        updatetime: datetime.create().format('Y-m-d H:M:S'),
      }
      common.update_data(
        'tbl_service_provider',
        req.user_id,
        updparam,
        function (is_update) {
          if (is_update) {
            sp_model.get_user_detail(req.user_id, function (userdata, err) {
              if (userdata == null) {
                common.send_response(
                  req,
                  res,
                  '0',
                  t('restapi_user_notfound'),
                  null
                )
              } else {
                common.send_response(req, res, '1', 'Success', userdata)
              }
            })
          } else {
            common.send_response(
              req,
              res,
              '0',
              t('restapi_globals_error'),
              null
            )
          }
        }
      )
    }
  })
})

/*
 * imported contact from phone book add client for this service provider
 */
router.post('/importcontacts', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      contacts: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      contacts: t('keyword_contacts'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.addclientsfromimportedcontacts(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/*
 * check block slot availability
 */
router.post('/checkblockavailability', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      date: 'required',
      start_time: 'required',
      end_time: 'required',
      timezone: 'required',
      timezone_diff: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {
      contacts: t('keyword_contacts'),
    }
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.checkblockslotavailability(request, function (blockslotdata) {
        if (blockslotdata == null) {
          common.send_response(req, res, '1', 'Success', null)
        } else {
          common.send_response(
            req,
            res,
            '2',
            t('restapi_blockslotnotavailable_error'),
            null
          )
        }
      })
    }
  })
})

/*
 * API for get service provider booth reviews
 */
router.post('/myboothreviews', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      page: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.getmyboothreviews_list(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/*
* Api for user add review

*/
router.post('/getreviewoptionlist', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      user_type: 'required|in:service_provider,booth_user,booth_host',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.getallreviewoptionlist(
        request,
        function (responseCode, responseMsg, responseData) {
          common.send_response(
            req,
            res,
            responseCode,
            responseMsg,
            responseData
          )
        }
      )
    }
  })
})

/*
* Api for get service provider booth owner rate and reviews

*/
router.post('/boothhostreviewslist', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      booth_owner_id: 'required',
      page: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      require('../booth_owner/booth_owner_model').review_list(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/*
 * Api for get sp slider image
 */
router.post('/getspsliderimage', function (req, res) {
  common.decryption(req.body, function (request) {
    sp_model.getspsliderimages(
      req.user_id,
      function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response)
      }
    )
  })
})

/*
 * Api for upload slider image
 */
router.post('/savesliderimage', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      image_name: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.savesp_sliderimage(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

/*
 * Api for remove slider image
 */
router.post('/removesliderimage', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      image_id: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id
      sp_model.removesp_sliderimage(
        request,
        function (msgcode, message, response) {
          common.send_response(req, res, msgcode, message, response)
        }
      )
    }
  })
})

router.post('/createtransfers', function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      amount: 'required',
      account_id: 'required',
    }
    const messages = {
      required: t('required'),
    }
    var keywords = {}
    if (common.check_validation(request, res, rules, messages, keywords)) {
      require('../../../config/payment').createTransfer(
        {
          amount: Math.round(request.amount * 100),
          currency: 'usd',
          account_id: request.account_id,
          description: 'Wallet booking earning amount',
        },
        function (transferResult, message, msgcode) {
          common.send_response(req, res, msgcode, message, transferResult)
        }
      )
    }
  })
})
module.exports = router
