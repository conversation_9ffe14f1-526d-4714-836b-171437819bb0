const con = require('../../../config/database');
const global = require('../../../config/constant');
const asyncloop = require('node-async-loop');
const datetime = require('node-datetime');
const randtoken = require('rand-token').generator();
const cryptoLib = require('cryptlib');
const shakey = cryptoLib.getHashSha256(global.key, 32);
const { t } = require('localizify');
const common = require('../../../config/common');
const moment = require('moment');
const momentTimezone = require('moment-timezone')
const template = require("../../../config/template");
const queries = require("./queries");
const stripe = require("../../../config/payment");
const { logger, SIMPLE_DATE_FORMAT_WITH_12HR_TIME } = require("../../../src/utils");
const { find: geoFind } = require('geo-tz')
const { USER_TYPE } = require('../../../src/service')

var service_provider = {
    /*
    ** Function for check unique
    */
    check_unique: function (unique_key, callback) {
        if (unique_key.email != undefined && unique_key.email != "") {
            var len = Object.keys(unique_key).length;
            var i = 1;
            asyncloop(unique_key, function (item, next) {
                var query = con.query(`
          SELECT *
          FROM tbl_service_provider
          WHERE is_deleted = '0'
          AND ${item.key} = ?
        `, item.value, function (err, result, fields) {
                    if (!err) {
                        if (result == '') {
                            if (len == i) {
                                callback(true);
                                return;
                            }
                        } else {
                            var message = "";
                            if (item.key == 'email') {
                                message = t('restapi_email_alreadyexists_error');
                            } else if (item.key == 'phone') {
                                message = t('restapi_phone_alreadyexists_error');
                            } else {
                                message = t('restapi_socialid_alreadyexists_error');
                            }
                            // message = ((item.key == 'email') ? t('restapi_email_alreadyexists_error') : t('restapi_phone_alreadyexists_error'))
                            callback(false, message);
                            return;
                        }
                    } else {
                        callback(false + err);
                        return;
                    }
                    i++;
                    next();
                });
            }, function () {
                callback();
            });
        } else {
            callback(true);
        }
    },

    /*
    ** add user
    */
    add_user: function (request, callback) {
        if (request.social_id == undefined && (request.login_type == "F" || request.login_type == "G" || request.login_type == "A")) {
            callback(null, t("rest_keywords_socialid"), '0');
        }
        if (request.login_type == "S" && request.password == undefined) {
            callback(null, t("rest_keywords_password"), '0');
        }
        common.generate_unique_code('tbl_service_provider', function (unique_code) {
            var insert = {
                first_name: request.first_name,
                last_name: request.last_name,
                email: request.email,
                country_code: request.country_code,
                phone: request.phone,
                latitude: (request.latitude != undefined && request.latitude != "") ? request.latitude : "",
                longitude: (request.longitude != undefined && request.longitude != "") ? request.longitude : "",
                otp_verify: 'Pending',
                status: 'Active',
                login_status: 'Offline',
                login_type: request.login_type,
                unique_id: unique_code,
                account_type: request.account_type,
                last_login: datetime.create().format('Y-m-d H:M:S'),
                updatetime: datetime.create().format('Y-m-d H:M:S'),
                insertdate: datetime.create().format('Y-m-d H:M:S'),
                profile_image: 'default-user.png'
            }
            if (request.profile_image != "" && request.profile_image != undefined) {
                insert.profile_image = request.profile_image;
            }
            if (request.social_id != undefined && request.social_id != "") {
                insert.social_id = request.social_id;
                insert.otp_verify = 'Verify';
            }
            if (request.password != undefined && request.password != "") {
                insert.password = cryptoLib.encrypt(request.password, shakey, global.iv);
            }
            // insert.otp = Math.floor(1000 + Math.random() * 9000);
            // insert.otp = '1234';
            con.query("INSERT INTO tbl_service_provider SET ?", insert, function (err, result) {
                if (!err) {
                    var updateparam = {
                        token: randtoken.generate(64, "0123456789abcdefghijklnmopqrstuvwxyz"),
                        device_token: (request.device_token != undefined && request.device_token != "") ? request.device_token : "",
                        device_type: (request.device_type != undefined && request.device_type != "") ? request.device_type : "",
                        uuid: (request.uuid != undefined && request.uuid != "") ? request.uuid : "",
                        ip: (request.ip != undefined && request.ip != "") ? request.ip : "",
                        user_type: 'service_provider',
                        os_version: (request.os_version != undefined && request.os_version != "") ? request.os_version : "",
                        model_name: (request.model_name != undefined && request.model_name != "") ? request.model_name : "",
                    }
                    require('../../../config/common').save_user_deviceinfo(result.insertId, updateparam, function (user_id) {
                        if (user_id != null) {
                            const user_otp = {
                                user_id: result.insertId,
                                status: 'signup'
                            }
                            if (request.social_id != undefined && request.social_id != "") {
                                service_provider.get_user_detail(user_id, function (user_data, err) {
                                    if (user_data != null) {
                                        callback(user_data, t('restapi_signup_sucess'), '1');
                                    } else {
                                        callback(null, t('restapi_globals_error'), '0')
                                    }
                                });
                            } else {
                                /*service_provider.send_otp(user_otp, function(response1) {
                                    if (response1) {*/
                                        service_provider.get_user_detail(user_id, function (user_data, err) {
                                            if (user_data != null) {
                                                callback(user_data, t('restapi_signup_sucess'), '1');
                                            } else {
                                                callback(null, t('restapi_globals_error'), '0')
                                            }
                                        });
                                    /*} else {
                                        callback(null, t('restapi_globals_error'), '0')
                                    }
                                });*/
                            }
                        } else {
                            callback(null, t('restapi_globals_error'), '0')
                        }
                    });
                } else {
                    callback(null, t('restapi_globals_error'), '0')
                }
            });
        });
    },

    /*
    ** Function get uer detail
    */
  get_user_detail: function(user_id, callback) {
    common.get_app_setting(function(settings) {
      con.query(`
        SELECT user.*,
          user.id AS service_provider_id,
          CONCAT('${ global.S3_BUCKET_ROOT }${ global.SP_IMAGE }', user.profile_image) AS profile_image,
          CONCAT('${ global.S3_BUCKET_ROOT }${ global.DOCUMENT_IMAGE }', user.state_issue_id) AS state_issue_id,
          CONCAT('${ global.S3_BUCKET_ROOT }${ global.DOCUMENT_IMAGE }', user.signed_lease) AS signed_lease,
          CONCAT('${ global.S3_BUCKET_ROOT }${ global.DOCUMENT_IMAGE }', user.bank_image) AS bank_image,
          user.profile_image AS image_name,
          device.token,
          GET_SERVICE_PROVIDER_RATING(user.id, NULL) AS ratting,
          GET_SERVICE_PROVIDER_REVIEW_COUNT(user.id, NULL) AS review
        FROM tbl_service_provider user
        JOIN tbl_user_deviceinfo device ON user.id = device.user_id
        WHERE user.id = ${ user_id }
        AND user.is_deleted = '0'
        AND device.user_type = 'service_provider'
      `, function(err, result) {
        if (!err && result[0] != undefined && result[0] != '') {
          con.query(`
            SELECT COUNT(DISTINCT b.user_id) AS total_followers 
            FROM tbl_bookmark AS b
            INNER JOIN tbl_user u ON b.user_id = u.id 
            WHERE b.service_provider_id = ${user_id} 
            AND u.status = 'Active'
            AND NOT u.is_deleted
            AND b.status
        `, function(err1, result1) {
            result[0].booth_rent_tax = settings.booth_rent_tax
            result[0].booth_rent_cleaning_fees = settings.booth_rent_cleaning_fees
            result[0].follower = (!err1 && result1[0] != undefined) ? result1[0].total_followers : 0
            result[0].following = 0
            service_provider.get_business_location(result[0].service_provider_id, function(business_location) {
              result[0].business_location = business_location
              callback(result[0])
            })
          })
        } else {
          callback(null)
        }
      })
    })
  },

    /*
    ** Function get shop by distance
    */
    distance: function (request, table, distance) {
        distance("ROUND((3956 * 2 * ASIN(SQRT( POWER(SIN(('" + request.latitude + "' - " + table + ".latitude) * pi()/180 / 2), 2) + COS('" + request.latitude + "' * pi()/180) * COS(" + table + ".latitude * pi()/180) * POWER(SIN(('" + request.longitude + "' - " + table + ".longitude) * pi()/180 / 2), 2) )))*2) as distance");
        /**below this will work */
        //distance("(3956 * 2 * ASIN(SQRT( POWER(SIN(('" + request.latitude + "' - " + table + ".latitude) *  pi()/180 / 2), 2) +COS( '" + request.latitude + "' * pi()/180) * COS(" + table + ".latitude * pi()/180) * POWER(SIN(( '" + request.longitude + "' - " + table + ".longitude) * pi()/180 / 2), 2) ))) as distance");
        // distance();
    },

    /*
    ** Function user login
    */
    check_login: function (request, response) {
        var error = "";
        if (request.social_id == undefined && (request.login_type == "F" || request.login_type == "G" || request.login_type == "A")) {
            response(null, t('rest_keywords_socialid'), '0');
        }
        if (request.login_type == "S" && request.password == undefined) {
            response(null, t('rest_keywords_password'), '0');
        }
        if (request.social_id != undefined && request.login_type != 'S') {
            var wherecondition = " social_id = '" + request.social_id + "' AND login_type = '" + request.login_type + "' AND is_deleted='0' "
        } else {
            var wherecondition = " (email = '" + request.email + "' OR phone = '" + request.phone + "')  AND is_deleted='0' "
        }
        con.query("select *,id as service_provider_id from tbl_service_provider where " + wherecondition + "", function (err, result) {
            if (!err) {
                if (result[0] != undefined) {
                    if (request.login_type == 'S') {
                        if (result[0].login_type != 'S') {
                            response(null, t('restapi_login_errormsg'), '0');
                            return;
                        }
                        var user_pass = result[0].password;
                        var original_pass = cryptoLib.encrypt(request.password, shakey, global.iv);
                        if (user_pass !== original_pass) {
                            response(null, "Please enter valid email or password", '0');
                            return;
                        }
                    }
                    if (result[0].status == "Inactive") {
                        response(null, t('restapi_logindisactive_error'), '3');
                    } else if (result[0].is_complete_profile == '0') {
                        response(result[0], 'Profile is pending', '8');
                    } else {
                        var login_details = {
                            latitude: (request.latitude != undefined && request.latitude != "") ? request.latitude : "",
                            longitude: (request.longitude != undefined && request.longitude != "") ? request.longitude : "",
                            login_status: "Online",
                            last_login: datetime.create().format('Y-m-d H:M:S')
                        }
                        var query = con.query("UPDATE tbl_service_provider SET ? WHERE id = " + result[0].id + " ", login_details, function (err, result, fields) { });
                        //add update user device information
                        var updateparam = {
                            token: randtoken.generate(64, "0123456789abcdefghijklnmopqrstuvwxyz"),
                            device_token: request.device_token,
                            user_type: 'service_provider',
                            device_type: request.device_type,
                            uuid: (request.uuid != undefined && request.uuid != "") ? request.uuid : "",
                            ip: (request.ip != undefined && request.ip != "") ? request.ip : "",
                            os_version: (request.os_version != undefined && request.os_version != "") ? request.os_version : "",
                            model_name: (request.model_name != undefined && request.model_name != "") ? request.model_name : "",
                        }
                        require('../../../config/common').save_user_deviceinfo(result[0].id, updateparam, function (callback) {
                            if (callback != null) {
                                require("../chat/customtwilio").removeUserBindings('service_provider'+result[0].id).then((is_removed)=>{
                                    service_provider.get_user_detail(result[0].id, function (user_data, err) {
                                        if (user_data != null) {
                                            response(user_data, t('restapi_login_sucess'), '1');
                                        } else {
                                            response(user_data, t('restapi_login_errormsg'), '0');
                                        }
                                    });
                                });
                            } else {
                                response(null, t('restapi_login_errormsg'), '0');
                            }
                        });
                    }
                } else {
                    if (request.social_id != undefined && request.login_type != 'S') {
                        response(null, t('rest_keywords_user_fb_not_found'), '11');
                    } else {
                        response(null, "Please enter valid email or password", '0');
                    }
                }
            } else {
                response(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * send otp  to user
     */
    send_otp: function (request, callback) {
        var query = con.query("SELECT * FROM tbl_service_provider where id='" + request.user_id + "' AND is_deleted = 0", function (err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else {
                if (result[0] != undefined) {
                    if (request.status == 'signup') {
                        var otp = Math.floor(1000 + Math.random() * 9000);
                        var userdata = result[0];
                        // var otp = 1234;
                        userdata.forgot_otp = otp;
                        template.send_otp(userdata, function (result1) {
                            var subject = global.APP_NAME + ' One Time Password (OTP)';
                            common.send_email(subject, userdata.email, result1, function (is_send) {
                                var phone = result[0].country_code + result[0].phone;
                                var message = t('rest_keywords_customerapp_otpcode', {
                                    app_name: global.APP_NAME,
                                    otp_code: otp
                                });
                                require('../../../config/common').sendSMS(phone, message, function (response) {
                                    if (response) {
                                        var otp_update = {
                                            otp_verify: 'Pending',
                                            otp: otp,
                                        }
                                        var query = con.query("UPDATE tbl_service_provider SET ? WHERE id = '" + result[0].id + "' ", otp_update, function (err, res) {
                                            if (!err) {
                                                service_provider.get_user_detail(result[0].id, function (user_data, err) {
                                                    //console.log("result", user_data);
                                                    if (user_data != null) {
                                                        callback('1', t('restapi_otpsent_success'), user_data);
                                                    } else {
                                                        callback('0', t('restapi_sentotp_error'), null);
                                                    }
                                                });
                                            } else {
                                                callback('0', t('restapi_globals_error'), null);
                                            }
                                        });
                                    } else {
                                        callback('0', t('restapi_sentotp_error'), null);
                                    }
                                });
                            }); // Send Email
                        }); //end template
                    } else if (request.status == 'forgotpassword') {
                        service_provider.check_email_phone({
                          status: 'E',
                          email: result[0].email
                        },
                        function (response) {
                            if (response == null) {
                                callback(t('restapi_loginemail_error'));
                            } else {
                                var userdata = response;
                                var rancode = Math.floor(1000 + Math.random() * 9000)
                                var forgotpass_otp = {
                                    forgot_otp: rancode,
                                    forgot_otp_verify: 'Pending',
                                    forgotpwd_datetime: datetime.create().format('Y-m-d H:M:S')
                                };
                                //update user forgot password data
                                service_provider.update_user(userdata.id, forgotpass_otp, function (response) {
                                    if (response) {
                                        userdata.forgot_otp = forgotpass_otp.forgot_otp;
                                        userdata.forgotpwd_datetime = forgotpass_otp.forgotpwd_datetime;
                                        template.send_otp(userdata, function (result) {
                                            var subject = global.APP_NAME + ' Forgot Password';
                                            common.send_email(subject, userdata.email, result, function (result) {
                                                if (!result) {
                                                    callback('0', t('restapi_mail_error'), null);
                                                } else {
                                                    callback('1', t('restapi_forgotpassword_success'), userdata);
                                                }
                                            }); // Send Email
                                        }); //end template
                                    } else {
                                        common.send_response("0", t('restapi_forgotpass_error'), null);
                                    }
                                });
                            }
                        });
                    } else {
                        callback('0', t('restapi_globals_error'), null);
                    }
                } else {
                    callback('0', t('restapi_user_notfound'), null);
                }
            }
        });
    },

    /*
    ** Function verify user otp
    */
    verify_otp: function (request, response) {
        // verify otp
        var condition = " id = '" + request.user_id + "' AND is_deleted = '0' ";
        if (request.status == "signup") {
            condition += " AND otp = '" + request.otp + "' ";
            var otp_status = {
                otp: '',
                otp_verify: 'Verify',
                last_login: datetime.create().format('Y-m-d H:M:S')
            }
        }
        if (request.status == "forgotpassword") {
            condition += " AND forgot_otp = '" + request.otp + "'";
            var otp_status = {
                forgot_otp: '',
                forgot_otp_verify: 'Verify',
                last_login: datetime.create().format('Y-m-d H:M:S')
            }
        }
        var query = con.query("SELECT * FROM tbl_service_provider where  " + condition + " ", function (err, result) {
            if (err) {
                response(null, t('restapi_globals_error'), '0');
            } else {
                if (result[0] != "" && result[0] != undefined) {

                    var query = con.query("UPDATE tbl_service_provider SET ? WHERE id = '" + request.user_id + "' ", otp_status, function (err, res) {
                        if (!err) {
                            var updateparam = {
                                token: randtoken.generate(64, "0123456789abcdefghijklnmopqrstuvwxyz"),
                                user_type: 'user'
                            }
                            require('../../../config/common').save_user_deviceinfo(request.user_id, updateparam, function () {
                                service_provider.get_user_detail(request.user_id, function (userdata, err) {
                                    response(userdata, t('restapi_verifyotp_success'), '1');
                                });
                            });
                        } else {
                            response(null, t('restapi_globals_error'), '0');
                        }
                    });
                } else {
                    response(null, t('restapi_verifyotp_error'), '0');
                }
            }
        });
    },

    /*
    ** Function reset password api
    */
    resetpassword: function (request, callback) {
        var password = cryptoLib.encrypt(request.password, shakey, global.iv);
        var update_customer = {
            password: password
        };
        service_provider.update_user(request.user_id, update_customer, function (error) {
            if (!error) {
                callback('0', t('restapi_globals_error'), null);
            } else {
                service_provider.get_user_detail(request.user_id, function (userdetails) {
                    if (userdetails) {
                        callback('1', t('restapi_passwordreset_success'), userdetails);
                    } else {
                        callback('0', t('restapi_globals_error'), null);
                    }
                })
            }
        });
    },

  check_email_phone: (data, callback) => {
    let condition = ''
    if (data.status === 'E') {
      condition += ` AND email = '${ data.email }'`
    }

    if (data.status === 'P') {
      condition += ` AND phone = '${ data.phone }'`
    }

    con.query(`
      SELECT *, id AS user_id
      FROM tbl_service_provider
      WHERE NOT is_deleted
      ${ condition }
    `,
    (err, result) => {
      if (!err) {
        callback(result[0])
      } else {
        logger.error({
          message: 'Failed to get service provider during check by email/phone',
          meta: data,
          error: err,
        })
        callback(null)
      }
    })
  },
  
    update_user: function (user_id, update, callback) {
        con.query("UPDATE tbl_service_provider SET ? WHERE id = '" + user_id + "'", update, function (err, result, fields) {
            if (!err) {
                callback(true);
            } else {
                callback(false);
            }
        });
    },
  checkuser_unique: function(user_id, unique_key, callback) {
    let len = Object.keys(unique_key).length
    let i = 1
    asyncloop(
      unique_key,
      (item, next) => {
        con.query(`
            SELECT *
            FROM tbl_service_provider
            WHERE id != ${ user_id }
            AND is_deleted = '0'
            AND ${ item.key } = ?
          `,
          item.value,
          (err, result) => {
            logger.info({ message: 'Check for unique service provider on edit_profile', error: err, meta: { key: item.key, value: item.value, result, user_id }})
            if (!err) {
              if (!result[0]) {
                if (len === i) {
                  return callback(true)
                }
              } else {
                return callback(false, item.key === 'email' ? 'The email is already in use' : 'The phone is already in use')
              }
            } else {
              return callback(false, t('restapi_globals_error'))
            }
            i++
            next()
          })
      },
      () => callback(),
    )
  },

    /*
    ** Function get country list
    */
    country_list: function (request, callback) {
        var condition = " is_delete = '0' ";
        if (request.country_id != undefined && request.country_id != "") {
            condition += "AND id = '" + request.country_id + "' ";
        }
        var sql = con.query("SELECT id,name,dial_code from tbl_country where " + condition + " AND is_active = 1", function (err, result) {
            // //console.log(sql);
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback(result, t('restapi_country_found'), '1');
                } else {
                    callback(null, t('restapi_country_notfound'), '1');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /*
    ** Function change password
    */
    change_password: function (req, callback) {
        con.query("select * from tbl_service_provider where id = '" + req.user_id + "' and is_deleted = 0 ", function (err, result, fields) {
            if (!err) {
                var decrypt_password = cryptoLib.decrypt(result[0].password, shakey, global.iv);
                if (decrypt_password == req.old_password) {
                    if (req.old_password == req.new_password) {
                        callback(null, t('reatapi_user_old_new_password_same'), '0');
                    } else {
                        var password = {
                            password: cryptoLib.encrypt(req.new_password, shakey, global.iv)
                        };
                        con.query("UPDATE tbl_service_provider SET ? WHERE id = '" + req.user_id + "'", password, function (err, result, fields) {
                            if (!err) {
                                callback(true, t('restapi_user_change_password_success'), '1');
                            } else {
                                callback(null, t('restapi_globals_error'), '0');
                            }
                        });
                    }
                } else {
                    callback(null, t('restapi_user_change_password_fail'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /*
    ** Function add contact us information
    */
    contact_us: function (addparams, response) {
        var sql = con.query("INSERT INTO tbl_contactus SET ?", addparams, function (err, result) {
            if (!err && result != undefined) {
                response(addparams);
            } else {
                response(null);
            }
        })
    },

    /**
     * This function is used for complete profile
     * @param {*} request
     * @param {*} callback
     */
    complete_profile: function (request, callback) {
        var complete_profiles = {
            profile_image: request.profile_image,
            bio: request.bio,
            dob: (request.dob != undefined && request.dob != "") ? request.dob : "",
            is_complete_profile: '1',
            state_issue_id: (request.state_issue_id != undefined && request.state_issue_id != "") ? request.state_issue_id : "",
            //business_location: (request.business_location != undefined && request.business_location != "") ? request.business_location : "",
            last_login: datetime.create().format('Y-m-d H:M:S'),
            updatetime: datetime.create().format('Y-m-d H:M:S'),
            profile_image: (request.profile_image != undefined && request.profile_image != "") ? request.profile_image : "default-user.png",
        }
        if (request.account_type == 'booth_renter' || request.account_type == 'both') {
            complete_profiles.signed_lease = (request.signed_lease != undefined && request.signed_lease != "") ? request.signed_lease : "";
        }
        if (request.account_type == 'professional' || request.account_type == 'both') {
            complete_profiles.experience = request.experience;
            complete_profiles.tax = request.tax;
            complete_profiles.professional_license = (request.professional_license != undefined && request.professional_license != "") ? request.professional_license : "";
        }
        service_provider.update_user(request.service_provider_id, complete_profiles, function (update_profile) {
            if (update_profile) {
                service_provider.add_business_location(request, function (response) {
                    if (response) {
                        service_provider.get_user_detail(request.service_provider_id, function (user_detail) {
                            if (user_detail != null) {
                                callback(user_detail, t('restapi_complete_profile_success'), '1');
                            } else {
                                callback(null, t('restapi_globals_error'), '0');
                            }
                        });
                    } else {
                        callback(null, t('restapi_globals_error'), '0')
                    }
                });
            } else {
                callback(null, t('restapi_globals_error'), '0')
            }
        });
    },

    /**
     * Add business location
     */
    add_business_location: function (request, callback) {
        if (request.business_location) {
            asyncloop(request.business_location, function (item, next) {
                if (item.name) {
                    con.query("SELECT short_code FROM tbl_country WHERE LOWER(name) = '" + item.country_name.toLowerCase() + "'", function (error1, country, fields) {
                        const business_location = {
                            name: item.name,
                            service_provider_id: request.service_provider_id,
                            latitude: item.latitude,
                            longitude: item.longitude,
                            timezone_code: geoFind(item.latitude, item.longitude)[0],
                            country_name: (item.country_name != undefined && item.country_name != "") ? item.country_name : '',
                            country_code: (!error1 && country[0] != undefined) ? country[0].short_code : 'US',
                            state_name: (item.state_name != undefined && item.state_name != "") ? item.state_name : '',
                            city_name: (item.city_name != undefined && item.city_name != "") ? item.city_name : '',
                            postal_code: (item.postal_code != undefined && item.postal_code != "") ? item.postal_code : '',
                            product_tax_percent: item.product_tax_percent || 0,
                            service_tax_percent: item.service_tax_percent || 0,
                        }
                        if (item.id != undefined && parseInt(item.id) > 0) {
                            var condition = " business_location_id = '" + item.id + "'";
                            common.delete_data('tbl_service_provider_available_slot', condition, function (slot_delete) {
                                common.delete_data('tbl_service_provider_block_date', condition, function (date_delete) {
                                    common.delete_data('tbl_service_provider_block_time_slot', condition, function (date_time_delete) {
                                        common.update_data_condition("tbl_business_location", "id = '" + item.id + "'", business_location, function (is_updated) {
                                            service_provider.add_service_provider_slot_time(item, item.id, request, function (slot) {
                                                next();
                                            });
                                        });
                                    });
                                });
                            });
                        } else {
                            business_location.status = 'Active';
                            business_location.is_deleted = '0';
                            business_location.insertdate = datetime.create().format('Y-m-d H:M:S');

                            common.single_insert_data('tbl_business_location', business_location, function (b_location) {
                                if (b_location) {
                                    if (item) {
                                        service_provider.add_service_provider_slot_time(item, b_location.insertId, request, function (slot) {
                                            next();
                                        });
                                    } else {
                                        next();
                                    }
                                } else {
                                    next();
                                }
                            });
                        }
                    });
                } else {
                    next();
                }
            }, function () {
                callback(true);
            });
        } else {
            callback(false);
        }
    },

    /**
     * add slot time
     * Possibly DEPR because savelocationavailability is possibly depr
     */
    add_service_provider_slot_time: function (slot, location_id, request, callback) {
        if (slot.slot_available != undefined && slot.slot_available != null && slot.slot_available.length > 0) {
            asyncloop(slot.slot_available, function (item2, next2) {
                if (item2.day) {
                    var slot_book = {
                        service_provider_id: request.service_provider_id,
                        business_location_id: location_id,
                        day: item2.day,
                        from_time: item2.from_time,
                        to_time: item2.to_time,
                        is_deleted: '0',
                        insertdate: datetime.create().format('Y-m-d H:M:S')
                    }
                    common.single_insert_data('tbl_service_provider_available_slot', slot_book, function (available) {
                        next2();
                    });
                } else {
                    next2();
                }
            }, function () {
                if (slot.block_availability != undefined && slot.block_availability != null && slot.block_availability.length > 0) {
                    asyncloop(slot.block_availability, function (item3, next3) {
                        if (item3.date) {
                            var slot_book = {
                                service_provider_id: request.service_provider_id,
                                business_location_id: location_id,
                                date: item3.date,
                                insertdate: datetime.create().format('Y-m-d H:M:S')
                            }
                            common.single_insert_data('tbl_service_provider_block_date', slot_book, function (block_slot) {
                                if (block_slot) {
                                    asyncloop(item3.booked_slot, function (item4, next4) {
                                        if (item4) {
                                            var slot_book = {
                                                service_provider_id: request.service_provider_id,
                                                business_location_id: location_id,
                                                block_date_id: block_slot.insertId,
                                                from_time: item4.from_time,
                                                to_time: item4.to_time,
                                                is_deleted: '0',
                                                insertdate: datetime.create().format('Y-m-d H:M:S')
                                            }
                                            common.single_insert_data('tbl_service_provider_block_time_slot', slot_book, function (block_slot_time) {
                                                next4();
                                            });
                                        } else {
                                            next4();
                                        }
                                    }, function () {
                                        next3()
                                    });
                                } else {
                                    next3()
                                }
                            });
                        } else {
                            next3();
                        }
                    }, function () {
                        callback(true);
                    });
                } else {
                    callback(true);
                }
            });
        } else {
            callback(true);
        }
    },

    /**
     * This function is used for calculate amount for target
     */
    calculate_amount_by_target: function (request, callback) {
        let week_days = 5;
        let month_days = 22;
        let yearly_days = 264;
        let hour = 8;
        /*if (request.target == 'Hourly') {
            var weekly_amount = week_days * hour * request.amount;
            var monthly_amount = month_days * hour * request.amount;
            var yearly_amount = yearly_days * hour * request.amount;
            callback(request.amount, weekly_amount, monthly_amount, yearly_amount)
        } else if (request.target == 'Weekly') {
            var hourly_amount = request.amount / (week_days * hour);
            var monthly_amount = month_days * hour * hourly_amount;
            var yearly_amount = yearly_days * hour * hourly_amount;
            callback(hourly_amount, request.amount, monthly_amount, yearly_amount)
        } else if (request.target == 'Monthly') {
            var hourly_amount = request.amount / (month_days * hour);
            var weekly_amount = week_days * hour * hourly_amount;
            var yearly_amount = yearly_days * hour * hourly_amount;
            callback(hourly_amount, weekly_amount, request.amount, yearly_amount)
        } else {
            var hourly_amount = request.amount / (yearly_days * hour);
            var weekly_amount = week_days * hour * hourly_amount;
            var monthly_amount = month_days * hour * hourly_amount;
            callback(hourly_amount, weekly_amount, monthly_amount, request.amount)
        }*/
        if (request.target == 'Hourly') {
            var daily_amount = request.amount * hour;
            var weekly_amount = daily_amount * week_days;
            var monthly_amount = daily_amount * month_days;
            var yearly_amount = daily_amount * yearly_days;
            callback(request.amount, weekly_amount, monthly_amount, yearly_amount);
        } else if (request.target == 'Weekly') {
            var daily_amount = request.amount / week_days;
            var hourly_amount = daily_amount / hour;
            var monthly_amount = daily_amount * month_days;
            var yearly_amount = daily_amount * yearly_days;
            callback(hourly_amount, request.amount, monthly_amount, yearly_amount);
        } else if (request.target == 'Monthly') {
            var daily_amount = request.amount / month_days;
            var hourly_amount = daily_amount / hour;
            var weekly_amount = daily_amount * week_days;
            var yearly_amount = daily_amount * yearly_days;
            callback(hourly_amount, weekly_amount, request.amount, yearly_amount);
        } else {
            var daily_amount = request.amount / yearly_days;
            var hourly_amount = daily_amount / hour;
            var weekly_amount = daily_amount * week_days;
            var monthly_amount = daily_amount * month_days;
            callback(hourly_amount, weekly_amount, monthly_amount, request.amount);
        }
    },

    /**
     * Function add set fincial target
     */
    set_fincial_target: function (request, callback) {
        service_provider.calculate_amount_by_target(request, function (hourly_amount, weekly_amount, monthly_amount, yearly_amount) {
            let data = {
                amount: request.amount,
                target: request.target,
                service_provider_id: request.service_provider_id,
                show_home_target: request.show_home_target,
                hourly_amount: parseFloat(hourly_amount).toFixed(2),
                weekly_amount: parseFloat(weekly_amount).toFixed(2),
                monthly_amount: parseFloat(monthly_amount).toFixed(2),
                yearly_amount: parseFloat(yearly_amount).toFixed(2),
                is_deleted: '0',
                insertdate: datetime.create().format('Y-m-d H:M:S')
            }
            var condition = " service_provider_id = '" + request.service_provider_id + "' AND is_deleted = '0' ";
            common.select_data('tbl_service_provider_target', condition, function (sp_detail) {
                if (sp_detail) {
                    common.update_data('tbl_service_provider_target', sp_detail[0].id, data, function (response) {
                        if (response) {
                            var calculate = {
                                hourly_amount: parseFloat(hourly_amount).toFixed(2),
                                weekly_amount: parseFloat(weekly_amount).toFixed(2),
                                monthly_amount: parseFloat(monthly_amount).toFixed(2),
                                yearly_amount: parseFloat(yearly_amount).toFixed(2),
                            }
                            callback('1', t('restapi_target_success'), calculate);
                        } else {
                            callback('0', t('restapi_globals_error'), null);
                        }
                    })
                } else {
                    common.single_insert_data('tbl_service_provider_target', data, function (response) {
                        if (response) {
                            var calculate = {
                                hourly_amount: parseFloat(hourly_amount).toFixed(2),
                                weekly_amount: parseFloat(weekly_amount).toFixed(2),
                                monthly_amount: parseFloat(monthly_amount).toFixed(2),
                                yearly_amount: parseFloat(yearly_amount).toFixed(2),
                            }
                            callback('1', t('restapi_target_success'), calculate);
                        } else {
                            callback('0', t('restapi_globals_error'), null);
                        }
                    });
                }
            })
        })
    },

    /**
     * Function for get financial target
     * @param {*} request
     * @param {*} callback
     */
    get_fincial_target: function (service_provider_id, callback) {
        //console.log(service_provider_id);
        var sql = con.query("SELECT id,amount,target,hourly_amount,weekly_amount,monthly_amount,yearly_amount,show_home_target from tbl_service_provider_target where service_provider_id = " + service_provider_id + "", function (err, result) {
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    callback('1', 'Financial target found successfully', result[0]);
                } else {
                    callback('0', 'Please set financial target', null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for add client
     */
    add_client: function (request, callback) {
        var email_phone = {
            email: request.email,
            phone: request.phone,
        }
        common.check_email_phone(email_phone, 'tbl_service_provider_client', function (result, message) {
            if (result) {
                var clientins = {
                    service_provider_id: request.service_provider_id,
                    profile_image: (request.profile_image != undefined && request.profile_image != "") ? request.profile_image : 'default-user.png',
                    customer_name: request.customer_name,
                    phone: request.phone,
                    country_code: request.country_code,
                    email: request.email,
                    is_user_app: 0,
                    notes: request.notes
                }
                common.single_insert_data('tbl_service_provider_client', clientins, function (is_client) {
                    if (is_client) {
                        callback('1', t('restapi_client_add_success'), true)
                    } else {
                        callback('0', t('restapi_globals_error'), null)
                    }
                })
            } else {
                callback('0', message, null)
            }
        })

    },

    all_client: function(request, callback) {
      const page = request.page - 1
      const limit = page * global.PER_PAGE
      const per_page = global.PER_PAGE
      let condition = ` service_provider_id = ${request.service_provider_id} AND NOT is_deleted `
      if (request.word) {
        condition += ` AND (customer_name LIKE '%${request.word}%') `
      }
      con.query(`
        SELECT
          id AS client_id,
          is_user_app AS user_id,
          CONCAT(
            UPPER(SUBSTRING(customer_name, 1, 1)), 
            LOWER(SUBSTRING(customer_name, 2))
          ) AS customer_name,
          CONCAT('${ global.S3_BUCKET_ROOT }${ global.USER_IMAGE }', profile_image) AS profile_image,
          country_code,
          phone,
          notes,
          email,
          avg_rating,
          GET_CLIENT_REVIEW_COUNT(c.id, NULL, NULL) AS review_count
        FROM tbl_service_provider_client c
        WHERE ${ condition }
        GROUP BY phone, email
        ORDER BY customer_name
        LIMIT ${ limit }, ${ per_page }
      `, function(err, result) {
        if (!err) {
          if (result[0]) {
            callback('1', t('restapi_client_found'), result)
          } else {
            callback('2', t('restapi_client_not_found'), null)
          }
        } else {
          callback('0', t('restapi_globals_error'), null)
        }
      })
    },

    get_client_detail: function (request, callback) {
        var sql = con.query("select *,avg_rating as avg_ratting,CONCAT('" + global.S3_BUCKET_ROOT + global.USER_IMAGE + "','',profile_image) as profile_image from tbl_service_provider_client where id = " + request.client_id + " AND is_deleted = '0'", function (err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    result[0].ratting = 0;
                    callback('1', 'Client detail found successfully', result[0]);
                } else {
                    callback('0', t('restapi_client_not_found'), null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function to get product category
     */
    product_category_list: function (request, callback) {
        var sql = con.query("select id,name,status,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',image_name) as category_image from tbl_product_category where is_deleted = '0' AND status = 'Active' ", function (err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback('1', t('restapi_productcateoryfound'), result);
                } else {
                    callback('0', t('restapi_cateorynotfound'), null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

  service_category_list: function(request, callback) {
    con.query(`
      SELECT 
        id,
        name,
        status,
        CONCAT('${ global.S3_BUCKET_ROOT }${ global.PRODUCT_IMAGE }', image_name) AS category_image
      FROM tbl_service_category
      WHERE NOT is_deleted
      AND status = 'Active'
    `, (err, result) => {
      if (!err) {
        if (result[0]) {
          callback('1', t('restapi_service_category_found'), result)
        } else {
          callback('0', t('restapi_service_category_notfound'), null)
        }
      } else {
        callback('0', t('restapi_globals_error'), null)
      }
    })
  },

    /**
     * Function for add stroy
     */
    add_story: function (request, callback) {
        const story = {
            service_provider_id: request.service_provider_id,
            story_name: request.story_name,
            is_deleted: '0',
            updatetime: datetime.create().format('Y-m-d H:M:S'),
            insertdate: datetime.create().format('Y-m-d H:M:S')
        }
        con.query("INSERT INTO tbl_story SET ? ", story, function (err, result) {
            if (!err) {
                if (result.insertId) {
                    if (request.story_image) {
                        asyncloop(request.story_image, function (item, next) {
                            if (item.image_name) {
                                var image = {
                                    story_id: result.insertId,
                                    image_name: item.image_name,
                                    is_deleted: '0',
                                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                                    insertdate: datetime.create().format('Y-m-d H:M:S')
                                }
                                var sql = con.query("INSERT INTO tbl_story_image SET ? ", image, function (err, result) {
                                    next();
                                })
                            } else {
                                next();
                            }
                        }, function () {
                            callback('1', t('restapi_story_addsuccess'), null);
                        })
                    } else {
                        callback('0', t('restapi_story_image'), null);
                    }
                } else {
                    callback('0', t('restapi_globals_error'), null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for edit story
     */
    edit_story: function (request, callback) {
        var story = {
            story_name: request.story_name,
            updatetime: datetime.create().format('Y-m-d H:M:S'),
        }
        var sql = con.query("UPDATE tbl_story SET ? where id = " + request.story_id + "", story, function (err, result) {
            if (!err) {
                if (request.story_image) {
                    var condition = " story_id = " + request.story_id + "";
                    common.delete_data('tbl_story_image', condition, function () {
                        asyncloop(request.story_image, function (item, next) {
                            if (item.image_name) {
                                var image = {
                                    story_id: request.story_id,
                                    image_name: item.image_name,
                                    is_deleted: '0',
                                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                                    insertdate: datetime.create().format('Y-m-d H:M:S')
                                }
                                var sql = con.query("INSERT INTO tbl_story_image SET ? ", image, function (err, result) {
                                    next();
                                })
                            } else {
                                next();
                            }
                        }, function () {
                            callback('1', t('restapi_story_editsuccess'), null);
                        })
                    });
                } else {
                    callback('0', t('restapi_story_image'), null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Delete story api
     */
    delete_story: function (request, callback) {
        let condition = " id = '" + request.story_id + "' ";
        common.delete_data('tbl_story', condition, function (story_deleted) {
            if (story_deleted) {
                let condition1 = " story_id = '" + request.story_id + "' ";
                common.delete_data('tbl_story_image', condition1, function (story_image_deleted) {
                    if (story_image_deleted) {
                        callback('1', t('restapi_story_deletesuccess'), true);
                    } else {
                        callback('0', t('restapi_globals_error'), false);
                    }
                })
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        })
    },

    /**
     * This function is used for get story detail
     * @param {*} request
     * @param {*} callback
     */
    get_story_detail: function (request, callback) {
        var sql = con.query("select * from tbl_story where id = " + request.story_id + " AND is_deleted = '0'", function (err, result) {
            if (result[0] != undefined && result[0] != "") {
                var sql = con.query("select id as story_image_id,CONCAT('" + global.S3_BUCKET_ROOT + global.STORY_IMAGE + "','',image_name) as image_name from tbl_story_image where story_id = '" + request.story_id + "'", function (err, result1) {
                    if (!err && result1[0] != "" && result1[0] != undefined) {
                        result[0].story_image = result1;
                        callback('1', t('restapi_storydetail_found'), result[0]);
                    } else {
                        callback('0', t('restapi_storydetail_not_found'), false);
                    }
                })
            } else if (err) {
                callback('0', t('restapi_storydetail_not_found'), false);
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        })
    },

    /**
     * Get story list api
     */
    story_list: function (service_provider_id, callback) {
        var sql = con.query("select id as story_id,story_name,service_provider_id from tbl_story where service_provider_id = '" + service_provider_id + "' AND is_deleted = '0' AND TIMESTAMPDIFF(MINUTE,insertdate, CURRENT_TIMESTAMP) <= 1440 ", function (err, result) {
            if (!err) {
                if (result[0] != "" && result[0] != "") {
                    asyncloop(result, function (item, next) {
                        if (item) {
                            var sql = con.query("select id as story_image_id,story_id,CONCAT('" + global.S3_BUCKET_ROOT + global.STORY_IMAGE + "','',image_name) as image_name from tbl_story_image where story_id = '" + item.story_id + "'", function (err, result1) {
                                if (err && result1[0] == undefined && result1[0] == "") {
                                    next();
                                } else {
                                    item.story_image = result1;
                                    next();
                                }
                            })
                        } else {
                            next();
                        }
                    }, function () {
                        callback('1', t('restapi_story_found'), result);
                    })
                } else {
                    callback('0', t('restapi_story_notfound'), []);
                }
            } else {
                callback('0', t('restapi_globals_error'), []);
            }
        })
    },

    /**
     * Function for ad product
     */
    add_product: function (request, callback) {
        var product = {
            product_name: request.product_name,
            description: (request.description != undefined && request.description != "") ? request.description : "",
            category_id: request.category_id,
            publish: request.publish,
            service_provider_id: request.service_provider_id,
            product_image: request.product_images[0].image_name,
            status: 'Active',
            is_deleted: '0',
            updatetime: datetime.create().format('Y-m-d H:M:S'),
            insertdate: datetime.create().format('Y-m-d H:M:S'),
        }
        var sql = con.query("INSERT INTO tbl_product SET ?", product, function (err, result) {
            if (!err && result != undefined && result != "") {
                request.product_images.shift();
                if (request.variant_list) {
                    asyncloop(request.variant_list, function (item, next) {
                        if (item) {
                            var sub_product = {
                                product_id: result.insertId,
                                size: item.size,
                                product_size_id: item.product_size_id,
                                quantity: item.quantity,
                                used_quantity: 0,
                                remaining_quantity: item.quantity,
                                price: item.price,
                                status: 'Active',
                                is_deleted: '0',
                                updatetime: datetime.create().format('Y-m-d H:M:S'),
                                insertdate: datetime.create().format('Y-m-d H:M:S'),
                            }
                            var sql = con.query("INSERT INTO tbl_subproduct SET ? ", sub_product, function (err, result) {
                                next();
                            })
                        } else {
                            next()
                        }
                    }, function () {
                        if (request.product_images) {
                            asyncloop(request.product_images, function (item2, next2) {
                                if (item2) {
                                    var product_image = {
                                        image_name: item2.image_name,
                                        type_id: result.insertId,
                                        type: 'product',
                                        is_deleted: '0',
                                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                                    }
                                    var query = con.query("INSERT INTO tbl_image SET ? ", product_image, function (err, result) {
                                        //console.log(err);
                                        next2();
                                    })
                                } else {
                                    next2()
                                }
                            }, function () {
                                callback('1', t('restapi_product_add_success'), true)
                            })
                        } else {
                            callback('1', t('restapi_product_add_success'), true)
                        }
                    })
                } else {
                    callback('0', t('restapi_globals_error'), false);
                }
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        })
    },

    /**
     * Function for edit product
     */
    edit_product: function (request, callback) {
        var product = {
            product_name: request.product_name,
            description: (request.description != undefined && request.description != "") ? request.description : "",
            category_id: request.category_id,
            publish: request.publish,
            // service_provider_id: request.service_provider_id,
            product_image: request.product_images[0].image_name,
            status: 'Active',
            is_deleted: '0',
            updatetime: datetime.create().format('Y-m-d H:M:S'),
        }
        con.query("UPDATE tbl_product SET ? where id = '" + request.product_id + "' ", product, function (err, result) {
            if (!err && result != undefined && result != "") {
                request.product_images.shift();
                if (request.variant_list) {
                    var condition = " product_id = " + request.product_id + " ";
                    asyncloop(request.variant_list, function (item, next) {
                        if (item) {
                            var sub_product = {
                                product_id: request.product_id,
                                size: item.size,
                                product_size_id: item.product_size_id,
                                quantity: item.quantity,
                                used_quantity: 0,
                                remaining_quantity: item.quantity,
                                price: item.price,
                                status: 'Active',
                                is_deleted: '0',
                                updatetime: datetime.create().format('Y-m-d H:M:S'),
                            }
                            if (item.subproduct_id != undefined && item.subproduct_id != "" && parseInt(item.subproduct_id) > 0) {
                                con.query("UPDATE tbl_subproduct SET ? where id = '" + item.subproduct_id + "' ", sub_product, function (err, result) {
                                    next();
                                });
                            } else {
                                con.query("INSERT INTO tbl_subproduct SET ? ", sub_product, function (err, result) {
                                    next();
                                });
                            }
                        } else {
                            next()
                        }
                    }, function () {
                        if (request.product_images) {
                            var condition = " type_id = " + request.product_id + " AND type = 'product' ";
                            common.delete_data("tbl_image", condition, function (delete_image) {
                                asyncloop(request.product_images, function (item2, next2) {
                                    if (item2) {
                                        var product_image = {
                                            image_name: item2.image_name,
                                            type_id: request.product_id,
                                            type: 'product',
                                            is_deleted: '0',
                                            insertdate: datetime.create().format('Y-m-d H:M:S'),
                                        }
                                        con.query("INSERT INTO tbl_image SET ? ", product_image, function (err, result) {
                                            next2();
                                        })
                                    } else {
                                        next2()
                                    }
                                }, function () {
                                    if (request.remove_variant_ids != undefined && request.remove_variant_ids != "") {
                                        var remove_variant_ids = request.remove_variant_ids.split(",");
                                        asyncloop(remove_variant_ids, function (item, next) {
                                            con.query("UPDATE tbl_subproduct SET ? where id = '" + item + "' ", { is_deleted: '1', updatetime: datetime.create().format('Y-m-d H:M:S') }, function (err, result) {
                                                next();
                                            });
                                        }, function () {
                                            callback('1', t('restapi_product_edit_success'), true)
                                        });
                                    } else {
                                        callback('1', t('restapi_product_edit_success'), true)
                                    }
                                });
                            });
                        } else {
                            if (request.remove_variant_ids != undefined && request.remove_variant_ids != "") {
                                var remove_variant_ids = request.remove_variant_ids.split(",");
                                asyncloop(remove_variant_ids, function (item, next) {
                                    con.query("UPDATE tbl_subproduct SET ? where id = '" + item + "' ", { is_deleted: '1', updatetime: datetime.create().format('Y-m-d H:M:S') }, function (err, result) {
                                        next();
                                    });
                                }, function () {
                                    callback('1', t('restapi_product_edit_success'), true)
                                });
                            } else {
                                callback('1', t('restapi_product_edit_success'), true)
                            }
                        }
                    });
                } else {
                    callback('0', t('restapi_globals_error'), false);
                }
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        });
    },

    /**
     * Function for delete product
     */
    delete_product: function (request, callback) {
        var product = {
            is_deleted: '1'
        }
        var sql = con.query("UPDATE tbl_product SET ? where id = '" + request.product_id + "'", product, function (err, result) {
            if (!err) {
                var sql = con.query("UPDATE tbl_subproduct SET ? where product_id = '" + request.product_id + "'", product, function (err1, result1) {
                    if (!err1) {
                        callback('1', t('restapi_product_deleted'), true);
                    } else {
                        callback('0', t('restapi_globals_error'), false);
                    }
                })
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        })
    },

    /**
     * Function for get product detail
     */
    product_detail: function (request, responce) {
        var query = con.query("select tp.*,GET_SERVICE_PROVIDER_RATING(tp.service_provider_id, NULL) AS ratting,GET_SERVICE_PROVIDER_REVIEW_COUNT(tp.service_provider_id, NULL) AS review,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',tp.product_image) as product_image,tp.product_image as main_product_image,tp.id as product_id,c.name as category_name from tbl_product tp LEFT JOIN tbl_product_category c ON c.id = tp.category_id where tp.id = " + request.product_id + " AND tp.is_deleted = '0' AND c.is_deleted = '0'", function (err, result) {
            //console.log(query.sql);
            //console.log('errerr', err);
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    result[0].ratting = result[0].ratting;
                    result[0].review = result[0].review;
                    service_provider.multiple_productimage(result[0].product_id, function (image) {
                        // var image = [];
                        if (image != null) {
                            image.push({ multiple_product_image: result[0].product_image });
                            result[0].image = image.reverse();

                        } else {
                            result[0].image = [{ multiple_product_image: result[0].product_image }];
                        }
                        service_provider.sub_product(request.product_id, function (quantities) {
                            //console.log("quantities", quantities);
                            if (quantities != null) {
                                result[0].variant_list = quantities;
                                responce(result[0], t('restapi_productfound'), '1');

                            } else {
                                responce(null, t('restapi_quantitynotfound'), '0');
                            }
                        });

                    });

                } else {
                    responce(null, t('restapi_productnotfound'), '0');
                }
            } else {
                responce(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /*
     * get multiple product image
     */
    multiple_productimage(product_id, responce) {
        var query = con.query("select CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',pi.image_name) as multiple_product_image from tbl_image pi where type_id = " + product_id + " AND type = 'product' AND is_deleted = '0'", function (err, result) {
            if (!err && result[0] != undefined) {
                responce(result);
            } else {
                responce([]);
            }
        })
    },

    /**
     * Function for get multiple service image
     * @param {*} service_id
     * @param {*} callback
     */
    multiple_serviceimage(service_id, responce) {
        var query = con.query("select CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',pi.image_name) as multiple_service_image from tbl_image pi where type_id = " + service_id + " AND type = 'service' AND is_deleted = '0'", function (err, result) {
            if (!err && result[0] != undefined) {
                responce(result);
            } else {
                responce(null);
            }
        })
    },

    /**
     * Get subproduct
     */
    sub_product: function (product_id, callback) {
        var query = con.query("select tq.*,tq.id as subproduct_id from tbl_subproduct tq where tq.product_id = " + product_id + " AND tq.is_deleted = '0'", function (err, result) {
            if (!err && result[0] != undefined) {
                callback(result)
            } else {
                callback(null);
            }
        })
    },

    /**
     * Function for all product
     */
    all_product: function (request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        var condition = " p.service_provider_id = " + request.service_provider_id + "  AND p.is_deleted = '0'";
        if(request.publish != undefined && request.publish != "" && request.publish != "All"){
            condition += " AND p.publish = 'Yes'";
        }
        if (request.category_id) {
            condition += " AND p.category_id = " + request.category_id + " ";
        }
        var sql = con.query("select *,id as product_id,GET_SERVICE_PROVIDER_RATING(p.service_provider_id, NULL) AS ratting,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',product_image) as product_image from tbl_product p where " + condition + " ORDER BY product_id DESC LIMIT " + limit + ", " + per_page + " ", function (err, result) {
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    asyncloop(result, function (item, next) {
                        if (item) {
                            service_provider.sub_product(item.product_id, function (price) {
                                if (price) {
                                    item.subproduct_id = price[0].subproduct_id;
                                    item.ratting = 0;
                                    item.price = price[0].price;
                                    item.remaining_quantity = price[0].remaining_quantity;
                                } else {
                                    item.subproduct_id = 0;
                                    item.ratting = 0;
                                    item.price = 0;
                                    item.remaining_quantity = 0;
                                }
                                if (request.user_id) {
                                    con.query("select * from tbl_product_bookmark where type = 'product' AND user_id = '" + request.user_id + "' AND type_id = '" + item.product_id + "'", function (error1, results1) {
                                        if (!error1 && results1[0] != undefined && results1[0] != "") {
                                            item.bookmark_status = results1[0].status;
                                            next();
                                        } else {
                                            item.bookmark_status = 0;
                                            next();
                                        }
                                    })
                                } else {
                                    next();
                                }
                            })
                        } else {
                            next()
                        }
                    }, function () {
                        callback(result, t('restapi_productfound'), '1');
                    })
                } else {
                    callback(null, t('restapi_productnotfound'), '2');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * Add services
     */
    add_service: function (request, callback) {
        var product = {
            service_name: request.service_name,
            description: request.description,
            category_id: request.category_id,
            price: request.price,
            duration: request.duration,
            service_image: request.service_images[0].image_name,
            status: 'Active',
            is_deleted: '0',
            service_provider_id: request.service_provider_id,
            updatetime: datetime.create().format('Y-m-d H:M:S'),
            insertdate: datetime.create().format('Y-m-d H:M:S'),
        }
        var sql = con.query("INSERT INTO tbl_service SET ?", product, function (err, result) {
            if (!err && result != undefined && result != "") {
                request.service_images.shift();
                if (request.service_images) {
                    asyncloop(request.service_images, function (item, next) {
                        if (item) {
                            var product_image = {
                                image_name: item.image_name,
                                type_id: result.insertId,
                                type: 'service',
                                is_deleted: '0',
                                insertdate: datetime.create().format('Y-m-d H:M:S'),
                            }
                            var query = con.query("INSERT INTO tbl_image SET ? ", product_image, function (err, result) {
                                next();
                            })
                        } else {
                            next()
                        }
                    }, function () {
                        callback('1', t('restapi_service_add_success'), true)
                    })
                } else {
                    callback('1', t('restapi_service_add_success'), false);
                }
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        })
    },

    /**
     * Function for edit service
     */
    edit_service: function (request, callback) {
        var service = {
            service_name: request.service_name,
            description: request.description,
            category_id: request.category_id,
            price: request.price,
            duration: request.duration,
            status: 'Active',
            is_deleted: '0',
            service_provider_id: request.service_provider_id,
            updatetime: datetime.create().format('Y-m-d H:M:S'),
        }
        if (request.service_images) {
            service['service_image'] = request.service_images[0].image_name;
        }
        var sql = con.query("UPDATE tbl_service SET ? where id = '" + request.service_id + "' ", service, function (err, result) {
            if (!err && result != undefined && result != "") {
                request.service_images.shift();
                if (request.service_images) {
                    var condition = " type_id = " + request.service_id + " AND type = 'service' ";
                    common.delete_data('tbl_image', condition, function (delete_image) {
                        if (delete_image) {
                            asyncloop(request.service_images, function (item, next) {
                                if (item) {
                                    var product_image = {
                                        image_name: item.image_name,
                                        type_id: request.service_id,
                                        type: 'service',
                                        is_deleted: '0',
                                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                                    }
                                    var query = con.query("INSERT INTO tbl_image SET ? ", product_image, function (err, result) {
                                        next();
                                    })
                                } else {
                                    next()
                                }
                            }, function () {
                                callback('1', t('restapi_service_edit_success'), true)
                            })
                        } else {
                            callback('0', t('restapi_globals_error'), false);
                        }
                    })
                } else {
                    callback('1', t('restapi_service_edit_success'), false);
                }
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        })
    },

    /**
     * Function for delete service
     */
    delete_service: function (request, callback) {
        var service = {
            is_deleted: '1'
        }
        var sql = con.query("UPDATE tbl_service SET ? where id = '" + request.service_id + "'", service, function (err, result) {
            if (!err) {
                callback('1', t('restapi_service_deleted'), true);
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        })
    },

    /**
     * Function for service detail
     */
    service_detail: function (request, callback) {
        var query = con.query("select ts.*,GET_SERVICE_PROVIDER_RATING(ts.service_provider_id, NULL) AS ratting,GET_SERVICE_PROVIDER_REVIEW_COUNT(ts.service_provider_id, NULL) AS review,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',ts.service_image) as service_image,ts.service_image as main_service_image,ts.id as service_id,c.name as category_name from tbl_service ts LEFT JOIN tbl_service_category c ON c.id = ts.category_id where ts.id = " + request.service_id + " AND ts.is_deleted = '0' AND c.is_deleted = '0'", function (err, result) {
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    result[0].ratting = result[0].ratting;
                    result[0].review = result[0].review;
                    service_provider.multiple_serviceimage(result[0].service_id, function (image) {
                        if (image != null) {
                            image.push({ multiple_service_image: result[0].service_image });
                            result[0].image = image.reverse();
                        } else {
                            result[0].image = [{ multiple_service_image: result[0].service_image }];
                        }
                        callback(result[0], t('restapi_service_detail_found'), '1');
                    });
                } else {
                    callback(null, t('restapi_service_detail_notfound'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * Fnuction for all service
     */
    all_service: function (request, callback) {
        var select = "*,GET_SERVICE_PROVIDER_RATING(s.service_provider_id, NULL) AS ratting,GET_SERVICE_PROVIDER_REVIEW_COUNT(s.service_provider_id, NULL) AS review,id as service_id,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',service_image) as service_image";
        var condition = " service_provider_id = " + request.service_provider_id + " AND is_deleted = '0'";
        if (request.category_id) {
            condition += " AND category_id = " + request.category_id + "";
        }
        if (request.user_id) {
            select += ",IFNULL((SELECT b.status FROM tbl_product_bookmark b WHERE b.type_id = s.id AND b.type = 'service' AND b.user_id = " + request.user_id + " ),0) AS bookmark_status";
        }
        var sql = con.query("select " + select + " from tbl_service s where " + condition + " ORDER BY ID DESC", function (err, result) {
            //console.log(this.sql);
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    callback(result, t('restapi_servicefound'), '1');
                } else {
                    callback([], t('restapi_service_category_notfound'), '2');
                }
            } else {
                callback([], t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * Function for create post
     */
    create_post: function (request, callback) {
        var post = {
            service_provider_id: request.service_provider_id,
            caption: request.caption,
            description: request.description,
            post_images: request.post_images[0].image_name,
            is_deleted: '0',
            updatetime: datetime.create().format('Y-m-d H:M:S'),
            insertdate: datetime.create().format('Y-m-d H:M:S'),
        }
        var sql = con.query("INSERT INTO tbl_post SET ? ", post, function (err, result) {
            if (!err && result != "") {
                request.post_images.shift();
                if (request.post_images) {
                    asyncloop(request.post_images, function (item, next) {
                        if (item) {
                            var product_image = {
                                image_name: item.image_name,
                                type_id: result.insertId,
                                type: 'post',
                                is_deleted: '0',
                                insertdate: datetime.create().format('Y-m-d H:M:S'),
                            }
                            var query = con.query("INSERT INTO tbl_image SET ? ", product_image, function (err, result) {
                                //console.log(err);
                                next();
                            })
                        } else {
                            next();
                        }
                    }, function () {
                        callback(true, t('restapi_post_success'), '1');
                    })
                } else {
                    callback(true, t('restapi_post_success'), '1');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * Function fot get all post
     */
    post_list: function (request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;

        var sql = con.query("select id as post_id,service_provider_id,caption,description,(select count(id) from tbl_like l where is_deleted = '0' AND l.post_id = p.id) as total_like,(select count(id) from tbl_comment c where is_deleted = '0' AND c.post_id = p.id) as total_comment,CONCAT('" + global.S3_BUCKET_ROOT + global.POST_IMAGE + "','',post_images) as post_image,IFNULL((SELECT 1 FROM tbl_like l WHERE l.user_id = " + request.service_provider_id + " AND type = 'service_provider' AND l.post_id = p.id ORDER BY ID DESC LIMIT 1),0) AS is_like,CONCAT('"+global.base_url_without_api+"share-feed/',TO_BASE64(p.id)) as sharefeed_url from tbl_post p where is_deleted = '0' AND service_provider_id = " + request.service_provider_id + " ORDER BY ID DESC LIMIT " + limit + ", " + per_page + "", function (err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback(result, t('restapi_post_found'), '1');
                } else {
                    callback([], t('restapi_post_notfound'), '2');
                }
            } else {
                callback([], t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * Function for get post detail
     */
    post_detail: function (request, callback) {
        var sql = con.query("select id post_id,service_provider_id,caption,description,CONCAT('" + global.S3_BUCKET_ROOT + global.POST_IMAGE + "','',post_images) as post_image,IFNULL((SELECT 1 FROM tbl_like l WHERE l.user_id = " + request.service_provider_id + " AND type = 'service_provider' AND l.post_id = p.id ORDER BY ID DESC LIMIT 1),0) AS is_like,CONCAT('"+global.base_url_without_api+"share-feed/',TO_BASE64(p.id)) as sharefeed_url from tbl_post p where id = " + request.post_id + " AND is_deleted = '0' ", function (err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    service_provider.multiple_postimage(result[0].post_id, function (post_image) {
                        if (post_image != null) {
                            post_image.push({ post_image: result[0].post_image });
                            result[0].image = post_image.reverse();
                        } else {
                            result[0].image = { post_image: result[0].post_image };
                        }
                        service_provider.like_count(result[0].post_id, function (total_like) {
                            result[0].total_like = total_like;
                            service_provider.comment_count(result[0].post_id, function (total_comment) {
                                result[0].total_comment = total_comment;
                                // result[0].comment = comment;
                                callback(result[0], t('restapi_post_found'), '1');
                            });
                        })
                    })
                } else {
                    callback(null, t('restapi_post_notfound'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * Function for get post image
     */
    multiple_postimage(post_id, responce) {
        var query = con.query("select CONCAT('" + global.S3_BUCKET_ROOT + global.POST_IMAGE + "','',pi.image_name) as post_image from tbl_image pi where type_id = " + post_id + " AND type = 'post' AND is_deleted = '0'", function (err, result) {
            if (!err && result[0] != undefined) {
                responce(result);
            } else {
                responce([]);
            }
        })
    },

    /**
     * Function for count like and comment
     */
    like_count: function (post_id, callback) {
        var sql = con.query("select COUNT(id) as total_like from tbl_like where post_id = " + post_id + "", function (err, result) {
            if (!err && result[0] != undefined) {
                callback(result[0].total_like);
            } else {
                callback(0);
            }
        })
    },

    /**
     * Function for count post comments
     */
    comment_count: function (post_id, callback) {
        var sql = con.query("SELECT COUNT(id) as comment_count from tbl_comment where post_id = " + post_id + " AND is_deleted = '0' ", function (err, result) {
            if (!err && result[0] != undefined) {
                callback(result[0].comment_count);
            } else {
                callback(0);
            }
        })
    },

    /**
     * Function for delete post
     */
    delete_post: function (request, callback) {
        var post_delete = {
            is_deleted: '1',
        }
        con.query("UPDATE tbl_post SET ? where id = " + request.post_id + "", post_delete, function (err, result) {
            if (!err && result != "") {
                callback('1', t('restapi_post_delete_success'), true);
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        })
    },

    /**
     * Function for get comments of post
     */
    get_post_comments: function (request, callback) {
      const page = request.page - 1
      const limit = page * global.PER_PAGE
      const per_page = global.PER_PAGE
      con.query(`
        SELECT
          c.id,
          c.post_id,
          c.comment,
          c.user_id,
          DATE_FORMAT(c.insertdate, '%Y-%m-%d %H:%i:%s') AS insert_date,
          IF(
            c.type = 'service_provider',
            CONCAT(s.first_name, ' ', s.last_name),
            CONCAT(u.first_name, ' ', u.last_name)
          ) AS user_name,
          IF(
            c.type ='service_provider',
            CONCAT('${global.S3_BUCKET_ROOT}${global.SP_IMAGE}', s.profile_image),
            CONCAT('${global.S3_BUCKET_ROOT}${global.USER_IMAGE}', u.profile_image)
          ) AS user_image,
          (
            SELECT COUNT(id)
            FROM comment_likes
            WHERE commentId = c.id
          ) AS likes_count,
          EXISTS(
            SELECT id 
            FROM comment_likes
            WHERE userId = '${request.service_provider_uuid}'
            AND commentId = c.id
          ) AS liked
        FROM tbl_comment c
        LEFT JOIN tbl_user u ON u.id = c.user_id
          AND c.type = 'user'
        LEFT JOIN tbl_service_provider s ON s.id = c.user_id
          AND c.type = 'service_provider'
        WHERE post_id = ${request.post_id}
        AND c.is_deleted = '0'
        ORDER BY ID DESC
        LIMIT ${limit}, ${per_page}
      `, function (err, result) {
        if (!err) {
          if (result[0]) {
            callback(result, 'Comment list found successfully', '1');
          } else {
            callback([], "Comment list not found", '2');
          }
        } else {
          logger.error({
            message: 'Failed to retrieve a post comments',
            meta: { ...request },
            error: err,
          })
          callback([], t('restapi_globals_error'), '0');
        }
      })
    },

    /**
     * Function for get business location
     * oct 3
     */
    get_business_location: function (service_id, callback) {
        var sql = con.query("select * from tbl_business_location where service_provider_id = " + service_id + " AND is_deleted = '0'", function (err, result) {
            if (!err && result[0] != "" && result[0] != undefined) {
                asyncloop(result, function (item, next) {
                    if (item) {
                        service_provider.available_slot(item.service_provider_id, item.id, function (available_slot) {
                            item.slot_available = available_slot?.map(it => ({
                                ...it,
                                from_time: it.from_time ? moment(`1990-10-10 ${it.from_time}`, SIMPLE_DATE_FORMAT_WITH_12HR_TIME).format('h:mm A') : '',
                                to_time: it.to_time ? moment(`1990-10-10 ${it.to_time}`, SIMPLE_DATE_FORMAT_WITH_12HR_TIME).format('h:mm A') : ''
                            }));
                            service_provider.block_date(item.service_provider_id, item.id, function (block_date) {
                                item.block_date = block_date;
                                next();
                            })
                        })
                    } else {
                        next()
                    }
                }, function () {
                    callback(result)
                })
            } else {
                callback([])
            }
        })
    },

    /**
     * Function for get availability
     */
    available_slot: function (service_provider_id, business_location_id, callback) {
        var query = con.query("select *,id as business_location_id from tbl_service_provider_available_slot where service_provider_id = " + service_provider_id + " AND business_location_id = " + business_location_id + "", function (err, result) {
            //console.log("error", err);
            if (!err && result[0] != "") {
                callback(result)
            } else {
                callback([])
            }
        })
    },

    /**
     * Function for get block date
     */
    block_date: function (service_provider_id, business_location_id, callback) {
        var query = con.query("select id as block_date_id,date_format(date,'%Y-%m-%d') as date from tbl_service_provider_block_date bd where service_provider_id = " + service_provider_id + " AND business_location_id = " + business_location_id + "", function (err, result) {
            if (!err && result[0] != "" && result[0] != undefined) {
                asyncloop(result, function (item, next) {
                    var sql = con.query("select id block_time_id,from_time,to_time from tbl_service_provider_block_time_slot where block_date_id = " + item.block_date_id + "", function (err1, result1) {
                        if (!err1 && result[0] != undefined && result[0] != "") {
                            item.booked_slot = result1;
                        } else {
                            item.booked_slot = [];
                        }
                        next()
                    })
                }, function () {
                    callback(result)
                })
            } else {
                callback([])
            }
        })
    },

    /**
     * Function for create occational promo
     */
    create_occational_promo: function (request, callback) {
      let query = `
        SELECT *
        FROM tbl_occational_promo
        WHERE promocode = '${request.promocode}'
          AND is_deleted = '0'
      `
      var sql = con.query(query, function (err, result) {
          if (!err && result.length > 0) {
              return callback(null, 'Please add another promocode.This promocode already in use', '0');
          }
          con.query("SELECT * FROM tbl_promocode where promocode = '" + request.promocode + "' AND is_deleted = '0'", function (err1, result1) {
              if (!err1 && result1.length > 0) {
                  return callback(null, 'Please add another promocode.This promocode already in use', '0');
              }

              var occationl_promo = {
                  image_name: request.occational_promo_image,
                  discount_amount: request.discount_amount,
                  discount_type: request.discount_type,
                  max_discount_amount: (request.discount_type == 'Percentage') ? request.max_discount_amount : request.discount_amount,
                  description: request.description,
                  promocode: request.promocode,
                  valid_till: request.valid_till,
                  service_provider_id: request.service_provider_id,
                  applied_on: (request.applied_on != undefined && request.applied_on != "") ? request.applied_on : "",
                  applied_service_on: (request.applied_service_on != undefined && request.applied_service_on != "") ? request.applied_service_on : "",
              }
              if (request.title) {
                  occationl_promo.title = request.title;
              }

              common.single_insert_data('tbl_occational_promo', occationl_promo, function (promo_add) {
                  if (promo_add) {
                      if (request.client_list != undefined && request.client_list.length > 0) {
                          var clients = [];
                          asyncloop(request.client_list, function (item, next) {
                              var message = {
                                  sender_id: request.service_provider_id,
                                  sender_type: USER_TYPE.SERVICE_PROVIDER,
                                  receiver_id: item.user_id,
                                  receiver_type: USER_TYPE.USER,
                                  type: 'user',
                                  message: `You have received a new Promo Code!`,
                                  title: 'New Promo Code received',
                                  isaction_id: promo_add.insertId,
                                  tag: 'newoccationalpromo',
                              }
                              require('../../../config/common').prepare_customer_notification(item.user_id, message, function (notification) {
                                  var temp = [
                                      promo_add.insertId, item.client_id, item.user_id, moment().utc().format("Y-m-d H:i:s")
                                  ];
                                  clients.push(temp);
                                  next();
                              });
                          }, function () {
                              var query = con.query("INSERT INTO tbl_occational_promo_clients(promo_id,client_id,user_id,inserted_datetime) VALUES ?", [clients], function (err2, result1, fields) {
                                  callback(true, t('restapi_occational_promo_success'), '1');
                              });
                          })
                      } else {
                          callback(true, t('restapi_occational_promo_success'), '1');
                      }
                  } else {
                      callback(null, t('restapi_globals_error'), '0');
                  }
              })
          });
      });
    },

    /**
     * Function for get occational promo
     */
    get_occational_promo: function (request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        var sql = con.query("select *,id as occational_promo_id,CONCAT('" + global.S3_BUCKET_ROOT + global.POST_IMAGE + "','',image_name) as occational_promo_image from tbl_occational_promo where service_provider_id = " + request.service_provider_id + " AND is_deleted = '0' AND is_clientspecific_promo = '0' ORDER BY id DESC LIMIT " + limit + ", " + per_page + "", function (err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback(result, t('restapi_occational_promo_found'), '1');
                } else {
                    callback(null, t('restapi_occational_promo_not_found'), '2');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * Function for delete occational promo
     */
    delete_occational_promo: function (request, callback) {
        var updparam = {
            is_deleted: '1'
        }
        common.update_data('tbl_occational_promo', request.occational_promo_id, updparam, function (delete_promo) {
            if (delete_promo) {
                callback(true, t('restapi_occational_promo_delete'), '1');
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * Function for occational promo detail
     */
    occational_promo_detail: function (request, callback) {
        var sql = con.query("select *,id as occational_promo_id,CONCAT('" + global.S3_BUCKET_ROOT + global.POST_IMAGE + "','',image_name) as occational_promo_image from tbl_occational_promo where id = " + request.occational_promo_id + "", function (err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback(result[0], t('restapi_occational_promo_found'), '1');
                } else {
                    callback(null, t('restapi_occational_promo_not_found'), '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * Function for create client specific promo
     */
    create_clientspecific_promo: function (request, callback) {
        let query = `
          SELECT promo.*, sp.first_name, sp.last_name
          FROM tbl_occational_promo as promo
          JOIN tbl_service_provider sp ON sp.id = promo.service_provider_id
          WHERE promocode = '${request.promocode}' AND promo.is_deleted = '0';
        `
        con.query(query, function (err, result) {
            if (!err && result.length > 0) {
                return callback(null, 'Please add another promocode.This promocode already in use', '0');
            } else {
                con.query("SELECT * FROM tbl_promocode where promocode = '" + request.promocode + "' AND is_deleted = '0'", function (err1, result1) {
                    if (!err1 && result1.length > 0) {
                        callback(null, 'Please add another promocode.This promocode already in use', '0');
                    } else {
                        var occationl_promo = {
                            image_name: request.promo_image,
                            discount_amount: request.discount_amount,
                            discount_type: request.discount_type,
                            max_discount_amount: (request.discount_type == 'Percentage') ? request.max_discount_amount : request.discount_amount,
                            description: "",
                            promocode: request.promocode,
                            valid_till: request.valid_till,
                            service_provider_id: request.service_provider_id,
                            is_clientspecific_promo: '1',
                        }
                        common.single_insert_data('tbl_occational_promo', occationl_promo, function (promo_add) {
                            if (promo_add) {
                                if (request.client_list != undefined && request.client_list.length > 0) {
                                    const sp_full_name = `${request.first_name || ''} ${request.last_name || ''}`.trim()
                                    var clients = [];
                                    asyncloop(request.client_list, function (item, next) {
                                        var message = {
                                            sender_id: request.service_provider_id,
                                            sender_type: USER_TYPE.SERVICE_PROVIDER,
                                            receiver_id: item.user_id,
                                            receiver_type: USER_TYPE.USER,
                                            type: 'user',
                                            message: `You have received a special offer from ${sp_full_name}!`,
                                            title: 'New Promo Code received',
                                            isaction_id: promo_add.insertId,
                                            tag: 'newclientspecificpromo',
                                        }
                                        require('../../../config/common').prepare_customer_notification(item.user_id, message, function (notification) {
                                            var temp = [
                                                promo_add.insertId, item.client_id, item.user_id, moment().utc().format("Y-m-d H:i:s")
                                            ];
                                            clients.push(temp);
                                            next();
                                        });
                                    }, function () {
                                        var query = con.query("INSERT INTO tbl_occational_promo_clients(promo_id,client_id,user_id,inserted_datetime) VALUES ?", [clients], function (err2, result1, fields) {
                                            callback(true, t('restapi_occational_promo_success'), '1');
                                        });
                                    })
                                } else {
                                    callback(true, t('restapi_occational_promo_success'), '1');
                                }
                            } else {
                                callback(null, t('restapi_globals_error'), '0');
                            }
                        })
                    }
                });
            }
        });
    },

    /**
     * Function for get client specific promo
     */
    get_clientspecific_promo: function (request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;
        var sql = con.query("select *,id as occational_promo_id,CONCAT('" + global.S3_BUCKET_ROOT + global.POST_IMAGE + "','',image_name) as occational_promo_image from tbl_occational_promo where service_provider_id = " + request.service_provider_id + " AND is_deleted = '0' AND is_clientspecific_promo = '1' ORDER BY id DESC LIMIT " + limit + ", " + per_page + "", function (err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    callback(result, t('restapi_occational_promo_found'), '1');
                } else {
                    callback(null, t('restapi_occational_promo_not_found'), '2');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },


    /*
     * param start: <moment object> start time
     * param end: <moment object> end time
     * param duration: <string> duration of a single slot
    */
    createTimeslots: (start, end, duration) => {
        console.log(`::createTimeslots for ${start} to ${end} by ${duration} minutes`)
        const timeSlots = []
        while(start < end) {
            if (start.format("YYYY-MM-DD") != end.format("YYYY-MM-DD")) {
                return timeSlots
            }
            // create new moment object because adding "start" is by reference
            timeSlots.push(moment(start))
            start = start.add(duration, "m")
        }
        return timeSlots
    }, // createTimeslots


    get_available_slot: async (request, callback) => {
        const query = `SELECT from_time, to_time, day
            FROM tbl_service_provider_available_slot
            WHERE service_provider_id = ${request.service_provider_id}
                AND business_location_id = ${request.business_location_id}
                AND is_deleted = '0'
                AND from_time > '00:00'
                AND to_time >= '00:00'
                AND from_time != ''
                AND to_time != ''
            HAVING day = DAYNAME('${request.date}')`;
        con.query(
            query,
            [request.service_provider_id, request.business_location_id],
            (err, result) =>
        {
            console.log(result)
            if (err) {
                console.error(`get_available_slot:: tbl_service_provider_available_slot error: \n${err}`)
                callback([], t('restapi_globals_error'), '0')
                return false;
            }
            if (result[0] == undefined || result[0] == "") {
                console.error('get_available_slot:: tbl_service_provider_available_slot response is empty.')
                callback([], t('restapi_day_not_available'), '0')
                return false;
            }

            const slotfrom_time = momentTimezone
                                .utc(`${request.date} ${result[0].from_time}`, 'YYYY-MM-DD HH:mm:ss')
                                .tz(request.timezone)
            const slotto_time = momentTimezone
                                .utc(`${request.date} ${result[0].to_time}`, 'YYYY-MM-DD HH:mm:ss')
                                .tz(request.timezone)

            const total_duration = (request.total_duration != undefined && request.total_duration != "") ? request.total_duration : 30;
            let timeslots = service_provider.createTimeslots(slotfrom_time, slotto_time, total_duration)
            console.log(timeslots)
            if (timeslots[0] == undefined) {
                callback([], t('restapi_slot_notfound'), '2')
            }

            const booking_query = `SELECT booking_id, service_provider_id, user_id, date, slot_time, end_datetime, booking_status
            FROM blookddb.tbl_appointment_booking
            where service_provider_id = ${request.service_provider_id}
                and business_location_id = ${request.business_location_id}
                and date = '${request.date}';`
            con.query(booking_query, [], (err, bookings) => {
                bookings.forEach((item) => {
                    if (timeslots.length === 0)
                        return;

                    console.log('---- booking ---')
                    console.log(item)
                    console.log(`Available Time Range:
                        ${result[0].from_time} = ${slotfrom_time.format("YYYY-MM-DD HH:mm:ss")}
                        -to->
                        ${result[0].to_time} = ${slotto_time.format("YYYY-MM-DD HH:mm:ss")}`
                    )
                    const booked_start = momentTimezone.utc(`${request.date} ${item.slot_time}`)
                                        .tz(request.timezone)
                    const booked_end = momentTimezone.utc(item.end_datetime)
                                        .tz(request.timezone)

                    console.log(`Booking:
                        '${item.slot_time}' : '${booked_start.format("YYYY-MM-DD HH:mm:ss")}'
                        -to->
                        '${item.end_datetime}' : '${booked_end.format("YYYY-MM-DD HH:mm:ss")}'`
                    )

                    const to_remove = []
                    // NOTE: .forEach doesn't work - idk why. It just skips it
                    // as if timeslots is empty. Suspecting due to async...
                    for(let i = 0; i < timeslots.length; i++) {
                        const slot = timeslots[i]
                        console.log(`${slot.format("HH:mm")} --bkd start--> ${booked_start.format("HH:mm")}`)
                        if (i >= timeslots.length - 1) {
                            if (slot.isSame(booked_start))
                                to_remove.push(slot)
                            break
                        }

                        if (!(slot.isSame(booked_start) && slot.isBefore(timeslots[i + 1])))
                            continue

                        to_remove.push(slot)

                        const end_slots = timeslots.slice(i)
                        for(let j = 0; j < end_slots.length; j++) {
                            const next_slot = end_slots[j]
                            console.log(`${next_slot.format("YYYY-MM-DD HH:mm:ss")} --bkd end--> ${booked_end.format("YYYY-MM-DD HH:mm:ss")}`)
                            if (next_slot.isBefore(booked_end)){
                                to_remove.push(next_slot)
                            }
                        } // for j
                    } // for i
                    console.log('To remove: ')
                    console.log(to_remove)
                    timeslots = timeslots.filter(slot => to_remove.indexOf(slot) < 0)
                })
                timeslots.forEach((slot, i) => timeslots[i] = slot.format("HH:mm"))
                callback(timeslots, t('restapi_slot_found'), '1');
            }) // con booking_query
        }) // con query
    }, // get_available_slot


    /**
     * Function for get available slot
     */
    get_available_slot_depr: function (request, callback) {
        con.query("select *,DAYNAME('" + request.date + "') as day_name from tbl_service_provider_available_slot where service_provider_id = " + request.service_provider_id + " AND business_location_id =" + request.business_location_id + " AND is_deleted = '0' AND from_time > '00:00' AND to_time >= '00:00' AND from_time != '' AND to_time != '' having day = day_name", function (err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    con.query("select * from tbl_service_provider_block_date where service_provider_id = " + request.service_provider_id + " AND business_location_id =" + request.business_location_id + " AND is_deleted = '0' AND date = '" + request.date + "'", function (err1, block_result) {
                        var moment = require('moment-timezone');
                        var slotfrom_time = moment.utc(request.date + ' ' + result[0].from_time, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("HH:mm");
                        var slotto_time = moment.utc(request.date + ' ' + result[0].to_time, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("HH:mm");
                        var current_date = moment().tz(request.timezone).format("YYYY-MM-DD");
                        var current_time = moment().tz(request.timezone).format("HH:mm");
                        if ((request.date != current_date) || (request.date == current_date && slotto_time >= current_time)) {
                            if ((request.date == current_date) && (slotfrom_time < current_time)) {
                                /*var current_time = moment().utc().format("HH:mm:ss");
                                if((request.date ==  datetime.create().format('Y-m-d')) && (result[0].from_time < current_time)){*/
                                var moment = require('moment');
                                var current_minute = moment().utc().format("mm");
                                //var remainder = 30 - (current_minute % 30);
                                var remainder = 10 - (current_minute % 10);
                                /*if (current_minute > 30) {
                                    var current_hour = parseInt(moment().utc().format("HH")) + parseInt(1);
                                    if(current_hour >= 24){
                                        current_hour = '00';
                                    }
                                    var start_time = moment().utc().format("YYYY-MM-DD")+' '+current_hour + ':00';
                                } else {*/
                                    var start_time = moment().utc().format("YYYY-MM-DD HH") + ':' + (parseInt(current_minute) + parseInt(remainder));
                                //}
                                //var start_time = moment().utc().format("YYYY-MM-DD HH:mm");
                                request.is_today = true;
                            } else {
                                var start_time = result[0].from_time;
                                request.is_today = false;
                            }
                            if (!err1 && block_result[0] != undefined && block_result[0] != "") {
                                service_provider.get_time_slots(start_time, result[0].to_time, block_result[0], request, function (slots) {
                                    if (slots.length > 0) {
                                        callback(slots, t('restapi_slot_found'), '1');
                                    } else {
                                        callback(null, t('restapi_slot_notfound'), '2');
                                    }
                                });
                            } else {
                                service_provider.get_time_slots(start_time, result[0].to_time, '', request, function (slots) {
                                    console.log(slots)
                                    if (slots.length > 0) {
                                        callback(slots, t('restapi_slot_found'), '1');
                                    } else {
                                        callback(null, t('restapi_slot_notfound'), '2');
                                    }
                                });
                            }
                        } else {
                            callback(null, t('restapi_slot_notfound'), '2');
                            return false;
                        }
                    });
                } else {
                    callback([], t('restapi_day_not_available'), '0');
                }
            } else {
                callback([], t('restapi_globals_error'), '0');
            }
        })
    },


    /**
     * Function for generate time slot from time to end time
     */
    get_time_slots: function (StartTime, EndTime, block_slot = "", request, callback) {
        var total_duration = (request.total_duration != undefined && request.total_duration != "") ? request.total_duration : 30;
        let timeslot = service_provider.get_all_time_slot(StartTime, EndTime, total_duration, request);
        console.log(`timeslot: ${timeslot}`)
        if (timeslot != null && timeslot.length > 0) {
            var block_slots = [];
            var moment = require('moment-timezone');
            if (block_slot != "") {
                asyncloop(timeslot, function (item, next) {
                    var moment = require('moment');
                    var localstarttime = moment.utc(request.date + " " + item, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("HH:mm");
                    // var sql = "select * from tbl_service_provider_block_time_slot where block_date_id = " + block_slot.id + " AND service_provider_id = " + block_slot.service_provider_id + " AND business_location_id = " + block_slot.business_location_id + " AND is_deleted = '0' AND '" + localstarttime + "' BETWEEN DATE_FORMAT(CONVERT_TZ(CONCAT('"+request.date+"',' ',from_time),'+00:00','"+request.timezone_diff+"'),'%H:%i') AND DATE_FORMAT(CONVERT_TZ(CONCAT('"+request.date+"',' ',to_time),'+00:00','"+request.timezone_diff+"'),'%H:%i')";
                    // const sql = `
                    //     SELECT *
                    //     FROM tbl_service_provider_block_time_slot
                    //     -- WHERE block_date_id = ${block_slot.id}
                    //         AND service_provider_id = ${block_slot.service_provider_id}
                    //         AND business_location_id = ${block_slot.business_location_id}
                    //         AND is_deleted = '0'
                    //         AND '${localstarttime}' BETWEEN DATE_
                    //             FORMAT(CONVERT_TZ(CONCAT('${request.date}', ' ', from_time), '+00:00', '${request.timezone_diff}'), '%H:%i')
                    //         AND DATE_FORMAT(CONVERT_TZ(CONCAT('${request.date}', ' ', to_time), '+00:00', '${request.timezone_diff}'), '%H:%i')
                    //     `;

                    const query = `
                        SELECT *
                        FROM tbl_appointment_booking
                        WHERE service_provider_id = ${request.service_provider_id}
                            AND business_location_id = ${request.business_location_id}
                            AND '${localstarttime}' BETWEEN DATE_
                                FORMAT(CONVERT_TZ(CONCAT('${request.date}', ' ', from_time), '+00:00', '${request.timezone_diff}'), '%H:%i')
                            AND DATE_FORMAT(CONVERT_TZ(CONCAT('${request.date}', ' ', to_time), '+00:00', '${request.timezone_diff}'), '%H:%i')
                    `
                    console.log(`get_time_slots:: tbl_service_provider_block_time_slot query: \n ${query}`)
                    con.query(query, function (err, result) {
                        if (!err && result[0] != "" && result[0] != undefined) {
                            next()
                        } else {
                            if (request.is_user_app) {
                                block_slots.push(item);
                                next();
                            } else {
                                var slot_starttime = moment.utc(request.date + " " + item, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("YYYY-MM-DD HH:mm");
                                var slot_endtime = moment.utc(request.date + " " + item, 'YYYY-MM-DD HH:mm:ss').add(parseInt(total_duration) - 1, "minutes").tz(request.timezone).format("YYYY-MM-DD HH:mm");

                                var sql2 = "select * from tbl_appointment_booking where service_provider_id = " + request.service_provider_id + " AND business_location_id = " + request.business_location_id + " AND is_deleted = '0' AND booking_status NOT IN ('Cancelled','No Show','Paid','Completed') AND (('"+slot_starttime+"' >= DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i') AND '"+slot_starttime+"' < DATE_FORMAT(CONVERT_TZ(end_datetime,'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i')) OR ('" + slot_endtime + "' BETWEEN DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i') AND DATE_FORMAT(CONVERT_TZ(end_datetime,'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i')))";
                                con.query(sql2, function (err1, result1) {
                                    if (!err1 && result1[0] != "" && result1[0] != undefined) {
                                        next();
                                    } else {
                                        block_slots.push(item);
                                        next();
                                    }
                                });
                            }
                        }
                    });
                }, function () {
                    callback(block_slots)
                });
            } else {
                asyncloop(timeslot, function (item, next) {
                    var slot_starttime = '', slot_endtime = '';

                    // Get the current time in local timezone
                    const localTimeZone = moment.tz(request.timezone);

                    // Check if DST is active for local timezone
                    const isDSTActive = localTimeZone.isDST();
                    if(isDSTActive){
                        slot_starttime = moment.utc(request.date + " " + item, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).subtract(1, 'hours').format("YYYY-MM-DD HH:mm");
                        slot_endtime = moment.utc(request.date + " " + item, 'YYYY-MM-DD HH:mm:ss').add(parseInt(total_duration) - 1, "minutes").tz(request.timezone).subtract(1, 'hours').format("YYYY-MM-DD HH:mm");
                    }else{
                        slot_starttime = moment.utc(request.date + " " + item, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("YYYY-MM-DD HH:mm");
                        slot_endtime = moment.utc(request.date + " " + item, 'YYYY-MM-DD HH:mm:ss').add(parseInt(total_duration) - 1, "minutes").tz(request.timezone).format("YYYY-MM-DD HH:mm");
                    }
                    var sql1 = "select * from tbl_appointment_booking where DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "' AND service_provider_id = " + request.service_provider_id + " AND business_location_id = " + request.business_location_id + " AND is_deleted = '0' AND booking_status NOT IN ('Cancelled','No Show','Paid','Completed') AND (('"+slot_starttime+"' >= DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i') AND '"+slot_starttime+"' < DATE_FORMAT(CONVERT_TZ(end_datetime,'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i')) OR ('" + slot_endtime + "' BETWEEN DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i') AND DATE_FORMAT(CONVERT_TZ(end_datetime,'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i')))";
                    con.query(sql1, function (err1, result1) {
                        if (!err1 && result1[0] != "" && result1[0] != undefined) {
                            next();
                        } else {
                            block_slots.push(item)
                            next()
                        }
                    });
                }, function () {
                    callback(block_slots)
                });
            }
        } else {
            callback([]);
        }
    },

    /**
     * Function for get all time slot
     */
    get_all_time_slot(pstart_time = '00:00', pend_time = '23:00', pinterval = 60, request) {
        //jira point number BPROIOS-272 Time slots - Weird as client we did this changes
        var moment = require('moment-timezone');
        if (pstart_time > pend_time) {
            if (request.is_today) {
                var start_time = moment.utc(pstart_time, 'YYYY-MM-DD HH:mm').tz(request.timezone);
            } else {
                var start_time = moment.utc(request.date + " " + pstart_time, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone);
            }
            var end_time = moment.tz(request.date + " 23:59", 'YYYY-MM-DD HH:mm:ss', request.timezone);
            var localend_datetime = moment.utc(moment(request.date, 'YYYY-MM-DD').add(1, 'days').format('YYYY-MM-DD') + " " + pend_time, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone);
            if (localend_datetime <= end_time) {
                end_time = localend_datetime;
            }
            let interval = pinterval;
            let timeslot = [];
            while (start_time < end_time) {
                if (request.date == new moment(start_time).format('YYYY-MM-DD') && new moment(start_time).add(interval, 'minutes') <=  end_time) {
                    timeslot.push(new moment(start_time, request.timezone).utc().format('HH:mm:ss'));
                }
                start_time.add(interval, 'minutes');
            }
            return timeslot;
        } else {
            if (request.is_today) {
                var start_time = moment.utc(pstart_time, 'YYYY-MM-DD HH:mm').tz(request.timezone);
            } else {
                var start_time = moment.utc(request.date + " " + pstart_time, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone);
            }
            var end_time = moment.tz(request.date + " 23:59", 'YYYY-MM-DD HH:mm:ss', request.timezone);
            var localend_datetime = moment.utc(request.date + " " + pend_time, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone);
            if (localend_datetime <= end_time) {
                end_time = localend_datetime;
            }
            let interval = pinterval;
            let timeslot = [];
            while (start_time < end_time) {
                //if(request.date == new moment(start_time).format('YYYY-MM-DD') && new moment(start_time).add(interval, 'minutes').format('YYYY-MM-DD HH:mm:ss') <= end_time.format('YYYY-MM-DD HH:mm:ss'))
                if (new moment(start_time).add(interval, 'minutes') <=  end_time) {
                    timeslot.push(new moment(start_time, request.timezone).utc().format('HH:mm:ss'));
                }
                start_time.add(interval, 'minutes');
            }
            return timeslot;
        }
    },

    /**
     * Function for get product type
     */
    get_product_size: function (request, callback) {
        var sql = con.query("select id,longname,unit from tbl_product_type where is_deleted = '0' AND status = 'Active'", function (err, result) {
            if (!err) {
                if (result[0] != "" && result[0] != undefined) {
                    callback(result, 'Produt size found successfully', '1');
                } else {
                    callback(null, 'Produt size not found', '0');
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        })
    },

    /**
     * check already book this slot or provider
     */
    checkalreadybookthisslotorprovider: function (request, callback) {
        if (request.date != undefined && request.date != "" && request.time_slot != undefined && request.time_slot != "") {
            con.query("SELECT ab.* FROM tbl_appointment_booking as ab WHERE CONCAT('" + request.date + "',' ','" + request.time_slot + "') >= CONCAT(ab.date,' ',ab.slot_time) AND CONCAT('" + request.date + "',' ','" + request.time_slot + "') < ab.end_datetime AND ab.service_provider_id = '" + request.service_provider_id + "' AND ab.business_location_id = '" + request.business_location_id + "' AND ab.is_deleted = '0' AND ab.booking_status NOT IN ('Cancelled','No Show','Completed','Paid') GROUP BY ab.id ORDER BY ab.id DESC", function (error, result) {
                if (!error && result[0] != undefined) {
                    callback(true);
                } else {
                    callback(false);
                }
            });
        } else {
            if (request.date == undefined || request.date == "") {
                request.date = datetime.create().format('Y-m-d');
            }
            if (request.time_slot == undefined || request.time_slot == "") {
                request.time_slot = "00:00";
            }
            callback(false);
        }
    },

    /**
     * Function for create walk in booking
     */
    create_new_booking: function (request, callback) {
        con.query("SELECT * FROM tbl_service_provider WHERE id = '"+request.service_provider_id+"'",function(error,spdata,fields){
            if(!error && spdata[0] != undefined && spdata[0].merchant_account_id != ""){
                if (request.total_duration != undefined && request.total_duration != "") {
                    var enddatetime = moment(request.date + " " + request.time_slot)
                      .add(request.total_duration, 'minutes')
                      .add(request.additional_duration || 0, 'minutes')
                      .format("YYYY-MM-DD HH:mm:ss");
                } else {
                    var enddatetime = request.date + " " + request.time_slot;
                }
                if(request.discount == undefined || request.discount == ""){
                    request.discount = 0;
                }
                if(request.payment_mode != undefined && request.payment_mode != "" && request.payment_mode == 'Wallet'){
                    var merchant_fees_amount = parseFloat(0);
                } else {
                    var merchant_fees_amount = (parseFloat(request.total_amount) * parseFloat(spdata[0].merchant_fees) / 100) + parseFloat(spdata[0].merchant_fees_cents);
                }
                var app_fees_amount = (parseFloat(request.sub_total) - parseFloat(request.discount)) * (spdata[0].app_fees) / 100;
                var admin_earning = merchant_fees_amount + app_fees_amount;
                service_provider.generate_booking_id("tbl_appointment_booking", function (booking_id) {
                    var new_booking = {
                        booking_id: booking_id,
                        service_provider_id: request.service_provider_id,
                        business_location_id: request.business_location_id,
                        customer_name: request.customer_name,
                        client_id: request.client_id,
                        payment_mode: request.payment_mode,
                        customer_id: '0',
                        date: request.date,
                        slot_time: request.time_slot,
                        end_datetime: enddatetime,
                        total_duration: (request.total_duration != undefined && request.total_duration != "") ? request.total_duration : 0,
                        additional_duration: request.additional_duration || 0,
                        description: (request.description != undefined && request.description != "") ? request.description : "",
                        booking_status: 'Accepted',
                        booking_type: 'walkin',
                        payment_status: (request.payment_status != "" && request.payment_status != undefined) ? request.payment_status : "paid",
                        payment_mode: (request.payment_mode != "" && request.payment_mode != undefined) ? request.payment_mode : "Cash",
                        payment_from: "Cash",
                        payment_intent_id: (request.payment_intent_id != "" && request.payment_intent_id != undefined) ? request.payment_intent_id : null,
                        promocode: (request.promocode != "" && request.promocode != undefined) ? request.promocode : "",
                        sub_total: request.sub_total,
                        wallet_amount: '0',
                        total_amount: request.total_amount,
                        discount: request.discount,
                        tip_amount: request.tip,
                        merchant_fees:spdata[0].merchant_fees,
                        merchant_fees_amount:merchant_fees_amount,
                        merchant_fees_cents:spdata[0].merchant_fees_cents,
                        app_fees:spdata[0].app_fees,
                        app_fees_amount:app_fees_amount,
                        admin_earning:admin_earning,
                        provider_earning: parseFloat(request.total_amount) - admin_earning,
                        tax: request.tax,
                        is_reported: '0',
                        is_deleted: '0',
                        payment_datetime: datetime.create().format('Y-m-d H:M:S'),
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                    }
                    service_provider.checkalreadybookthisslotorprovider(request, function (is_alreadybook) {
                        if (is_alreadybook) {
                            callback(null, "You are already booked for this time duration.", '0');
                        } else {
                            common.single_insert_data('tbl_appointment_booking', new_booking, function (appointment_id) {
                                var appointmentId = appointment_id.insertId;
                                if (appointment_id) {
                                    if (request.service) {
                                        asyncloop(request.service, function (item, next) {
                                            if (item) {
                                                var appointment_detail = {
                                                    appointment_id: appointmentId,
                                                    booking_id: booking_id,
                                                    service_provider_id: request.service_provider_id,
                                                    service_id: item.service_id,
                                                    price: item.price,
                                                    type: 'service',
                                                    product_id: '0',
                                                    quantity: '0',
                                                    service_status: 'pending',
                                                    is_deleted: '0',
                                                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                    insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                }
                                                common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function (is_appointment_detail) {
                                                    next();
                                                })
                                            } else {
                                                next();
                                            }
                                        }, function () {
                                            if (request.product) {
                                                asyncloop(request.product, function (item1, next1) {
                                                    if (item1) {
                                                        var appointment_detail = {
                                                            appointment_id: appointmentId,
                                                            booking_id: booking_id,
                                                            service_provider_id: request.service_provider_id,
                                                            product_id: item1.product_id,
                                                            subproduct_id: (item1.subproduct_id != "" && item1.subproduct_id != undefined) ? item1.subproduct_id : '0',
                                                            price: item1.price,
                                                            type: 'product',
                                                            service_id: '0',
                                                            quantity: item1.quantity,
                                                            service_status: '',
                                                            is_deleted: '0',
                                                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                            insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                        }
                                                        common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function (is_appointment_detail) {
                                                            common.credit_wallet("tbl_subproduct", "id = '" + appointment_detail.subproduct_id + "' AND product_id = '" + appointment_detail.product_id + "'", "used_quantity", item1.quantity, function (balance) {
                                                                common.debit_wallet("tbl_subproduct", "id = '" + appointment_detail.subproduct_id + "' AND product_id = '" + appointment_detail.product_id + "'", "remaining_quantity", item1.quantity, function (balance) {
                                                                    next1();
                                                                });
                                                            });
                                                        })
                                                    } else {
                                                        next1();
                                                    }
                                                }, function () {
                                                    callback(true, t('restapi_service_booked'), '1');
                                                })
                                            } else {
                                                callback(true, t('restapi_service_booked'), '1');
                                            }
                                        })
                                    } else {
                                        if (request.product) {
                                            asyncloop(request.product, function (item1, next1) {
                                                if (item1) {
                                                    var appointment_detail = {
                                                        appointment_id: appointmentId,
                                                        booking_id: booking_id,
                                                        service_provider_id: request.service_provider_id,
                                                        product_id: item1.product_id,
                                                        subproduct_id: (item1.subproduct_id != "" && item1.subproduct_id != undefined) ? item1.subproduct_id : '0',
                                                        price: item1.price,
                                                        type: 'product',
                                                        service_id: '0',
                                                        quantity: item1.quantity,
                                                        service_status: '',
                                                        is_deleted: '0',
                                                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                    }
                                                    common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function (is_appointment_detail) {
                                                        common.credit_wallet("tbl_subproduct", "id = '" + appointment_detail.subproduct_id + "' AND product_id = '" + appointment_detail.product_id + "'", "used_quantity", item1.quantity, function (balance) {
                                                            common.debit_wallet("tbl_subproduct", "id = '" + appointment_detail.subproduct_id + "' AND product_id = '" + appointment_detail.product_id + "'", "remaining_quantity", item1.quantity, function (balance) {
                                                                next1();
                                                            });
                                                        });
                                                    })
                                                } else {
                                                    next1();
                                                }
                                            }, function () {
                                                callback(true, "Order has been placed", '1');
                                            })
                                        } else {
                                            callback(true, "Order has been placed", '1');
                                        }
                                    }
                                } else {
                                    callback(null, t('restapi_globals_error'), '0');
                                }
                            });
                        }
                    });
                })
            } else {
                callback(null, "The service provider not added his bank account.", '0');
            }
        });
    },

    /**
     * Function for quick checkout booking
     */
    quick_checkout_booking: function (request, callback) {
        con.query("SELECT * FROM tbl_service_provider WHERE id = '"+request.service_provider_id+"'",function(error,spdata,fields){
            if(!error && spdata[0] != undefined && spdata[0].merchant_account_id != ""){
                if (request.total_duration != undefined && request.total_duration != "") {
                    var enddatetime = moment(request.date + " " + request.time_slot, SIMPLE_DATE_FORMAT_WITH_12HR_TIME)
                      .add(request.total_duration, 'minutes')
                      .add(request.additional_duration || 0, 'minutes')
                      .format("YYYY-MM-DD HH:mm:ss");
                } else {
                    var enddatetime = request.date + " " + request.time_slot;
                }
                if(request.discount == undefined || request.discount == ""){
                    request.discount = 0;
                }
                if(request.payment_mode != undefined && request.payment_mode != "" && request.payment_mode == 'Wallet'){
                    var merchant_fees_amount = parseFloat(0);
                } else {
                    var merchant_fees_amount = (parseFloat(request.total_amount) * parseFloat(spdata[0].merchant_fees) / 100) + parseFloat(spdata[0].merchant_fees_cents);
                }
                var app_fees_amount = (parseFloat(request.sub_total) - parseFloat(request.discount)) * (spdata[0].app_fees) / 100;
                var admin_earning = merchant_fees_amount + app_fees_amount;
                service_provider.generate_booking_id("tbl_appointment_booking", function (booking_id) {
                    var new_booking = {
                        booking_id: booking_id,
                        service_provider_id: request.service_provider_id,
                        business_location_id: request.business_location_id,
                        customer_id: request.customer_id,
                        customer_name: '',
                        date: (request.date) ? request.date : datetime.create().format('Y-m-d'),
                        slot_time: (request.time_slot) ? request.time_slot : datetime.create().format('H:M'),
                        end_datetime: enddatetime,
                        total_duration: (request.total_duration != undefined && request.total_duration != "") ? request.total_duration : 0,
                        additional_duration: request.additional_duration || 0,
                        description: (request.description != undefined && request.description != "") ? request.description : "",
                        booking_status: 'Completed',
                        booking_type: 'walkin',
                        payment_mode: (request.payment_mode != "" && request.payment_mode != undefined) ? request.payment_mode : "Cash",
                        payment_from: "Cash",
                        payment_intent_id: (request.payment_intent_id != "" && request.payment_intent_id != undefined) ? request.payment_intent_id : null,
                        payment_status: (request.payment_status != "" && request.payment_status != undefined) ? request.payment_status : "paid",
                        promocode: (request.promocode != "" && request.promocode != undefined) ? request.promocode : "",
                        sub_total: request.sub_total,
                        wallet_amount: (request.wallet_amount != "" && request.wallet_amount != undefined) ? request.wallet_amount : 0,
                        total_amount: request.total_amount,
                        discount: request.discount,
                        merchant_fees:spdata[0].merchant_fees,
                        merchant_fees_amount:merchant_fees_amount,
                        merchant_fees_cents:spdata[0].merchant_fees_cents,
                        app_fees:spdata[0].app_fees,
                        app_fees_amount:app_fees_amount,
                        admin_earning:admin_earning,
                        provider_earning: parseFloat(request.total_amount) - admin_earning,
                        tip_amount: request.tip,
                        tax: request.tax,
                        is_reported: '0',
                        is_deleted: '0',
                        payment_datetime: datetime.create().format('Y-m-d H:M:S'),
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                    }
                    if (!request.service && request.product != "") {
                        new_booking.is_product = '1';
                        new_booking.is_only_product = '1';
                    }
                    service_provider.checkalreadybookthisslotorprovider(request, function (is_alreadybook) {
                        if (is_alreadybook && request.service != undefined && request.service != "" && request.service.length > 0) {
                            callback(null, "You are already booked for this time duration.", '0');
                        } else {
                            common.single_insert_data('tbl_appointment_booking', new_booking, function (appointment_id) {
                                var appointmentId = appointment_id.insertId;
                                if (appointment_id) {
                                    if (request.service) {
                                        asyncloop(request.service, function (item, next) {
                                            if (item) {
                                                var appointment_detail = {
                                                    appointment_id: appointmentId,
                                                    booking_id: booking_id,
                                                    service_provider_id: request.service_provider_id,
                                                    service_id: item.service_id,
                                                    price: item.price,
                                                    type: 'service',
                                                    product_id: '0',
                                                    quantity: '0',
                                                    service_status: 'pending',
                                                    is_deleted: '0',
                                                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                    insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                }
                                                common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function (is_appointment_detail) {
                                                    next();
                                                })
                                            } else {
                                                next();
                                            }
                                        }, function () {
                                            if (request.image) {
                                                asyncloop(request.image, function (item2, next2) {
                                                    if (item2) {
                                                        var images1 = {
                                                            image_name: item2.image_name,
                                                            type_id: appointmentId,
                                                            type: 'quick_appointment_image'
                                                        }
                                                        common.single_insert_data('tbl_image', images1, function (is_image_add) {
                                                            next2();
                                                        })
                                                    } else {
                                                        next2();
                                                    }
                                                }, function () {
                                                    if (request.product) {
                                                        asyncloop(request.product, function (item1, next1) {
                                                            if (item1) {
                                                                var appointment_detail = {
                                                                    appointment_id: appointmentId,
                                                                    booking_id: booking_id,
                                                                    service_provider_id: request.service_provider_id,
                                                                    product_id: item1.product_id,
                                                                    subproduct_id: (item1.subproduct_id != "" && item1.subproduct_id != undefined) ? item1.subproduct_id : '0',
                                                                    price: item1.price,
                                                                    type: 'product',
                                                                    service_id: '0',
                                                                    quantity: item1.quantity,
                                                                    service_status: '',
                                                                    is_deleted: '0',
                                                                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                                    insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                                }
                                                                common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function (is_appointment_detail) {
                                                                    common.credit_wallet("tbl_subproduct", "id = '" + appointment_detail.subproduct_id + "' AND product_id = '" + appointment_detail.product_id + "'", "used_quantity", item1.quantity, function (balance) {
                                                                        common.debit_wallet("tbl_subproduct", "id = '" + appointment_detail.subproduct_id + "' AND product_id = '" + appointment_detail.product_id + "'", "remaining_quantity", item1.quantity, function (balance) {
                                                                            next1();
                                                                        });
                                                                    });
                                                                })
                                                            } else {
                                                                next1();
                                                            }
                                                        }, function () {
                                                            callback(true, "Order has been placed", '1');
                                                        })
                                                    } else {
                                                        callback(true, t('restapi_service_booked'), '1');
                                                    }
                                                })
                                            } else {
                                                if (request.product) {
                                                    asyncloop(request.product, function (item1, next1) {
                                                        if (item1) {
                                                            var appointment_detail = {
                                                                appointment_id: appointmentId,
                                                                booking_id: booking_id,
                                                                service_provider_id: request.service_provider_id,
                                                                product_id: item1.product_id,
                                                                subproduct_id: (item1.subproduct_id != "" && item1.subproduct_id != undefined) ? item1.subproduct_id : '0',
                                                                price: item1.price,
                                                                type: 'product',
                                                                service_id: '0',
                                                                quantity: item1.quantity,
                                                                service_status: '',
                                                                is_deleted: '0',
                                                                updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                                insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                            }
                                                            common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function (is_appointment_detail) {
                                                                common.credit_wallet("tbl_subproduct", "id = '" + appointment_detail.subproduct_id + "' AND product_id = '" + appointment_detail.product_id + "'", "used_quantity", item1.quantity, function (balance) {
                                                                    common.debit_wallet("tbl_subproduct", "id = '" + appointment_detail.subproduct_id + "' AND product_id = '" + appointment_detail.product_id + "'", "remaining_quantity", item1.quantity, function (balance) {
                                                                        next1();
                                                                    });
                                                                });
                                                            })
                                                        } else {
                                                            next1();
                                                        }
                                                    }, function () {
                                                        callback(true, "Order has been placed", '1');
                                                    })
                                                } else {
                                                    callback(true, t('restapi_service_booked'), '1');
                                                }
                                            }
                                        })
                                    } else {
                                        if (request.product) {
                                            asyncloop(request.product, function (item1, next1) {
                                                if (item1) {
                                                    var appointment_detail = {
                                                        appointment_id: appointmentId,
                                                        booking_id: booking_id,
                                                        service_provider_id: request.service_provider_id,
                                                        product_id: item1.product_id,
                                                        subproduct_id: (item1.subproduct_id != "" && item1.subproduct_id != undefined) ? item1.subproduct_id : '0',
                                                        price: item1.price,
                                                        type: 'product',
                                                        service_id: '0',
                                                        quantity: item1.quantity,
                                                        service_status: '',
                                                        is_deleted: '0',
                                                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                                                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                    }
                                                    common.single_insert_data('tbl_appointment_booking_detail', appointment_detail, function (is_appointment_detail) {
                                                        common.credit_wallet("tbl_subproduct", "id = '" + appointment_detail.subproduct_id + "' AND product_id = '" + appointment_detail.product_id + "'", "used_quantity", item1.quantity, function (balance) {
                                                            common.debit_wallet("tbl_subproduct", "id = '" + appointment_detail.subproduct_id + "' AND product_id = '" + appointment_detail.product_id + "'", "remaining_quantity", item1.quantity, function (balance) {
                                                                next1();
                                                            });
                                                        });
                                                    })
                                                } else {
                                                    next1();
                                                }
                                            }, function () {
                                                callback(true, "Order has been placed", '1');
                                            })
                                        } else {
                                            callback(true, "Order has been placed", '1');
                                        }
                                    }
                                } else {
                                    callback(null, t('restapi_globals_error'), '0');
                                }
                            })
                        }
                    });
                })
            } else {
                callback(null, "The service provider not added his bank account.", '0');
            }
        });
    },

    /**
     * Function for generate booking id
     */
    generate_booking_id(table_name, callback) {
        // var code = randomstring.generate({ length: 15, charset: 'numeric' });
        var code = "ORDER" + Math.floor(*********** + Math.random() * ***********);
        con.query("SELECT booking_id FROM " + table_name + " WHERE booking_id = '" + code + "'", function (err, result, fields) {
            if (!err) {
                if (result.length <= 0) {
                    callback(code);
                } else {
                    data.generate_order_id(table_name, function (response) { });
                }
            } else {
                callback(null, t('restapi_globals_error'), '0');
            }
        });
    },

    /**
     * Function for apply promo code
     */
    apply_promocode: function (request, callback) {
        // if (request.product_id) {
        //applied_on IN (" + '\'' + request.product_id.split(',').join('\',\'') + '\'' + ")
        //service_provider_id = " + request.service_provider_id + "
        var sql = con.query("SELECT id,promocode,service_provider_id,is_deleted,description,discount_amount as discount_amounts,discount_type,max_discount_amount,applied_on,applied_service_on,is_clientspecific_promo,valid_till from tbl_occational_promo where promocode = '" + request.promocode + "' AND is_deleted = '0'", function (error, results) {
            if (!error && results[0] != undefined) {
                if (request.product_id != undefined && request.product_id != "") {
                    var product_ids = request.product_id.split(',').map(val => String(val));
                } else {
                    var product_ids = "";
                }
                if (request.service_id != undefined && request.service_id != "") {
                    var service_ids = request.service_id.split(',').map(val => String(val));
                } else {
                    var service_ids = "";
                }
                if (request.service_provider_id.toString() != results[0].service_provider_id.toString()) {
                    callback('0', t('restapi_promocode_isnotforthisserviceprovider'), null);
                } else if (results[0].is_clientspecific_promo == '0' && product_ids != "" && results[0].applied_on != "" && product_ids.indexOf(results[0].applied_on.toString()) < 0) {
                    callback('0', t('restapi_promocode_isnotforthisproduct'), null);
                } else if (results[0].is_clientspecific_promo == '0' && service_ids != "" && results[0].applied_service_on != "" && service_ids.indexOf(results[0].applied_service_on.toString()) < 0) {
                    callback('0', t('restapi_promocode_isnotforthisservice'), null);
                } else if (datetime.create().format('Y-m-d') > results[0].valid_till) {
                    callback('0', t('restapi_promocode_isexpired_now'), null);
                } else {
                    con.query("SELECT * FROM tbl_occational_promo_clients WHERE user_id = '" + request.user_id + "' AND promo_id = '" + results[0].id + "'", function (err1, result1) {
                        if (err1 && result1.length <= 0) {
                            callback('0', t('restapi_promocode_isnotforthisuser'), null);
                        } else {
                            if (results[0].discount_type == 'Flat') {
                                if (parseFloat(results[0].max_discount_amount) >= parseFloat(request.sub_total)) {
                                    var discount_amount = parseFloat(request.sub_total).toFixed(2);
                                } else {
                                    var discount_amount = parseFloat(results[0].max_discount_amount).toFixed(2);
                                }
                            } else {
                                var amount = (parseFloat(request.sub_total) * parseFloat(results[0].discount_amounts)) / 100;
                                if (parseFloat(results[0].max_discount_amount) >= amount) {
                                    var discount_amount = parseFloat(amount).toFixed(2);
                                } else {
                                    var discount_amount = parseFloat(results[0].max_discount_amount).toFixed(2);
                                }
                            }
                            delete (results[0].discount_amounts)
                            results[0].discount_amount = discount_amount;
                            callback('1', t('reatapi_promocode_success'), results[0]);
                        }
                    });
                }
            } else {
                var sql = con.query("SELECT * from tbl_promocode where is_active='1' AND is_deleted='0' AND promocode = '" + request.promocode + "'", function (err, result) {
                    if (!err && result[0] != undefined) {
                        var dt = datetime.create(result[0].start_date);
                        var dt1 = datetime.create(result[0].end_date);
                        const secondDate = dt.format('Y-m-d');
                        const secondDate1 = dt1.format('Y-m-d');
                        if (datetime.create().format('Y-m-d') < secondDate) {
                            callback('0', t('restapi_promocode_isnotusable_yet'), null);
                        } else if (datetime.create().format('Y-m-d') > secondDate1) {
                            callback('0', t('restapi_promocode_isexpired_now'), null);
                        } else {
                            if (result[0].promocodetype == 'Amount') {
                                if (parseFloat(result[0].max_discount) >= parseFloat(request.sub_total)) {
                                    var discount_amount = parseFloat(request.sub_total).toFixed(2);
                                } else {
                                    var discount_amount = parseFloat(result[0].max_discount).toFixed(2);
                                }
                            } else {
                                var amount = (parseFloat(request.sub_total) * parseFloat(result[0].discount)) / 100;
                                if (parseFloat(result[0].max_discount) >= amount) {
                                    var discount_amount = parseFloat(amount).toFixed(2);
                                } else {
                                    var discount_amount = parseFloat(result[0].max_discount).toFixed(2);
                                }
                            }
                            result[0].discount_amount = discount_amount;
                            callback('1', t('reatapi_promocode_success'), result[0]);
                        }
                    } else {
                        callback('0', t('restapi_promocode_invalid'), null);

                    }
                });
            }
        });
    },

    /**
     * create token with card
     */
    create_payment_intent: function (request, callback) {
        var service_provider_id = request.user_id;
        if(request.service_provider_id != undefined && request.service_provider_id != ""){
            service_provider_id = request.service_provider_id;
        }
        con.query("SELECT * FROM tbl_service_provider WHERE id = '"+service_provider_id+"'",function(error,spdata,fields){
            if(!error && spdata[0] != undefined && spdata[0].merchant_account_id != ""){
                if(request.discount == undefined || request.discount == ""){
                    request.discount = 0;
                }
                if(request.order_type != undefined && request.order_type == 'Booth'){
                    var finalamount = parseFloat(request.amount) - parseFloat(request.discount) + parseFloat(request.tax) + parseFloat(request.cleaning_fees);
                    var merchant_fees_amount = ((parseFloat(request.amount) - parseFloat(request.discount) + parseFloat(request.tax) + parseFloat(request.cleaning_fees)) * parseFloat(spdata[0].booth_merchant_fees) / 100)  + parseFloat(spdata[0].booth_merchant_fees_cents);
                    var app_fees_amount = (parseFloat(request.amount) - parseFloat(request.discount)) * (spdata[0].booth_app_fees) / 100;
                } else {
                    var finalamount = parseFloat(request.amount);
                    var merchant_fees_amount = (parseFloat(request.amount) * parseFloat(spdata[0].merchant_fees) / 100) + parseFloat(spdata[0].merchant_fees_cents);
                    var app_fees_amount = (parseFloat(request.sub_total) - parseFloat(request.discount)) * (spdata[0].app_fees) / 100;
                }
                var admin_earning = merchant_fees_amount + app_fees_amount;
                if(request.payment_from == 'ApplePay' || request.payment_from == 'GooglePay'){
                    var payment_object = {
                        amount: Math.round(finalamount * 100),
                        currency: "USD".toLowerCase(),
                        confirm : true,
                        payment_method : request.transaction_id,
                        application_fee_amount : Math.round(admin_earning * 100),
                        transfer_data:{
                            destination:spdata[0].merchant_account_id
                        },
                        capture_method : "automatic",//"manual",
                        description: 'Blookd',
                        statement_descriptor : 'Blookd',
                        statement_descriptor_suffix : 'Blookd',
                        setup_future_usage : 'off_session',
                        confirmation_method : 'automatic',
                        use_stripe_sdk : 'true'
                    };
                    require("../../../config/payment").createpaymentintents(payment_object,function(respocode,responsmsg,paymentintent){
                        callback('1', 'payment intent generated successfully', paymentintent);
                    });
                } else {
                    var cardobject = {
                        "name": request.card_holder_name,
                        "number": request.card_number,
                        "exp_month": request.expiry_month,
                        "exp_year": request.expiry_year,
                        "cvc": request.cvv,
                    }
                    stripe.createCardToken(cardobject, function (responsecode, responsemsg, token) {
                        if (token != null) {
                            var customerObject = {
                                source: token.id,
                                description: global.APP_NAME + " WALKIN",
                            }
                            stripe.createpaymentmethod(token.id, function (customercode, customermsg, customer) {
                                var paymentObject = {
                                    amount: Math.round(finalamount * 100),
                                    currency: 'usd',
                                    payment_method: customer.id,
                                    description: "WALKIN",
                                    application_fee_amount : Math.round(admin_earning * 100),
                                    transfer_data:{
                                        destination:spdata[0].merchant_account_id
                                    },
                                    confirm: true,
                                    statement_descriptor: global.APP_NAME,
                                    statement_descriptor_suffix: global.APP_NAME,
                                    setup_future_usage: 'off_session',
                                    confirmation_method: 'automatic',
                                    capture_method: 'automatic',
                                    use_stripe_sdk: 'true'
                                }
                                console.log(" ",paymentObject);
                                if (paymentObject.amount >= 1) {
                                    stripe.createpaymentintents(paymentObject, function (code, message, paymentintent) {
                                        if (paymentintent != null) {
                                            callback('1', 'payment intent generated successfully', paymentintent);
                                        } else {
                                            callback(code, message, paymentintent);
                                        }
                                    });
                                } else {
                                    callback('0', t('restapi_globals_error'), null);
                                }
                            });
                        } else {
                            callback('0', responsemsg, responsecode);
                        }
                    })
                }
            } else {
                callback('0', "The service provider not added his bank account.", null);
            }
        });
    },

    /**
     * Function for appointment list
     */
    my_appointments: (request, service_provider_id, callback) => {
        let condition = ` ab.service_provider_id = ${service_provider_id} AND ab.is_deleted = '0' AND s.is_deleted ='0' `;
        const having = " having 1=1";
        let order_by = "ORDER BY DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i') DESC";
        if (request.sort_by_name != undefined && request.sort_by_name != "") {
            order_by = "ORDER BY customer_name " + request.sort_by_name;
        }
        if (request.first_name != undefined && request.first_name != "") {
            // condition += "AND (s.first_name LIKE '%" + request.first_name + "%') ";
            condition += " AND (tu.first_name LIKE '%" + request.first_name + "%' OR ab.customer_name LIKE '%"+request.first_name+"%')";
        }
        if (request.last_name != undefined && request.last_name != "") {
            // condition += "AND (s.last_name LIKE '%" + request.last_name + "%') ";
            condition += " AND (tu.last_name LIKE '%" + request.last_name + "%' OR ab.customer_name LIKE '%"+request.last_name+"%') ";
        }
        if (request.email != undefined && request.email != "") {
            condition += " AND (tu.email LIKE '%" + request.email + "%') ";
        }
        if (request.phone_number != undefined && request.phone_number != "") {
            condition += " AND (tu.phone LIKE '%" + request.phone_number + "%') ";
        }
        /*if (request.phone != undefined && request.phone != "") {
            condition += "AND (s.phone LIKE '%" + request.phone + "%') ";
        }*/
        if (request.date != undefined && request.date != "") {
            condition += " AND DATE(ab.date) = '" + request.date + "' ";
        }

        let queryStr = queries.selectAppointmentBooking(
          condition,
          having,
          order_by,
          ['In Progress', 'Reschedule Request', 'Pending', 'Accepted']
        )
        con.query(queryStr, function (err, upcomming) {
            if (err) {
                callback('0', t('restapi_globals_error') + err, null);
                return;
            }

            queryStr = queries.selectAppointmentBooking(
                condition=condition,
                having,
                order_by,
                ['Reject', 'Cancelled', 'Paid', 'Completed', 'No Show']
            )

            con.query(queryStr, function (err1, past) {
                if (err1) {
                    callback('0', t('restapi_globals_error') + err1, null);
                    return;
                }

                if (upcomming[0] != "" && upcomming[0] != undefined) {
                    var upcomming_booking = upcomming;
                } else {
                    var upcomming_booking = [];
                }
                if (past[0] != "" && past[0] != undefined) {
                    var past_booking = past;
                } else {
                    var past_booking = [];
                }
                if ((upcomming[0] != "" && upcomming[0] != undefined) || (past[0] != "" && past[0] != undefined)) {
                    callback('1', t('restapi_appointment_found'), { upcomming: upcomming_booking, past: past_booking });
                } else {
                    callback('2', 'no appointments found', { upcomming: [], past: [] });
                }
            })
        })
    },

    /**
     * Function for home screen
     */
    homescreen: function (request, service_provider_id, callback) {
        var condition = "ab.service_provider_id = '" + service_provider_id + "' AND abd.type = 'Service' AND ab.is_deleted = '0' AND ab.booking_status NOT IN ('Cancelled','No Show') ";
        if (request.date) {
            // condition += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            condition += " AND DATE_FORMAT(CONCAT(ab.date,' ',ab.slot_time),'%Y-%m-%d') = '" + request.date + "'";
            //condition += " AND ab.date = '" + request.date + "'";
        }
        con.query("select ab.*, IF(ab.customer_name = '',IF(ab.customer_id = '0', (SELECT c.customer_name FROM tbl_service_provider_client c WHERE c.is_user_app = ab.user_id ORDER BY ID DESC LIMIT 1),(SELECT c.customer_name FROM tbl_service_provider_client c WHERE c.id = ab.customer_id ORDER BY ID DESC LIMIT 1)) , ab.customer_name) AS customer_name,date_format(`date`, '%Y-%m-%d %H:%i:%s') as date,date_format(ab.end_datetime, '%Y-%m-%d %H:%i:%s') as end_datetime,ab.id as appointment_id,b.name as business_address,ab.total_duration from tbl_appointment_booking ab LEFT JOIN tbl_business_location b ON b.id = ab.business_location_id INNER JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id where " + condition + " GROUP BY ab.id ORDER BY ab.id DESC", function (err, result) {
            if (!err) {
                var total_earning = parseFloat(0.00);
                if (result[0] != undefined && result[0] != "") {
                    asyncloop(result, function (item, next) {
                        if (item) {
                            const date = moment(item.date).format('YYY-MM-DD')
                            // make time slot be 12h format always, regardless of what in DB
                            item.slot_time = moment(`${item.slot_time}`).format('hh:mm A')

                            var sql = con.query("select s.service_name,s.duration from tbl_appointment_booking_detail abd LEFT JOIN tbl_service s ON s.id = abd.service_id where abd.appointment_id = " + item.id + " AND abd.type = 'Service'", function (err1, result1) {
                                if (!err1 && result1[0] != undefined && result1[0] != "") {
                                    item.service_name = result1;
                                } else {
                                    item.service_name = [];
                                }
                                total_earning = total_earning + parseFloat(item.total_amount);
                                next()
                            });
                        } else {
                            next();
                        }
                    }, function () {
                        var sql = con.query("select IF(show_home_target = 'Yearly',yearly_amount,IF(show_home_target = 'Monthly',monthly_amount,IF(show_home_target = 'Weekly',weekly_amount,hourly_amount))) as amount,show_home_target,service_provider_id from tbl_service_provider_target where service_provider_id = " + service_provider_id + " AND is_deleted = '0'", function (error, results) {
                            if (!error && results[0] != undefined && results[0] != "") {
                                var target = results[0];
                                target.estimate_earning = parseFloat(total_earning).toFixed(2);
                                con.query("SELECT count(id) as total_notification FROM tbl_notification WHERE receiver_id = '" + service_provider_id + "' AND receiver_type = 'service_provider' AND status = 'Unread' GROUP BY receiver_id", function (err, notifications) {
                                    var total_unread_notification = (!err && notifications[0] != undefined) ? notifications[0].total_notification : 0;
                                    callback('1', 'Home screen data found successfully', { target: target, data: result, total_unread_notification: total_unread_notification });
                                });
                            } else {
                                con.query("SELECT count(id) as total_notification FROM tbl_notification WHERE receiver_id = '" + service_provider_id + "' AND receiver_type = 'service_provider' AND status = 'Unread' GROUP BY receiver_id", function (err, notifications) {
                                    var total_unread_notification = (!err && notifications[0] != undefined) ? notifications[0].total_notification : 0;
                                    var target = { estimate_earning: parseFloat(total_earning).toFixed(2) };
                                    callback('1', 'Home screen data found successfully', { target: target, data: result, total_unread_notification: total_unread_notification });
                                });
                            }
                        });
                    });
                } else {
                    var sql = con.query("select IF(show_home_target = 'Yearly',yearly_amount,IF(show_home_target = 'Monthly',monthly_amount,IF(show_home_target = 'Weekly',weekly_amount,hourly_amount))) as amount,show_home_target,service_provider_id from tbl_service_provider_target where service_provider_id = " + service_provider_id + " AND is_deleted = '0'", function (error, results) {
                        if (!error && results[0] != undefined && results[0] != "") {
                            var target = results[0];
                            target.estimate_earning = parseFloat(total_earning).toFixed(2);
                            con.query("SELECT count(id) as total_notification FROM tbl_notification WHERE receiver_id = '" + service_provider_id + "' AND receiver_type = 'service_provider' AND status = 'Unread' GROUP BY receiver_id", function (err, notifications) {
                                var total_unread_notification = (!err && notifications[0] != undefined) ? notifications[0].total_notification : 0;
                                callback('1', 'Home screen data found successfully', { target: target, data: [], total_unread_notification: total_unread_notification });
                            });
                        } else {
                            con.query("SELECT count(id) as total_notification FROM tbl_notification WHERE receiver_id = '" + service_provider_id + "' AND receiver_type = 'service_provider' AND status = 'Unread' GROUP BY receiver_id", function (err, notifications) {
                                var total_unread_notification = (!err && notifications[0] != undefined) ? notifications[0].total_notification : 0;
                                var target = null;
                                callback('1', 'Home screen data found successfully', { target: target, data: [], total_unread_notification: total_unread_notification });
                            });
                        }
                    });
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for cancel credit product quantity
     */
    cancelcreditproductquantity: function (request, callback) {
        con.query("SELECT * FROM tbl_appointment_booking_detail WHERE appointment_id = '" + request.appointment_id + "' AND type = 'Product' AND is_deleted = '0'", function (error, products, fields) {
            if (!error && products[0] != undefined) {
                asyncloop(products, function (item, next) {
                    common.debit_wallet("tbl_subproduct", "id = '" + item.subproduct_id + "' AND product_id = '" + item.product_id + "'", "used_quantity", item.quantity, function (balance) {
                        common.credit_wallet("tbl_subproduct", "id = '" + item.subproduct_id + "' AND product_id = '" + item.product_id + "'", "remaining_quantity", item.quantity, function (balance) {
                            next();
                        });
                    });
                }, function () {
                    callback(true);
                });
            } else {
                callback(true);
            }
        });
    },

    /**
     * Function for cancel appointment
     */
    cancel_appointment: function (request, callback) {
        var sql = con.query("select * from tbl_appointment_booking where id = '" + request.appointment_id + "'", function (err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    if (result[0].booking_status == 'Cancelled') {
                        callback('0', 'This appointment has already been cancelled', true);
                    } else if (result[0].booking_status == 'Completed') {
                        callback('0', 'This appointment has already been completed', true);
                    } else if (result[0].booking_status == 'Paid') {
                        callback('0', 'This appointment has already been completed and paid', true);
                    } else if (result[0].booking_status == 'No Show') {
                        callback('0', 'This appointment has already been cancelled and on show', true);
                    } else {
                        var updateparam = {
                            booking_status: 'Cancelled',
                            is_cancel: '1',
                            payment_status: 'paid',
                            payment_datetime: datetime.create().format('Y-m-d H:M:S'),
                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                            cancel_id: (request.cancel_id != undefined && request.cancel_id != "") ? request.cancel_id : "0",
                            cancel_reason: (request.cancel_reason != undefined && request.cancel_reason != "") ? request.cancel_reason : "",
                        }
                        common.update_data('tbl_appointment_booking', request.appointment_id, updateparam, function (is_cancel) {
                            if (is_cancel) {
                                service_provider.cancelcreditproductquantity(request, function (iscredit) {
                                    if (result[0].payment_from == 'Wallet') {
                                        var wallet_insert = {
                                            user_id: result[0].user_id,
                                            card_id: 0,
                                            order_id: request.appointment_id,
                                            amount: result[0].wallet_amount,
                                            title: 'refund cancel booking',
                                            transaction_id: '',
                                            status: 'credit'
                                        }
                                        require('../user/user_model').insert_wallet_history(wallet_insert, function (is_wallet_insert) {
                                            var update_param = {
                                                is_refund: '1',
                                                refund_amount: result[0].wallet_amount,
                                                refund_id: 'Blookd' + datetime.create().format('YmdHMS'),
                                                refund_status: 'succeeded',
                                                refund_object: {},
                                                refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                            }
                                            common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function (is_refuned) {
                                                var message = {
                                                    sender_id: request.service_provider_id,
                                                    sender_type: USER_TYPE.SERVICE_PROVIDER,
                                                    receiver_id: result[0].user_id,
                                                    type: 'user',
                                                    receiver_type: USER_TYPE.USER,
                                                    message: 'Your booking has been cancelled',
                                                    title: 'Cancelled Appointment Booking',
                                                    isaction_id: request.appointment_id,
                                                    tag: 'booking',
                                                }
                                                require('../../../config/common').prepare_customer_notification(result[0].user_id, message, function (notification) {
                                                    callback('1', 'Appointment cancelled successfully', true);
                                                });
                                            });
                                        });
                                    } else {
                                        var cancellation_charges_amount = parseFloat(result[0].merchant_fees_amount) + parseFloat(result[0].app_fees_amount);
                                        var refund_amount = parseFloat(result[0].total_amount) - cancellation_charges_amount;
                                        const paymentId = result[0].transaction_id ?? result[0].payment_intent_id
                                        if (paymentId) {
                                            stripe.createChargeRefund(paymentId, refund_amount, function (code, message, refund) {
                                                if (code == '1') {
                                                    var update_param = {
                                                        is_refund: '1',
                                                        refund_amount : refund_amount,
                                                        refund_id: (refund != null) ? refund.charge : "",
                                                        refund_status: (refund != null) ? refund.status : "successed",
                                                        refund_object: (refund != null) ? JSON.stringify(refund) : "",
                                                        refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                                    }
                                                    common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function (is_refuned) {
                                                        var message = {
                                                            sender_id: request.service_provider_id,
                                                            sender_type: USER_TYPE.SERVICE_PROVIDER,
                                                            receiver_id: result[0].user_id,
                                                            type: 'user',
                                                            receiver_type: USER_TYPE.USER,
                                                            message: 'Your booking has been cancelled',
                                                            title: 'Cancelled Appointment Booking',
                                                            isaction_id: request.appointment_id,
                                                            tag: 'booking',
                                                        }
                                                        require('../../../config/common').prepare_customer_notification(result[0].user_id, message, function (notification) {
                                                            callback('1', 'Appointment cancelled successfully', true);
                                                        })
                                                    })
                                                } else {
                                                    callback(code, message, refund);
                                                }
                                            });
                                        } else {
                                            var update_param = {
                                                is_refund: '1',
                                                refund_amount: 0,
                                                refund_id: "",
                                                refund_status: "successed",
                                                refund_object: "",
                                                refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                            }
                                            common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function (is_refuned) {
                                                var message = {
                                                    sender_id: request.service_provider_id,
                                                    sender_type: USER_TYPE.SERVICE_PROVIDER,
                                                    receiver_id: result[0].user_id,
                                                    type: 'user',
                                                    receiver_type: USER_TYPE.USER,
                                                    message: 'Your booking has been cancelled',
                                                    title: 'Cancelled Appointment Booking',
                                                    isaction_id: request.appointment_id,
                                                    tag: 'booking',
                                                }
                                                require('../../../config/common').prepare_customer_notification(result[0].user_id, message, function (notification) {
                                                    callback('1', 'Appointment cancelled successfully', true);
                                                })
                                            })
                                        }
                                    }
                                });
                            } else {
                                callback('0', t('restapi_globals_error'), null);
                            }
                        });
                    }
                } else {
                    callback('2', "Appointment not found", null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for get cancel reason list
     */
    cancel_reason_list: function (request, callback) {
        var sql = con.query("select * from tbl_cancel_reason where status = 'Active' AND user_type = '"+request.user_type+"' AND is_deleted = '0'", function (err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else if (result[0] != undefined && result[0] != "") {
                callback('1', t('restapi_cancelreason_detailfound'), result);
            } else {
                callback('0', t('restapi_cancelorder_notfound'), null);
            }
        })
    },

    /**
     * Function for get cancel booth rent reason list
     */
    cancel_boothrent_reason_list: function (request, callback) {
        var sql = con.query("select * from tbl_cancel_reason where status = 'Active' AND user_type = '"+request.user_type+"' AND is_deleted = '0'", function (err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else if (result[0] != undefined && result[0] != "") {
                callback('1', t('restapi_cancelreason_detailfound'), result);
            } else {
                callback('0', t('restapi_cancelorder_notfound'), null);
            }
        })
    },

    /**
     * Function for booking details
     */
    booking_details: function (request, callback) {
        var sql = con.query("select ab.*,date_format(ab.date,'%Y-%m-%d') as date,date_format(ab.end_datetime, '%Y-%m-%d %H:%i:%s') as end_datetime,date_format(ab.reminder_date_time,'%Y-%m-%d %H:%i:%s') as reminder_date_time,ab.id as appointment_id,b.name as business_address,IF(ab.customer_name = '',IF(ab.user_id = '0',(SELECT c.customer_name FROM tbl_service_provider_client c WHERE c.id = ab.client_id ORDER BY ID DESC LIMIT 1),(SELECT CONCAT (first_name,' ',last_name) as name FROM tbl_user WHERE id = ab.user_id ORDER BY ID DESC LIMIT 1)),ab.customer_name) AS customer_name,IF(ab.user_id = '0',(SELECT CONCAT('" + global.S3_BUCKET_ROOT + global.USER_IMAGE + "','',c.profile_image) FROM tbl_service_provider_client c WHERE c.id = ab.client_id),(SELECT CONCAT('" + global.S3_BUCKET_ROOT + global.USER_IMAGE + "',profile_image) FROM tbl_user WHERE id = ab.user_id ORDER BY ID DESC LIMIT 1)) AS customer_image,IFNULL(IF(ab.user_id = '0',(SELECT avg_rating FROM tbl_service_provider_client c WHERE c.id = ab.client_id ORDER BY ID DESC LIMIT 1),(SELECT avg_rating FROM tbl_user WHERE id = ab.user_id ORDER BY ID DESC LIMIT 1)),'0.00') as user_avg_rating from tbl_appointment_booking ab LEFT JOIN tbl_business_location b ON b.id = ab.business_location_id  where ab.id = " + request.appointment_id + "", function (err, result) {
            if (!err) {
                if (result[0] != undefined && result[0] != "") {
                    con.query("SELECT *,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "',image_name) as service_complete_image FROM tbl_image WHERE type_id = '"+request.appointment_id+"' AND type = 'quick_appointment_image' AND is_deleted = '0'",function(bookimgerr,bookingimages){
                        result[0].booking_images = (!bookimgerr && bookingimages.length > 0) ? bookingimages : [];
                        con.query("SELECT *,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "',image_name) as service_complete_image FROM tbl_image WHERE type_id = '" + request.appointment_id + "' AND type = 'complete_appointment' AND is_deleted = '0'", function (imgerr, images) {
                            result[0].complete_images = (!imgerr && images.length > 0) ? images : [];
                            var sql = con.query("select abd.*,s.price as service_price,s.service_name,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',s.service_image) as service_image,s.duration from tbl_appointment_booking_detail abd LEFT JOIN tbl_service s ON s.id = abd.service_id where abd.appointment_id = " + request.appointment_id + " AND abd.type = 'Service' group by abd.id", function (error, results) {
                                if (!error && results[0] != undefined && results[0] != "") {
                                    result[0].services = results;
                                } else {
                                    result[0].services = [];
                                }
                                var sql = con.query("select abd.*,s.price as product_price,p.product_name,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',p.product_image) as product_image from tbl_appointment_booking_detail abd LEFT JOIN tbl_product p ON p.id = abd.product_id LEFT JOIN tbl_subproduct s ON s.product_id = abd.product_id where abd.appointment_id = " + request.appointment_id + " AND abd.type = 'Product' group by abd.id", function (error1, product_result) {
                                    if (!error1 && product_result[0] != undefined && product_result[0] != "") {
                                        result[0].product = product_result;
                                    } else {
                                        result[0].product = [];
                                    }
                                    logger.info({ type: "Service Provider Booking details", result: result[0]})
                                    callback('1', 'Booking detail found successfully', result[0]);
                                });
                            });
                        });
                    });
                } else {
                    callback('0', 'Booking detail not found', null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for client appointment detail
     */
    client_appointment_details: function (request, callback) {
        var sql = con.query("select ab.*,date_format(ab.date,'%Y-%m-%d %H:%i:%s') as date,date_format(ab.end_datetime, '%Y-%m-%d %H:%i:%s') as end_datetime,ab.id as appointment_id,b.name as business_address from tbl_appointment_booking ab LEFT JOIN tbl_business_location b ON b.id = ab.business_location_id  where ab.id = " + request.appointment_id + "", function (err, result) {
            if (!err) {
                if (result.length > 0) {
                    service_provider.get_appointment_image(request, function (images) {
                        result[0].appointment_image = images;
                        var sql = con.query("select abd.*,s.price as service_price,s.service_name,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',s.service_image) as service_image from tbl_appointment_booking_detail abd LEFT JOIN tbl_service s ON s.id = abd.service_id where abd.appointment_id = " + request.appointment_id + " AND abd.type = 'Service' group by abd.id", function (error, results) {
                            if (!error && results[0] != undefined && results[0] != "") {
                                result[0].services = results;
                            } else {
                                result[0].services = [];
                            }
                            callback('1', 'Appointment detail found successfully', result);
                        })
                    })
                } else {
                    callback('0', 'Appointment detail not found', null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * This function is used for get sp upload image
     * @param {appointment_id} request
     * @param {*} callback
     */
    get_appointment_image: function (request, callback) {
        var sql = con.query("select CONCAT('" + global.S3_BUCKET_ROOT + global.SP_IMAGE + "','',pi.image_name) as image_name from tbl_image pi where type_id = " + request.appointment_id + " AND type = 'complete_appointment' AND is_deleted = '0'", function (err, result) {
            if (!err && result.length > 0) {
                callback(result)
            } else {
                callback([])
            }
        })
    },

    /*
    ** Function for upload customer bio
    */
    uploadcustomerbio: function (request, callback) {
        var update_param = {
            description_notes: (request.description_notes != undefined && request.description_notes != "") ? request.description_notes : '',
            updatetime: datetime.create().format('Y-m-d H:M:S'),
        }
        common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function (is_status_update) {
            if (is_status_update) {
                if (request.image != undefined && request.image != "" && request.image.length > 0) {
                    asyncloop(request.image, function (item, next) {
                        if (item) {
                            var product_image = {
                                image_name: item.image_name,
                                type_id: request.appointment_id,
                                type: 'complete_appointment',
                                is_deleted: '0',
                                insertdate: datetime.create().format('Y-m-d H:M:S'),
                            }
                            con.query("INSERT INTO tbl_image SET ? ", product_image, function (err, result) {
                                if (err) { console.log("if if error image", err); }
                                next();
                            });
                        } else {
                            next();
                        }
                    }, function () {
                        callback('1', t('restapi_appointmentCompleted_success'), null);
                    });
                } else {
                    callback('1', t('restapi_appointmentCompleted_success'), null);
                }
            } else {
                console.log("is update faield");
                callback('0', t('restapi_globals_error'), null);
            }
        });
    },

    /*
    ** Function for change booking status
    */
    change_booking_status: function (request, callback) {
        service_provider.booking_details(request, function (msgcode, messages, response) {
            if (msgcode == '1' && response != null) {
                if (request.status == 'Completed' && response.payment_mode == 'pay_later') {
                    if (response.payment_from == 'Wallet') {
                        var update_param = {
                            booking_status: request.status,
                            payment_status: 'paid',
                            description_notes: (request.description_notes != undefined && request.description_notes != "") ? request.description_notes : '',
                            payment_datetime: datetime.create().format('Y-m-d H:M:S'),
                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                        }
                        common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function (is_status_update) {
                            if (is_status_update) {
                                service_provider.booking_details(request, function (msgcode, messages, response) {
                                    if (msgcode == '1' && response.user_id != 0) {
                                        var message = {
                                            sender_id: request.service_provider_id,
                                            sender_type: USER_TYPE.SERVICE_PROVIDER,
                                            receiver_id: response.user_id,
                                            type: 'user',
                                            receiver_type: USER_TYPE.USER,
                                            message: 'Your booking has been completed please add your reviews for service.',
                                            title: 'Completed Appointment Booking',
                                            isaction_id: request.appointment_id,
                                            tag: 'booking_completed',
                                        }
                                        require('../../../config/common').prepare_customer_notification(response.user_id, message, function (notification) {
                                            if (request.status == 'Completed') {
                                                if (request.image != undefined && request.image.length > 0) {
                                                    asyncloop(request.image, function (item, next) {
                                                        if (item) {
                                                            var product_image = {
                                                                image_name: item.image_name,
                                                                type_id: request.appointment_id,
                                                                type: 'complete_appointment',
                                                                is_deleted: '0',
                                                                insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                            }
                                                            con.query("INSERT INTO tbl_image SET ? ", product_image, function (err, result) {
                                                                if (err) { console.log("if if error image", err); }
                                                                next();
                                                            });
                                                        } else {
                                                            next();
                                                        }
                                                    }, function () {
                                                        callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                                    });
                                                } else {
                                                    callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                                }
                                            } else {
                                                callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                            }
                                        });
                                    } else {
                                        callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                    }
                                });
                            } else {
                                callback('0', t('restapi_globals_error'), null);
                            }
                        })
                    } else {
                        require("../../../config/payment").captureStripeCharge(response.transaction_id, 0, function (code, message, chargedata) {
                            if (code == '1') {
                                var update_param = {
                                    booking_status: request.status,
                                    payment_status: 'paid',
                                    description_notes: (request.description_notes != undefined && request.description_notes != "") ? request.description_notes : '',
                                    payment_datetime: datetime.create().format('Y-m-d H:M:S'),
                                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                                }
                                common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function (is_status_update) {
                                    if (is_status_update) {
                                        service_provider.booking_details(request, function (msgcode, messages, response) {
                                            if (msgcode == '1' && response.user_id != 0) {
                                                var message = {
                                                    sender_id: request.service_provider_id,
                                                    sender_type: USER_TYPE.SERVICE_PROVIDER,
                                                    receiver_id: response.user_id,
                                                    type: 'user',
                                                    receiver_type: USER_TYPE.USER,
                                                    message: 'Your booking has been completed please add your reviews for service.',
                                                    title: 'Completed Appointment Booking',
                                                    isaction_id: request.appointment_id,
                                                    tag: 'booking_completed',
                                                }
                                                require('../../../config/common').prepare_customer_notification(response.user_id, message, function (notification) {
                                                    if (request.status == 'Completed') {
                                                        if (request.image != undefined && request.image.length > 0) {
                                                            asyncloop(request.image, function (item, next) {
                                                                if (item) {
                                                                    var product_image = {
                                                                        image_name: item.image_name,
                                                                        type_id: request.appointment_id,
                                                                        type: 'complete_appointment',
                                                                        is_deleted: '0',
                                                                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                                    }
                                                                    con.query("INSERT INTO tbl_image SET ? ", product_image, function (err, result) {
                                                                        if (err) { console.log("if else imaerror", err); }
                                                                        next();
                                                                    });
                                                                } else {
                                                                    next();
                                                                }
                                                            }, function () {
                                                                callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                                            });
                                                        } else {
                                                            callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                                        }
                                                    } else {
                                                        callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                                    }
                                                });
                                            } else {
                                                callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                            }
                                        });
                                    } else {
                                        callback('0', t('restapi_globals_error'), null);
                                    }
                                });
                            } else {
                                callback(code, message, chargedata);
                            }
                        });
                    }
                } else {
                    con.query("SELECT * FROM tbl_service_provider WHERE id = '" + request.service_provider_id + "'", function (error1, serviceprovider, fields) {
                        var moment = require('moment');
                        var booking_date = response.date;
                        var current_date = moment().format("YYYY-MM-DD");
                        if (!error1 && serviceprovider[0] != undefined && serviceprovider[0].timezone != 'UTC') {
                            var moment = require('moment-timezone');
                            booking_date = moment(response.date + " " + response.slot_time, "YYYY-MM-DD HH:mm:ss").tz(serviceprovider[0].timezone).format("YYYY-MM-DD");
                            current_date = moment().tz(serviceprovider[0].timezone).format("YYYY-MM-DD");
                        }
                        con.query("SELECT * FROM tbl_appointment_booking WHERE service_provider_id = '" + request.service_provider_id + "' AND is_deleted = '0' AND booking_status = 'In Progress'", function (error, runningbooking) {
                            if (request.status == 'In Progress' && booking_date != current_date) {
                                callback('0', t('restapi_firststartpastfutureservice_error'), null);
                            } else if (request.status == 'In Progress' && runningbooking[0] != undefined) {
                                callback('0', t('restapi_firstcompleterunningbooking_error'), null);
                            } else {
                                var update_param = {
                                    booking_status: request.status,
                                    description_notes: (request.description_notes != undefined && request.description_notes != "") ? request.description_notes : '',
                                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                                }
                                common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function (is_status_update) {
                                    if (is_status_update) {
                                        service_provider.booking_details(request, function (msgcode, messages, response) {
                                            if (msgcode == '1' && response.user_id != 0) {
                                                var notificationtag = 'booking';
                                                if (request.status == 'Completed') {
                                                    notificationtag = 'booking_completed';
                                                } else if (request.status == 'Paid') {
                                                    notificationtag = 'booking_paid';
                                                }
                                                var message = {
                                                    sender_id: request.service_provider_id,
                                                    sender_type: USER_TYPE.SERVICE_PROVIDER,
                                                    receiver_id: response.user_id,
                                                    type: 'user',
                                                    receiver_type: USER_TYPE.USER,
                                                    message: (request.status == 'Completed') ? 'Your booking has been completed please add your reviews for service.' : 'Reschedule request has been ' + request.status,
                                                    title: request.status + ' Reschedule Request',
                                                    isaction_id: request.appointment_id,
                                                    tag: notificationtag,
                                                }
                                                require('../../../config/common').prepare_customer_notification(response.user_id, message, function (notification) {
                                                    if (request.status == 'Completed') {
                                                        if (request.image != undefined && request.image.length > 0) {
                                                            asyncloop(request.image, function (item, next) {
                                                                var product_image = {
                                                                    image_name: item.image_name,
                                                                    type_id: request.appointment_id,
                                                                    type: 'complete_appointment',
                                                                    is_deleted: '0',
                                                                    insertdate: datetime.create().format('Y-m-d H:M:S'),
                                                                }
                                                                con.query("INSERT INTO tbl_image SET ? ", product_image, function (err, result) {
                                                                    if (err) { console.log("else image err", err); }
                                                                    next();
                                                                });
                                                            }, function () {
                                                                callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                                            });
                                                        } else {
                                                            callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                                        }
                                                    } else {
                                                        callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                                    }
                                                });
                                            } else {
                                                callback(msgcode, t('restapi_appointment' + request.status + '_success'), response)
                                            }
                                        });
                                    } else {
                                        callback('0', t('restapi_globals_error'), null);
                                    }
                                });
                            }
                        });
                    });
                }
            } else {
                callback(msgcode, messages, response)
            }
        });
    },

    /*
    ** Function for get service history
    */
    service_history: function (request, callback) {
        service_provider.get_client_detail(request, function (code, message, client_detail) {
            if (client_detail != null) {
                ///var condition = " ab.is_only_product = '0' AND ab.is_product = '0' AND ab.service_provider_id = '"+request.user_id+"'";
                var condition = "abd.service_id > '0' AND abd.product_id = '0' AND ab.service_provider_id = '" + request.user_id + "'";
                if (client_detail.is_user_app != '0') {
                    condition += " AND ((ab.user_id = '" + client_detail.is_user_app + "') OR  (ab.client_id = '" + client_detail.id + "' OR ab.customer_id = '" + client_detail.id + "'))";
                } else {
                    condition += " AND (ab.client_id = '" + client_detail.id + "' OR ab.customer_id = '" + client_detail.id + "')";
                }
                var sql = con.query("select date_format(`date`, '%Y-%m-%d') as date,ab.id as appointment_id from tbl_appointment_booking ab INNER JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id where " + condition + " group by ab.id order by ab.id DESC", function (err, result) {
                    if (!err) {
                        if (result.length > 0) {
                            asyncloop(result, function (item, next) {
                                if (item) {
                                    var sql = con.query("select s.service_name,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',s.service_image) as service_image from tbl_appointment_booking_detail abd LEFT JOIN tbl_service s ON s.id = abd.service_id where abd.appointment_id = " + item.appointment_id + " AND abd.type = 'Service'", function (err1, result1) {
                                        delete (item.appointment_ids)
                                        if (!err1 && result1[0] != undefined && result1[0] != "") {
                                            item.service_name = result1;
                                        } else {
                                            item.service_name = [];
                                        }
                                        next()
                                    })
                                } else {
                                    next()
                                }
                            }, function () {
                                callback('1', t('rest_keywords_service_history_found'), result);
                            })
                        } else {
                            callback('2', t('rest_keywords_service_history_not_found'), null);
                        }
                    } else {
                        callback('0', t('restapi_globals_error') + err, null);
                    }
                })
            } else {
                callback(code, message, client_detail);
            }
        })
    },

    /**
     * Function for get product history
     */
    product_history: function (request, callback) {
        service_provider.get_client_detail(request, function (code, message, client_detail) {
            if (client_detail != null) {
                //var condition = " ab.is_only_product = '1' AND ab.is_product = '1'";
                var condition = "abd.service_id = '0' AND abd.product_id > '0' AND ab.service_provider_id = '" + request.user_id + "'";
                if (client_detail.is_user_app != '0') {
                    condition += " AND ((ab.user_id = '" + client_detail.is_user_app + "') OR  (ab.client_id = '" + client_detail.id + "' OR ab.customer_id = '" + client_detail.id + "'))";
                } else {
                    condition += " AND (ab.client_id = '" + client_detail.id + "' OR ab.customer_id = '" + client_detail.id + "')";
                }
                var sql = con.query("select date_format(`date`, '%Y-%m-%d') as date,ab.id as appointment_id,abd.id as appointment_detail_id,ab.tax,abd.quantity,abd.price from tbl_appointment_booking ab LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id where " + condition + " order by ab.id DESC", function (err, result) {
                    if (!err) {
                        if (result.length > 0) {
                            var final_array = [];
                            asyncloop(result, function (item, next) {
                                if (item) {
                                    var sql = con.query("select ifnull((SELECT price FROM tbl_subproduct s WHERE s.id = abd.subproduct_id),((abd.price * abd.quantity) + " + item.tax + ")) AS price,abd.quantity,p.product_name,IFNULL((select s.size from tbl_subproduct s where s.id = abd.subproduct_id),'') as size,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',p.product_image) as product_image from tbl_appointment_booking_detail abd LEFT JOIN tbl_product p ON p.id = abd.product_id where abd.id = " + item.appointment_detail_id + " AND abd.type = 'Product'", function (err1, result1) {
                                        delete (item.appointment_ids)
                                        //console.log(err1);
                                        if (!err1 && result1[0] != undefined && result1[0] != "") {
                                            item.product = result1[0];
                                        } else {
                                            item.product = {};
                                        }
                                        next()
                                    })
                                } else {
                                    next()
                                }
                            }, function () {
                                callback('1', t('rest_keywords_product_history_found'), result);
                            })
                        } else {
                            callback('2', t('rest_keywords_product_history_not_found'), null);
                        }
                    } else {
                        callback('0', t('restapi_globals_error'), null);
                    }
                })
            } else {
                callback(code, message, client_detail);
            }
        })
    },
    calender_detail: async (request, callback) => {
        const days = service_provider.get_dates_from_month_year(request.month, request.year)
        let calender = [];
        asyncloop(days, (item, next) => {
            service_provider.is_calender_detail(item.date, request.user_id, request, (appointment) => {
                let data = {}
                if (appointment === 1) {
                    data = { date: item.date, is_booked: 1 }
                    calender.push(data)
                    next()
                } else {
                    next()
                }
            })
        }, () => {
            if (calender) {
                callback(1, 'Calender detail found successfully', calender)
            } else {
                callback(0, 'Calender detail not found', [])
            }
        })
    },

  get_dates_from_month_year(month, year) {
    let daysInMonth = new Date(year, month, 0).getDate()
    let i
    let dates = []
    let day = 1
    for (i = 0; i < daysInMonth; i++) {
      let date1 = (day <= '9') ? '0' + day : day
      let date = year + '-' + month + '-' + date1
      dates[i] = { 'date': date }
      day++
    }
    return dates
  },

  is_calender_detail: (date, service_provider_id, request = {}, callback) => {
    if (!request?.timezone_diff) {
      request.timezone_diff = '+00:00'
    }

    con.query(`
      SELECT id
      FROM tbl_appointment_booking ab
      WHERE DATE_FORMAT(
        CONVERT_TZ(CONCAT(ab.date, ' ', ab.slot_time), '+00:00', '${ request.timezone_diff }'),
        '%Y-%m-%d'
      ) = '${ date }'
      AND service_provider_id = ${ service_provider_id }
      AND NOT is_deleted
      AND booking_status NOT IN ('Cancelled', 'No Show')
    `, (err, result) => {
      callback(!err && !!result[0])
    })
  },

    product_purchase_detail: function (request, callback) {
        var sql = con.query("select ab.*,date_format(ab.date,'%Y-%m-%d %H:%i:%s') as date,date_format(ab.insertdate,'%Y-%m-%d %H:%i:%s') as insertdate,date_format(ab.end_datetime, '%Y-%m-%d %H:%i:%s') as end_datetime,ab.id as appointment_id,abd.quantity,ifnull((SELECT price FROM tbl_subproduct s WHERE s.id = abd.subproduct_id),0) AS price,p.product_name,IFNULL((select s.size as size from tbl_subproduct s where s.id = abd.subproduct_id),'') as size,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',p.product_image) as product_image from tbl_appointment_booking ab LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id LEFT JOIN tbl_product p ON p.id = abd.product_id where abd.id = " + request.appointment_detail_id + "", function (err, result) {
            if (!err) {
                if (result.length > 0) {
                    service_provider.multiple_productimage(result[0].product_id, function (image) {
                        if (image != null) {
                            image.push({ multiple_product_image: result[0].product_image });
                            result[0].image = image.reverse();
                        } else {
                            result[0].image = [{ multiple_product_image: result[0].product_image }];
                        }
                        callback('1', 'Product detail found successfully', result[0]);
                    })
                } else {
                    callback('0', 'Appointment detail not found', null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for review list
     */
    review_list: function (request, callback) {
        con.query(`SELECT GET_SERVICE_PROVIDER_RATING(${request.service_provider_id}, NULL) AS avg_ratting`, function (err1, avgratedata, fields) {
            var avgratting = (!err1 && avgratedata.length > 0) ? avgratedata[0].avg_ratting : 0.00;
            var page = request.page - 1;
            var limit = page * global.PER_PAGE;
            var per_page = global.PER_PAGE;
            var servicejoin = "";
            var condition = "ur.service_provider_id = " + request.service_provider_id + "";

            if (request.word) {
                condition += " AND (u.first_name LIKE '" + request.word + "%' OR u.last_name LIKE '" + request.word + "%')";
            }
            if (request.ratting) {
                condition += " AND ur.ratting = " + request.ratting + " ";
            }
            if (request.category_id != undefined && request.category_id != "") {
                servicejoin = "LEFT JOIN tbl_service as ts ON ur.type_id = ts.id AND ur.type = 'Service'";
                condition += " AND ts.category_id = '" + request.category_id + "'";
            }
            if (request.date != undefined && request.date != "") {
                condition += " AND date_format(ur.insertdate,'%Y-%m-%d') = '" + request.date + "'";
            }
            var order_by = " ORDER BY ";
            if (request.sorting) {
                order_by += "ur.id " + request.sorting + " ";
            } else {
                order_by += "ur.id DESC ";
            }
            con.query("select ur.id as review_id,ur.ratting,ur.review,IFNULL(ur.review_reply,'') as review_reply,ur.service_provider_id,ur.user_id,CONCAT('" + global.S3_BUCKET_ROOT + global.USER_IMAGE + "','',u.profile_image) as user_profile_image,u.first_name,u.last_name,date_format(ur.insertdate,'%Y-%m-%d %H:%i:%s') as insert_date from tbl_user_review ur LEFT JOIN tbl_user u ON u.id = ur.user_id " + servicejoin + " where " + condition + " and ur.ratting > 0 group by ur.id " + order_by + " LIMIT " + limit + ", " + per_page + "", function (err, result) {
                if (!err) {
                    if (result.length > 0) {
                        callback('1', 'Review list successfully', { avg_ratting: avgratting, reviewslist: result });
                    } else {
                        callback('2', 'Review list not found', null);
                    }
                } else {
                    console.log(err);
                    callback('0', t('restapi_globals_error'), null);
                }
            });
        });
    },

    /**
     * Function for get my booth reviews list
     */
    getmyboothreviews_list:function(request,callback){
        con.query("SELECT IFNULL(AVG(ratting), 0) as avg_ratting,count(id) as total_reviews FROM tbl_booth_review WHERE service_provider_id = '" + request.service_provider_id + "' AND receiver_type = 'service_provider' AND ratting > 0", function (err1, avgratedata, fields) {
            var avgratting = (!err1 && avgratedata.length > 0) ? avgratedata[0].avg_ratting : 0.00;
            var totalreviews = (!err1 && avgratedata.length > 0) ? avgratedata[0].total_reviews : 0.00;
            var page = request.page - 1;
            var limit = page * global.PER_PAGE;
            var per_page = global.PER_PAGE;
            var condition = "tbr.service_provider_id = '" + request.service_provider_id + "' AND tbr.receiver_type = 'service_provider'";
            if (request.word) {
                condition += " AND (tsp.first_name LIKE '" + request.word + "%' OR tsp.last_name LIKE '" + request.word + "%')";
            }
            if (request.ratting) {
                condition += " AND tbr.ratting = " + request.ratting + " ";
            }
            if (request.date != undefined && request.date != "") {
                condition += " AND date_format(tbr.insertdate,'%Y-%m-%d') = '" + request.date + "'";
            }
            var order_by = " ORDER BY ";
            if (request.sorting) {
                order_by += "tbr.id " + request.sorting + " ";
            } else {
                order_by += "tbr.id DESC ";
            }
            con.query("select tbr.id as review_id,tbr.ratting,tbr.review,IFNULL(tbr.review_reply,'') as review_reply,tbr.service_provider_id,tbr.booth_owner_id,CONCAT('" + global.S3_BUCKET_ROOT + global.SP_IMAGE + "','',tsp.profile_image) as profile_image,tsp.first_name,tsp.last_name,date_format(tbr.insertdate,'%Y-%m-%d %H:%i:%s') as insert_date,IFNULL((SELECT GROUP_CONCAT(name SEPARATOR', ') as names from tbl_review_list WHERE FIND_IN_SET(id,tbr.review_id)),'') as selected_reviews from tbl_booth_review as tbr LEFT JOIN tbl_service_provider as tsp ON tsp.id = tbr.booth_owner_id WHERE " + condition + " and tbr.ratting > 0 group by tbr.id " + order_by + " LIMIT " + limit + ", " + per_page + "", function (err, result) {
                if (!err) {
                    if (result.length > 0) {
                        callback('1', 'Review list successfully', { avg_ratting: avgratting,total_reviews: totalreviews, review_list: result });
                    } else {
                        callback('2', 'Review list not found', null);
                    }
                } else {
                    console.log(err);
                    callback('0', t('restapi_globals_error'), null);
                }
            });
        });
    },

    /**
     * Function for review detail api
     */
    review_detail: function (request, callback) {
        con.query("select ur.id as review_id,(select total_amount from tbl_appointment_booking ab where ab.id = ur.order_id LIMIT 1) as total_amount,ur.ratting,ur.review,IFNULL(ur.review_reply,'') as review_reply,ur.service_provider_id,ur.user_id,CONCAT('" + global.S3_BUCKET_ROOT + global.USER_IMAGE + "','',u.profile_image) as user_profile_image,u.first_name,u.last_name,date_format(ur.insertdate,'%Y-%m-%d %H:%i:%s') as insert_date,ur.order_id from tbl_user_review ur LEFT JOIN tbl_user u ON u.id = ur.user_id where ur.id = " + request.review_id + "", function (err, result) {
            if (!err) {
                if (result.length > 0) {
                    con.query("select abd.*,IF(abd.type='product',(select product_name from tbl_product p where p.id = abd.product_id LIMIT 1),(select service_name from tbl_service s where s.id = abd.service_id LIMIT 1)) AS name,IF(abd.type='product',(select price from tbl_subproduct sp where sp.id = abd.subproduct_id LIMIT 1),(select price from tbl_service s where s.id = abd.service_id LIMIT 1)) AS price from tbl_appointment_booking_detail abd where abd.appointment_id = " + result[0].order_id + "", function (err1, result1) {
                        if (!err1 && result1.length > 0) {
                            result[0].service = result1;
                        } else {
                            result[0].service = [];
                        }
                        callback('1', "Review detail found successfully", result[0]);
                    });
                } else {
                    callback('0', "Review detail not found", null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        });
    },

    /**
     * Function for reply review
     */
    review_reply: function (request, callback) {
        var update_param = {
            review_reply: request.reply,
            updatetime: datetime.create().format('Y-m-d H:M:S')
        }
        common.update_data('tbl_user_review', request.review_id, update_param, function (is_updated) {
            if (is_updated) {
                callback('1', "You have replied successfully", null);
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for sales history
     */
    sales_history: function (request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;

        var having = " having ";
        if (request.word) {
            having += " customer_name LIKE '" + request.word + "%'";
        } else {
            having += " 1=1 ";
        }
        var condition = "ab.service_provider_id = " + request.service_provider_id + " AND abd.service_id = '0' AND abd.product_id > '0'";
        if (request.date != undefined && request.date != "") {
            condition += "AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "' ";
        }
        con.query("select IF(ab.customer_name = '',IF(ab.customer_id = '0', (SELECT c.customer_name FROM tbl_service_provider_client c WHERE c.is_user_app = ab.user_id ORDER BY ID DESC LIMIT 1),(SELECT c.customer_name FROM tbl_service_provider_client c WHERE c.id = ab.customer_id ORDER BY ID DESC LIMIT 1)) , ab.customer_name) AS customer_name,date_format(ab.date,'%Y-%m-%d %H:%i:%s') as date,date_format(ab.end_datetime, '%Y-%m-%d %H:%i:%s') as end_datetime,abd.id,p.product_name,IFNULL((select s.size from tbl_subproduct s where s.id = abd.subproduct_id),'') as size from tbl_appointment_booking ab LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id LEFT JOIN tbl_product p ON p.id = abd.product_id where payment_status = 'paid' AND " + condition + having + " ORDER BY abd.id DESC LIMIT " + limit + ", " + per_page + "", function (err, upcomming) {
            if (!err) {
                con.query("select IF(ab.customer_name = '',IF(ab.customer_id = '0', (SELECT c.customer_name FROM tbl_service_provider_client c WHERE c.is_user_app = ab.user_id ORDER BY ID DESC LIMIT 1),(SELECT c.customer_name FROM tbl_service_provider_client c WHERE c.id = ab.customer_id ORDER BY ID DESC LIMIT 1)) , ab.customer_name) AS customer_name,date_format(ab.date,'%Y-%m-%d %H:%i:%s') as date,date_format(ab.end_datetime, '%Y-%m-%d %H:%i:%s') as end_datetime,abd.id,p.product_name,IFNULL((select s.size from tbl_subproduct s where s.id = abd.subproduct_id),'') as size from tbl_appointment_booking ab LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id LEFT JOIN tbl_product p ON p.id = abd.product_id where payment_status = 'paid' AND " + condition + having + " ORDER BY abd.id DESC LIMIT " + limit + ", " + per_page + "", function (err1, past) {
                    if (!err1) {
                        if (upcomming[0] != "" && upcomming[0] != undefined) {
                            var upcomming_booking = upcomming;
                        } else {
                            var upcomming_booking = [];
                        }
                        if (past[0] != "" && past[0] != undefined) {
                            var past_booking = past;
                        } else {
                            var past_booking = [];
                        }
                        if ((upcomming[0] != "" && upcomming[0] != undefined) || (past[0] != "" && past[0] != undefined)) {
                            callback('1', "Sales history found successfully", { upcomming: upcomming_booking, past: past_booking });
                        } else {
                            callback('2', 'No sales history found', { upcomming: [], past: [] });
                        }
                    } else {
                        callback('0', t('restapi_globals_error'), null);
                    }
                })
            } else {
                callback('0', t('restapi_globals_error') + err, null);
            }
        })

    },

    /**
     * Function for rent booth
     */
    rent_booth: function (request, callback) {
        service_provider.distance(request, 'tbr', function (distance) {
            var condition = "tbr.is_deleted = '0' AND tbr.service_provider_id != " + request.service_provider_id + " AND tbr.status = 'Active' AND tw.is_deleted = '0' AND tw.service_provider_id != " + request.service_provider_id + " AND tw.status = 'Active' AND tw.is_deleted = '0' AND tsp.is_deleted = '0'";
            var having = " having distance < 100 ";
            if(request.workspace_category_id != undefined && request.workspace_category_id != "" && parseInt(request.workspace_category_id) > 0){
                condition += " AND tw.workspace_category_id = '" + request.workspace_category_id + "'";
            }
            if (request.start_radius != undefined && request.start_radius > 0 && request.end_radius != undefined && request.end_radius != "") {
                having = "having distance between " + request.start_radius + " AND " + request.end_radius + "";
            }
            if (request.start_date && request.end_date) {
                condition += " AND ('" + request.start_date + "' BETWEEN  tw.start_availability AND tw.end_availability AND '" + request.end_date + "' BETWEEN  tw.start_availability AND tw.end_availability) ";
                // having += "AND dayname('" + request.start_date + "') = start_day";
            }
            if (request.date) {
                condition += " AND '" + request.date + "' BETWEEN tw.start_availability AND tw.end_availability ";
            }
            if (request.name) {
                condition += " AND LOWER(tw.tag_name) LIKE '%" + request.name.toLowerCase() + "%'";
            }
            if (request.days) {
                condition += " AND twl.day IN (" + '\'' + request.days.split(',').join('\',\'') + '\'' + ")";
            }
            if(request.amenities_id != undefined && request.amenities_id != ""){
                condition += " AND tw.id IN (SELECT workspace_id FROM tbl_workspace_amenties WHERE FIND_IN_SET(amenities_id,'"+request.amenities_id+"') > 0 GROUP BY workspace_id)";
            }
            con.query("select tw.*,tbr.name as location_name,tbr.address,tbr.latitude,IFNULL((SELECT ROUND(count(id),2) FROM tbl_booth_review ur WHERE ur.booth_owner_id = tw.service_provider_id AND ur.receiver_type = 'booth_host' AND ur.ratting > 0),0) AS review,IFNULL((SELECT ROUND(avg(ratting),2) FROM tbl_booth_review ur WHERE ur.booth_owner_id = tw.service_provider_id AND ur.receiver_type = 'booth_host' AND ur.ratting > 0),0.0) AS ratting,tbr.longitude,date_format(tw.start_availability,'%Y-%m-%d') as start_availability,date_format(tw.end_availability,'%Y-%m-%d') as end_availability,tw.booth_location_id,tw.id as workspace_id,twl.status as store_status,twl.open,twl.close,(SELECT GROUP_CONCAT(tmw.name SEPARATOR', ') FROM tbl_master_workspace_category tmw WHERE tmw.is_deleted = '0' AND tmw.id = tw.workspace_category_id) AS workspace_category,twl.day start_day, " + distance + ",tw.service_provider_id as booth_owner_id from tbl_workspace tw LEFT JOIN tbl_workspace_schedule twl ON twl.workspace_id = tw.id LEFT JOIN tbl_booth_renter_location tbr ON tbr.id = tw.booth_location_id LEFT JOIN tbl_service_provider as tsp ON tw.service_provider_id = tsp.id where " + condition + " group by tw.id " + having + " order by distance ASC ", function (err, result) {
                if (!err) {
                    if (result.length > 0) {
                        asyncloop(result, function (item, next) {
                            if (item) {
                                delete item.start_day;
                                var amenitieswahere = "";
                                if(request.amenities_id != undefined && request.amenities_id != ""){
                                    amenitieswahere = "AND tma.id IN (" + '\'' + request.amenities_id.split(',').join('\',\'') + '\'' + ")";
                                }
                                con.query("select CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',tma.image_name) as image_name from tbl_master_amenities tma LEFT JOIN tbl_booth_amenties tbm ON tbm.amenities_id = tma.id where tbm.booth_location_id = " + item.id + amenitieswahere , function (err1, amenities) {
                                    if (!err1 && amenities.length > 0) {
                                        item.amenities = amenities;
                                    } else {
                                        item.amenities = [];
                                    }
                                    con.query("select CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',image_name) as image_name from tbl_image where type = 'workspace_location_image' AND type_id = " + item.workspace_id + "", function (err2, result2) {
                                        if (!err2 && result2.length > 0) {
                                            item.booth_image = result2;
                                            next();
                                        } else {
                                            item.booth_image = [];
                                            next();
                                        }
                                    });
                                });
                            } else {
                                next();
                            }
                        }, function () {
                            callback('1', "Booth location found successfully", result);
                        })
                    } else {
                        callback('0', "Booth location not found", null);
                    }
                } else {
                    callback('0', t('restapi_globals_error'), null);
                }
            });
        })
    },

    /**
     * Function for booth renting
     */
    booth_renting: function (request, callback) {
        var condition = " tbr.is_deleted = '0'  AND tbr.status = 'Active' AND tbr.id = " + request.booth_location_id + " AND tw.is_deleted = '0' AND tw.id = " + request.workspace_id + " AND tw.status = 'Active' ";
        con.query("select tw.*,tw.service_provider_id as booth_owner_id,tbr.address,tbr.latitude,tbr.longitude,date_format(tw.start_availability,'%Y-%m-%d') as start_availability,date_format(tw.end_availability,'%Y-%m-%d') as end_availability,IFNULL((SELECT ROUND(avg(ratting),2) FROM tbl_booth_review ur WHERE ur.booth_owner_id = tw.service_provider_id AND ur.receiver_type = 'booth_host' AND ur.ratting > 0),0.0) AS ratting,IFNULL((SELECT ROUND(count(id),2) FROM tbl_booth_review ur WHERE ur.booth_owner_id = tw.service_provider_id AND ur.receiver_type = 'booth_host' AND ur.ratting > 0),0) AS review,(SELECT GROUP_CONCAT(tmw.name SEPARATOR', ') FROM tbl_master_workspace_category tmw WHERE tmw.is_deleted = '0' AND tmw.id = tw.workspace_category_id) AS workspace_category,tbr.id as booth_location_id,tw.id as workspace_id,tsp.first_name,tsp.last_name,tsp.email,CONCAT(tsp.country_code,tsp.phone) as phonenumber,CONCAT('"+global.S3_BUCKET_ROOT + global.SP_IMAGE+"',tsp.profile_image) as profile_image,CONCAT('"+global.BASE_URL+"','workspace-detail/',to_base64(tw.id)) as shareurl from tbl_workspace tw LEFT JOIN tbl_booth_renter_location tbr ON tw.booth_location_id = tbr.id LEFT JOIN tbl_service_provider as tsp ON tw.service_provider_id = tsp.id where " + condition + " group by tw.id", function (err, result) {
            if (!err) {
                if (result.length > 0) {
                    con.query("select CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',image_name) as image_name from tbl_image where type = 'workspace_location_image' AND type_id = " + request.workspace_id + "", function (err1, result1) {
                        if (!err1 && result1.length > 0) {
                            result[0].booth_image = result1;
                        } else {
                            result[0].booth_image = [];
                        }
                        con.query("select tma.id,CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',tma.image_name) as image_name from tbl_master_amenities tma LEFT JOIN tbl_workspace_amenties tbm ON tbm.amenities_id = tma.id where tbm.workspace_id = " + request.workspace_id + "", function (err2, amenities) {
                            if (!err2 && amenities.length > 0) {
                                result[0].amenities = amenities;
                            } else {
                                result[0].amenities = [];
                            }
                            callback('1', "Booth renting found successfully", result[0]);
                        })
                    })
                } else {
                    callback('0', "Booth renting not found", null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for booth rent date
     */
    available_booth_rent_date: function (request, callback) {
        const days = service_provider.get_dates_from_month_year(request.month, request.year);
        var calender = [];
        var moment = require('moment-timezone');
        var current_date = moment().tz(request.timezone).format("YYYY-MM-DD");
        var current_month = moment().tz(request.timezone).format("MM");
        asyncloop(days, function (item, next) {
            if (item.date < current_date && request.month == current_month) {
                next();
            } else {
                service_provider.booth_rent_detail(item.date,request,function(is_active,is_booked){
                    if(is_active == '1'){
                        request.date = item.date;
                        service_provider.booth_rent_timeslot(request, function(responsecode,responseMsg,timesloats) {
                            if (responsecode == 1 && timesloats.length > 0) {
                                calender.push({ date: item.date, is_booked: 0 });
                                next();
                            } else {
                                calender.push({ date: item.date, is_booked: 1 });
                                next();
                            }
                        });
                    } else {
                        next();
                    }
                });
            }
        }, function () {
            if (calender != "") {
                callback('1', 'Calender detail found successfully', calender)
            } else {
                callback('0', 'No available dates for this month', [])
            }
        });
    },

    /**
     * Function for booth rent detail
     */
    booth_rent_detail: function (date, request, callback) {
        con.query("select id,IFNULL((SELECT 1 FROM tbl_rent_booth rb WHERE DATE_FORMAT(CONVERT_TZ(CONCAT(rb.date,' ',rb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + date + "' AND rb.booth_location_id = " + request.booth_location_id + " AND rb.workspace_id = " + request.workspace_id + " order by id desc LIMIT 1),0) as is_booked from tbl_workspace w where '" + date + "' BETWEEN w.start_availability AND w.end_availability AND w.id = " + request.workspace_id + "", function (err, result) {
            if (!err && result[0] != undefined && result[0] != "") {
                callback(1, result[0].is_booked);
            } else {
                callback(0);
            }
        });
    },

    /**
     * Function for get workspace slot date wise
     */
    booth_rent_timeslot: function (request, callback) {
        //IFNULL((SELECT id FROM tbl_rent_booth WHERE is_deleted = '0' AND booth_location_id = '"+request.booth_location_id+"' AND workspace_id = '"+request.workspace_id+"' AND date = '"+request.date+"' AND start_slot_time = tws.open AND end_slot_time = tws.close AND status NOT IN ('Rejected','Completed','Cancelled') limit 1),0) as is_booked
        con.query("select tws.id workspace_schedule_id,tws.service_provider_id as booth_owner_id,ROUND((SELECT hourly_rate FROM tbl_workspace WHERE id = " + request.workspace_id + ") * (TIMESTAMPDIFF(MINUTE,CONCAT('"+request.date+"',' ',DATE_FORMAT(CONVERT_TZ(CONCAT('"+request.date+"',' ',tws.open),'+00:00','"+request.timezone_diff+"'),'%H:%i:%s')),CONCAT('"+request.date+"',' ',DATE_FORMAT(CONVERT_TZ(CONCAT('"+request.date+"',' ',tws.close),'+00:00','"+request.timezone_diff+"'),'%H:%i:%s')))/60),2) as price,day,tws.status as slot_status,tws.open,tws.close FROM tbl_workspace_schedule as tws where tws.is_deleted = '0' AND tws.booth_location_id = " + request.booth_location_id + " AND tws.workspace_id = " + request.workspace_id + " having tws.day = DAYNAME('" + request.date + "') AND price > 0", function (err, result) {
            if (!err) {
                if (result.length > 0) {
                    var moment = require('moment-timezone');
                    var timesloats = [];
                    var current_date = moment().tz(request.timezone).format("YYYY-MM-DD");
                    var current_time = moment().tz(request.timezone).format("HH:mm");
                    var current_datetime = moment().tz(request.timezone).format("YYYY-MM-DD HH:mm");
                    var utcdate = moment(request.date + " " +current_time,'YYYY-MM-DD HH:mm',request.timezone).utc().format('YYYY-MM-DD');
                    asyncloop(result, function (item, next) {
                        var slot_starttime = moment.utc(request.date + " " + item.open, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("YYYY-MM-DD HH:mm");
                        var slotstarttime = moment.utc(request.date + " " + item.open, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("HH:mm");
                        if(item.open > item.close){
                            var slot_endtime = moment.utc(moment(request.date).add(1,'day').format("YYYY-MM-DD")+ " " + item.close, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("YYYY-MM-DD HH:mm");
                            var slotendtimelocal = moment.utc(moment(utcdate).add(1,'day').format("YYYY-MM-DD") + " " + item.close, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("YYYY-MM-DD");
                        } else {
                            var slot_endtime = moment.utc(request.date+ " " + item.close, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("YYYY-MM-DD HH:mm");
                            var slotendtimelocal = moment.utc(utcdate + " " + item.close, 'YYYY-MM-DD HH:mm:ss').tz(request.timezone).format("YYYY-MM-DD");
                        }
                        con.query("select * from tbl_rent_booth where is_deleted = '0' AND booth_location_id = '"+request.booth_location_id+"' AND workspace_id = '"+request.workspace_id+"' AND status NOT IN ('Rejected','Completed','Cancelled') AND (('"+slot_starttime+"' >= DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i') AND '"+slot_starttime+"' < DATE_FORMAT(CONVERT_TZ(CONCAT(end_date,' ',end_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i')) OR ('" + slot_endtime + "' BETWEEN DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i') AND DATE_FORMAT(CONVERT_TZ(CONCAT(end_date,' ',end_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d %H:%i')))", function (error,bookingdata){
                            if(!error && bookingdata[0]!= undefined){
                                next();
                            } else {
                                if (request.date == current_date) {
                                    if(current_time > slotstarttime){
                                        next();
                                    } else if(request.date < slotendtimelocal){
                                        next();
                                    } else {
                                        timesloats.push(item);
                                        next();
                                    }
                                } else if(request.date < slotendtimelocal){
                                    next();
                                } else {
                                    timesloats.push(item);
                                    next();
                                }
                            }
                        });
                    },function(){
                        callback('1', "Booth slot found successfully", timesloats);
                    });
                } else {
                    callback('2', "No available slot for this date", null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        });
    },

    /**
     * Function for rent booking and payment
     */
    booth_rent_payment: function (request, callback) {
        con.query("SELECT * FROM tbl_service_provider WHERE id = '" + request.booth_owner_id + "'", function (err, spdata,fields) {
            if(!err && spdata[0] != undefined && spdata[0].merchant_account_id != ""){
                if(request.discount == undefined || request.discount == ""){
                    request.discount = 0;
                }
                if(request.cleaning_fees == undefined || request.cleaning_fees == ""){
                    request.cleaning_fees = 0;
                }
                var total_amount = parseFloat(request.total_amount) - parseFloat(request.discount) + parseFloat(request.tax) + parseFloat(request.cleaning_fees);
                if(request.order_type != undefined && request.order_type == 'Booth'){
                    if(request.payment_mode != undefined && request.payment_mode != "" && request.payment_mode == 'Wallet'){
                        var merchant_fees_amount = parseFloat(0);
                    } else {
                        var merchant_fees_amount = (parseFloat(total_amount) * parseFloat(spdata[0].booth_merchant_fees) / 100) + parseFloat(spdata[0].booth_merchant_fees_cents);
                    }
                    var app_fees_amount = (parseFloat(request.total_amount) - parseFloat(request.discount)) * (spdata[0].booth_app_fees) / 100;
                } else {
                    if(request.payment_mode != undefined && request.payment_mode != "" && request.payment_mode == 'Wallet'){
                        var merchant_fees_amount = parseFloat(0);
                    } else {
                        var merchant_fees_amount = (parseFloat(total_amount) * parseFloat(spdata[0].merchant_fees) / 100) + parseFloat(spdata[0].merchant_fees_cents);
                    }
                    var app_fees_amount = (parseFloat(request.sub_total) - parseFloat(request.discount)) * (spdata[0].app_fees) / 100;
                }
                var admin_earning = merchant_fees_amount + app_fees_amount;
                var new_booking = {
                    service_provider_id: request.service_provider_id,
                    booth_location_id: request.booth_location_id,
                    booth_owner_id: request.booth_owner_id,
                    workspace_id: request.workspace_id,
                    date: (request.date) ? request.date : datetime.create().format('Y-m-d'),
                    start_slot_time: (request.start_slot_time) ? request.start_slot_time : '',
                    end_date:(request.end_date) ? request.end_date : datetime.create().format('Y-m-d'),
                    end_slot_time: (request.end_slot_time) ? request.end_slot_time : '',
                    price: request.price,
                    payment_mode: (request.payment_mode != "" && request.payment_mode != undefined) ? request.payment_mode : "Cash",
                    payment_intent_id: (request.payment_intent_id != "" && request.payment_intent_id != undefined) ? request.payment_intent_id : "",
                    transaction_id: (request.transaction_id != "" && request.transaction_id != undefined) ? request.transaction_id : null,
                    payment_status: (request.payment_intent_id != "" && request.payment_intent_id != undefined) ? "Paid" : "Unpaid",
                    sub_total: request.total_amount,
                    tax: request.tax,
                    cleaning_fees:(request.cleaning_fees != undefined && request.cleaning_fees != "") ? request.cleaning_fees : 0.00,
                    total_amount: total_amount,
                    merchant_fees:spdata[0].merchant_fees,
                    merchant_fees_amount:merchant_fees_amount,
                    merchant_fees_cents:spdata[0].merchant_fees_cents,
                    app_fees:spdata[0].app_fees,
                    app_fees_amount:app_fees_amount,
                    admin_earning:admin_earning,
                    booth_owner_earning: parseFloat(total_amount) - admin_earning,
                    is_deleted: '0',
                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                    insertdate: datetime.create().format('Y-m-d H:M:S'),
                }
                common.single_insert_data('tbl_rent_booth', new_booking, function (rent_id) {
                    if (rent_id) {
                        con.query("SELECT * FROM tbl_service_provider WHERE id = '" + request.service_provider_id + "'", function (err, spdata) {
                            if (!err && spdata[0] != undefined) {
                                var message = {
                                    sender_id: request.service_provider_id,
                                    sender_type: USER_TYPE.SERVICE_PROVIDER,
                                    receiver_id: request.booth_owner_id,
                                    type: 'booth_owner',
                                    receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                    message: spdata[0].first_name + ' ' + spdata[0].last_name + ' has sent you new rent booth request',
                                    title: 'New Booth Rent Request',
                                    isaction_id: rent_id.insertId,
                                    tag: 'newrentboothrequest',
                                }
                                require('../../../config/common').prepare_notification(request.booth_owner_id, message, function (notification) {
                                    service_provider.booth_detail({booth_rent_id:rent_id.insertId},function(respcode,respmsg,respdata){
                                        callback('1', 'Booth booked successfully', respdata);
                                    });
                                });
                            } else {
                                service_provider.booth_detail({booth_rent_id:rent_id.insertId},function(respcode,respmsg,respdata){
                                    callback('1', 'Booth booked successfully', respdata);
                                });
                            }
                        });
                    } else {
                        callback('0', t('restapi_globals_error'), false);
                    }
                });
            } else {
                callback('0', "The service provider not added his bank account.", false);
            }
        });
    },

    /**
     * Function for booth history
     */
    booth_history: function (request, callback) {
        var page = request.page - 1;
        var limit = page * global.PER_PAGE;
        var per_page = global.PER_PAGE;

        var condition = "trb.is_deleted = '0' AND trb.service_provider_id = " + request.service_provider_id + "";
        if (request.date) {
            condition += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(trb.date,' ',trb.start_slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
        }
        if(request.location_id != undefined && request.location_id != ""){
            condition += " AND trb.booth_location_id = '"+request.location_id+"'";
        }
        var sort_by = ' order by trb.id DESC';
        if(request.sort_by != undefined && request.sort_by == 'Oldest'){
            sort_by = ' order by trb.id ASC';
        }
        con.query("select trb.*,trb.id as booth_rent_id,tbr.address,tbr.name as booth_location_name,tbr.latitude,tbr.longitude,date_format(trb.start_slot_time,'%H:%i:%s') as start_slot_time,date_format(trb.end_slot_time,'%H:%i:%s') as end_slot_time,trb.status as booking_status,tsp.first_name,tsp.last_name,tsp.email,CONCAT(tsp.country_code,tsp.phone) as phonenumber,CONCAT('"+global.S3_BUCKET_ROOT + global.SP_IMAGE+"',tsp.profile_image) as profile_image from tbl_rent_booth trb LEFT JOIN tbl_booth_renter_location tbr ON tbr.id = trb.booth_location_id LEFT JOIN tbl_service_provider as tsp ON trb.booth_owner_id = tsp.id where trb.status IN ('Waiting','Accepted','Processing') AND " + condition + sort_by +" LIMIT " + limit + ", " + per_page + "", function (err, upcomming) {
            if (!err) {
                con.query("select trb.*,trb.id as booth_rent_id,tbr.address,tbr.name as booth_location_name,tbr.latitude,tbr.longitude,date_format(trb.start_slot_time,'%H:%i:%s') as start_slot_time,date_format(trb.end_slot_time,'%H:%i:%s') as end_slot_time,trb.status as booking_status,tsp.first_name,tsp.last_name,tsp.email,CONCAT(tsp.country_code,tsp.phone) as phonenumber,CONCAT('"+global.S3_BUCKET_ROOT + global.SP_IMAGE+"',tsp.profile_image) as profile_image from tbl_rent_booth trb LEFT JOIN tbl_booth_renter_location tbr ON tbr.id = trb.booth_location_id LEFT JOIN tbl_service_provider as tsp ON trb.booth_owner_id = tsp.id where trb.status IN ('Completed','Rejected','Cancelled') AND " + condition + sort_by +" LIMIT " + limit + ", " + per_page + "", function (err1, past) {
                    if (!err1) {
                        if (upcomming[0] != "" && upcomming[0] != undefined) {
                            var upcomming_booking = upcomming;
                        } else {
                            var upcomming_booking = [];
                        }
                        if (past[0] != "" && past[0] != undefined) {
                            var past_booking = past;
                        } else {
                            var past_booking = [];
                        }
                        if ((upcomming[0] != "" && upcomming[0] != undefined) || (past[0] != "" && past[0] != undefined)) {
                            callback('1', "Booth history found successfully", { upcomming: upcomming_booking, past: past_booking });
                        } else {
                            callback('2', 'Booth history not found', { upcomming: [], past: [] });
                        }
                    } else {
                        callback('0', t('restapi_globals_error'), false);
                    }
                })
            } else {
                callback('0', t('restapi_globals_error'), false);
            }
        })
    },

    /**
     * Function for refund amount of appointment
     */
    refund_amount: function (request, callback) {
        con.query("select * from tbl_appointment_booking where id = " + request.appointment_id + " AND is_deleted = '0'", function (err, result) {
            if (!err) {
                if (result.length > 0) {
                    if (result[0].is_refund == '1') {
                        callback('0', "Refund is already initiate for this appointment", false);
                    }
                    /*else if (request.amount > result[0].total_amount) {
                                           callback('0', "Refund amount should less than total amount", false);
                                       }*/
                    else if (result[0].is_cancel == '1') {
                        callback('0', "This appointment is cancelled by customer", false);
                    } else if (result[0].payment_mode == 'Cash' || result[0].transaction_id == "") {
                        callback('0', "Refund is not applicable for cash transaction", false);
                    } else {
                        if (result[0].payment_from == 'Wallet') {
                            var wallet_insert = {
                                user_id: result[0].user_id,
                                card_id: 0,
                                order_id: request.appointment_id,
                                amount: request.amount,
                                title: 'refund booking',
                                transaction_id: '',
                                status: 'credit'
                            }
                            require('../user/user_model').insert_wallet_history(wallet_insert, function (is_wallet_insert) {
                                var update_param = {
                                    is_refund: '1',
                                    refund_amount: request.amount,
                                    refund_id: 'Blookd' + datetime.create().format('YmdHMS'),
                                    refund_status: 'succeeded',
                                    refund_object: {},
                                    refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                }
                                common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function (is_refuned) {
                                    var message = {
                                        sender_id: request.service_provider_id,
                                        sender_type: USER_TYPE.SERVICE_PROVIDER,
                                        receiver_id: result[0].user_id,
                                        type: 'user',
                                        receiver_type: USER_TYPE.USER,
                                        message: "You got $" + request.amount + " refunded amount on " + result[0].booking_id + " this booking.",
                                        title: "Refund Appointment",
                                        isaction_id: request.appointment_id,
                                        tag: "refund_amount",
                                    }
                                    console.log("Wallet message", message);
                                    require('../../../config/common').prepare_customer_notification(result[0].user_id, message, function (notification) {
                                        callback('1', 'Refund initiated successfully', true);
                                    });
                                });
                            });
                        } else {
                            stripe.createChargeRefund(result[0].transaction_id, request.amount, function (code, message, refund) {
                                if (refund != null) {
                                    var update_param = {
                                        is_refund: '1',
                                        refund_amount: request.amount,
                                        refund_id: refund.charge,
                                        refund_status: refund.status,
                                        refund_object: JSON.stringify(refund),
                                        refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                    }
                                    common.update_data('tbl_appointment_booking', request.appointment_id, update_param, function (is_refuned) {
                                        var message = {
                                            sender_id: request.service_provider_id,
                                            sender_type: USER_TYPE.SERVICE_PROVIDER,
                                            receiver_id: result[0].user_id,
                                            type: 'user',
                                            receiver_type: USER_TYPE.USER,
                                            message: "You got $" + request.amount + " refunded amount on " + result[0].booking_id + " this booking.",
                                            title: "Refund Appointment",
                                            isaction_id: request.appointment_id,
                                            tag: "refund_amount",
                                        }
                                        console.log("Card message", message);
                                        require('../../../config/common').prepare_customer_notification(result[0].user_id, message, function (notification) {
                                            callback('1', 'Refund initiated successfully', true);
                                        });
                                    });
                                } else {
                                    callback(code, message, refund);
                                }
                            });
                        }
                    }
                } else {
                    callback('0', t('restapi_appointment_not_found'), false);
                }
            } else {
                callback('0', t('restapi_globals_error') + err, false);
            }
        })
    },

    /**
     *  Function for booth rent detail
     */
    booth_detail: function (request, callback) {
        var condition = "trb.id = '" + request.booth_rent_id + "'";
        con.query("select tw.*,trb.id as booth_rent_id,trb.booth_owner_id,trb.sub_total,trb.tax,trb.cleaning_fees,trb.total_amount,date_format(trb.date,'%Y-%m-%d') as date,tbr.name as booth_location_name,tbr.address,date_format(trb.start_slot_time,'%H:%i:%s') as start_slot_time,trb.end_date,date_format(trb.end_slot_time,'%H:%i:%s') as end_slot_time,IFNULL((SELECT ROUND(avg(ratting),2) FROM tbl_booth_review ur WHERE ur.booth_owner_id = trb.booth_owner_id AND ur.receiver_type = 'booth_host' AND ur.ratting > 0),0.0) AS ratting,IFNULL((SELECT ROUND(count(id),2) FROM tbl_booth_review ur WHERE ur.booth_owner_id = trb.booth_owner_id AND ur.receiver_type = 'booth_host' AND ur.ratting > 0),0) AS review,(SELECT GROUP_CONCAT(tmw.name SEPARATOR', ') FROM tbl_master_workspace_category tmw WHERE tmw.is_deleted = '0' AND tmw.id = tw.workspace_category_id) AS workspace_category,tbr.id as booth_location_id,tw.id as workspace_id,trb.status as booking_status,trb.service_provider_id as service_provider_id from tbl_rent_booth trb LEFT JOIN tbl_workspace tw ON trb.workspace_id = tw.id LEFT JOIN tbl_booth_renter_location tbr ON tw.booth_location_id = tbr.id where " + condition + " group by tw.id", function (err, result) {
            if (!err) {
                if (result.length > 0) {
                    con.query("SELECT tsp.id as service_provider_id,tsp.first_name,tsp.last_name,tsp.email,CONCAT(tsp.country_code,tsp.phone) as phonenumber,CONCAT('"+global.S3_BUCKET_ROOT + global.SP_IMAGE+"',tsp.profile_image) as profile_image,IFNULL((SELECT ROUND(avg(ratting),2) FROM tbl_booth_review ur WHERE ur.service_provider_id = '"+result[0].service_provider_id+"' AND ur.receiver_type = 'service_provider' AND ur.ratting > 0),0.0) AS ratting,IFNULL((SELECT ROUND(count(id),2) FROM tbl_booth_review ur WHERE ur.service_provider_id = '"+result[0].service_provider_id+"' AND ur.receiver_type = 'service_provider' AND ur.ratting > 0),0) AS review FROM tbl_service_provider as tsp WHERE tsp.id = '"+result[0].service_provider_id+"'",function(err3,providerdata,fields){
                        result[0].provider_details = (!err3 && providerdata[0] != undefined) ? providerdata[0] : {};
                        con.query("SELECT tsp.id as booth_owner_id,tsp.first_name,tsp.last_name,tsp.email,CONCAT(tsp.country_code,tsp.phone) as phonenumber,CONCAT('"+global.S3_BUCKET_ROOT + global.SP_IMAGE+"',tsp.profile_image) as profile_image,IFNULL((SELECT ROUND(avg(ratting),2) FROM tbl_booth_review ur WHERE ur.booth_owner_id = '"+result[0].booth_owner_id+"' AND ur.receiver_type = 'booth_host' AND ur.ratting > 0),0.0) AS ratting,IFNULL((SELECT ROUND(count(id),2) FROM tbl_booth_review ur WHERE ur.booth_owner_id = '"+result[0].booth_owner_id+"' AND ur.receiver_type = 'booth_host' AND ur.ratting > 0),0) AS review FROM tbl_service_provider as tsp WHERE tsp.id = '"+result[0].booth_owner_id+"'",function(err4,boothownerdata,fields){
                            result[0].booth_details = (!err4 && boothownerdata[0] != undefined) ? boothownerdata[0] : {};
                            con.query("select CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',image_name) as image_name from tbl_image where type = 'workspace_location_image' AND type_id = " + result[0].workspace_id + "", function (err1, result1) {
                                if (!err1 && result1.length > 0) {
                                    result[0].booth_image = result1;
                                } else {
                                    result[0].booth_image = [];
                                }
                                con.query("select tma.id,name,CONCAT('" + global.S3_BUCKET_ROOT + global.BOOTH_OWNER + "','',tma.image_name) as image_name from tbl_master_amenities tma LEFT JOIN tbl_workspace_amenties tbm ON tbm.amenities_id = tma.id where tbm.workspace_id = " + result[0].workspace_id + "", function (err2, amenities) {
                                    if (!err2 && amenities.length > 0) {
                                        result[0].amenities = amenities;
                                    } else {
                                        result[0].amenities = [];
                                    }
                                    callback('1', "Booth renting found successfully", result[0]);
                                });
                            });
                        });
                    });
                } else {
                    callback('0', "Booth renting not found", null);
                }
            } else {
                callback('0', t('restapi_globals_error') + err, null);
            }
        })
    },

    /**
     * Function for cancel booth rent booking
     */
    cancelboothrentbooking:function(request,callback){
        service_provider.booth_detail(request,function(messagecode,messageobject,responsedata){
            if(messagecode == '1' && responsedata != null){
                if(responsedata.status == 'Cancelled'){
                    callback("2",t('restapi_boothrentalreadyCancelled_error'),null);
                } else if(responsedata.status == 'Completed'){
                    callback("2",t('restapi_boothrentalreadyCompleted_error'),null);
                } else if(responsedata.status == 'Processing'){
                    callback("2",t('restapi_boothrentalreadyProcessing_error'),null);
                } else if(responsedata.status == 'Rejected'){
                    callback("2",t('restapi_boothrentalreadyRejected_error'),null);
                } else if(responsedata.status == 'Waiting'){
                    callback("2",t('restapi_boothrentalreadyWaiting_error'),null);
                } else {
                    if(responsedata.payment_mode == 'Card' && responsedata.payment_intent_id != ""){
                        stripe.createChargeRefund(responsedata.payment_intent_id,responsedata.total_amount,function(rescode,resmessage,responsedata){
                            var updparams = {
                                status : 'Cancelled',
                                cancel_by:'service_provider',
                                cancel_datetime: datetime.create().format('Y-m-d H:M:S'),
                                updatetime: datetime.create().format('Y-m-d H:M:S'),
                            }
                            if(request.cancel_id != undefined && request.cancel_id != ""){
                                updparams.cancel_id = request.cancel_id;
                            }
                            if(request.cancel_reason != undefined && request.cancel_reason != ""){
                                updparams.cancel_reason = request.cancel_reason;
                            }
                            common.update_data('tbl_rent_booth',request.booth_rent_id,updparams,function(is_updated){
                                if(is_updated){
                                    service_provider.booth_detail(request,function(messagecode,messageobject,responsedata){
                                        if(messagecode == '1' && responsedata != null){
                                            var message = {
                                                sender_id: request.service_provider_id,
                                                sender_type: USER_TYPE.SERVICE_PROVIDER,
                                                receiver_id: responsedata.booth_owner_id,
                                                type: 'booth_owner',
                                                receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                                message: 'Your booth rent booking has been cancelled by booth rentee',
                                                title: 'Cancel Booth Rent',
                                                isaction_id: request.booth_rent_id,
                                                tag: 'cancelboothrentbooking',
                                            }
                                            require('../../../config/common').prepare_notification(responsedata.booth_owner_id, message, function (notification) {
                                                callback('1',t('restapi_boothrentCancelled_success'), responsedata);
                                            });
                                        } else {
                                            callback(messagecode,messageobject,responsedata);
                                        }
                                    });
                                } else {
                                    callback('0', t('restapi_globals_error'),null);
                                }
                            });
                        });
                    } else {
                        var updparams = {
                            status : 'Cancelled',
                            cancel_by:'service_provider',
                            cancel_datetime: datetime.create().format('Y-m-d H:M:S'),
                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                        }
                        if(request.cancel_id != undefined && request.cancel_id != ""){
                            updparams.cancel_id = request.cancel_id;
                        }
                        if(request.cancel_reason != undefined && request.cancel_reason != ""){
                            updparams.cancel_reason = request.cancel_reason;
                        }
                        common.update_data('tbl_rent_booth',request.booth_rent_id,updparams,function(is_updated){
                            if(is_updated){
                                service_provider.booth_detail(request,function(messagecode,messageobject,responsedata){
                                    if(messagecode == '1' && responsedata != null){
                                        var message = {
                                            sender_id: request.service_provider_id,
                                            sender_type: USER_TYPE.SERVICE_PROVIDER,
                                            receiver_id: responsedata.booth_owner_id,
                                            type: 'booth_owner',
                                            receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                            message: 'Your booth rent booking has been cancelled by booth rentee',
                                            title: 'Cancel Booth Rent',
                                            isaction_id: request.booth_rent_id,
                                            tag: 'cancelboothrentbooking',
                                        }
                                        require('../../../config/common').prepare_notification(responsedata.booth_owner_id, message, function (notification) {
                                            callback('1',t('restapi_boothrentCancelled_success'), responsedata);
                                        });
                                    } else {
                                        callback(messagecode,messageobject,responsedata);
                                    }
                                });
                            } else {
                                callback('0', t('restapi_globals_error'),null);
                            }
                        });
                    }
                }
            } else {
                callback(messagecode,messageobject,responsedata);
            }
        });
    },

    /**
     * Function for rate booth
     */
    rate_booth: function (request, callback) {
        con.query("select * from tbl_booth_review where booth_rent_id = " + request.booth_rent_id + " AND booth_owner_id = '"+request.booth_owner_id+"' AND receiver_type = 'booth_host'", function (err, result) {
            if (!err) {
                if (result.length > 0) {
                    var update_ratting = {
                        ratting: request.ratting,
                        review_id:(request.review_id != undefined && request.review_id != "") ? request.review_id : "",
                        review: (request.review != undefined && request.review != "") ? request.review : result[0].review,
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                    }
                    common.update_data('tbl_booth_review', result[0].id, update_ratting, function (is_ratting) {
                        if (is_ratting) {
                            con.query("SELECT * FROM tbl_service_provider WHERE id = '" + request.service_provider_id + "'", function (err, spdata) {
                                if (!err && spdata[0] != undefined) {
                                    var message = {
                                        sender_id: request.service_provider_id,
                                        sender_type: USER_TYPE.SERVICE_PROVIDER,
                                        receiver_id: request.booth_owner_id,
                                        type: 'booth_owner',
                                        receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                        message: `${ spdata[0].first_name } ${ spdata[0].last_name } left you a review.`,
                                        title: 'Rate & Reviews',
                                        isaction_id: request.booth_rent_id,
                                        tag: 'updateboothratereview',
                                    }
                                    require('../../../config/common').prepare_notification(request.booth_owner_id, message, function (notification) {
                                        callback('1', "Review updated successfully", true);
                                    });
                                } else {
                                    callback('1', "Review updated successfully", true);
                                }
                            });
                        } else {
                            callback('0', "Review can not be updated. Please try after sometime", null);
                        }
                    })
                } else {
                    var insert_ratting = {
                        ratting: request.ratting,
                        review: (request.review != undefined && request.review != "") ? request.review : "",
                        booth_owner_id: request.booth_owner_id,
                        service_provider_id: request.service_provider_id,
                        receiver_type: 'booth_host',
                        review_reply: "",
                        review_id:(request.review_id != undefined && request.review_id != "") ? request.review_id : "",
                        booth_rent_id: request.booth_rent_id,
                        is_deleted: '0',
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                        insertdate: datetime.create().format('Y-m-d H:M:S'),
                    }
                    common.single_insert_data('tbl_booth_review', insert_ratting, function (is_revew_add) {
                        if (is_revew_add) {
                            con.query("SELECT * FROM tbl_service_provider WHERE id = '" + request.service_provider_id + "'", function (err, spdata) {
                                if (!err && spdata[0] != undefined) {
                                    var message = {
                                        sender_id: request.service_provider_id,
                                        sender_type: USER_TYPE.SERVICE_PROVIDER,
                                        receiver_id: request.booth_owner_id,
                                        type: 'booth_owner',
                                        receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                        message: `${ spdata[0].first_name } ${ spdata[0].last_name } left you a review.`,
                                        title: 'Rate & Reviews',
                                        isaction_id: request.booth_rent_id,
                                        tag: 'newboothratereview',
                                    }
                                    require('../../../config/common').prepare_notification(request.booth_owner_id, message, function (notification) {
                                        callback('1', "Review added successfully", true);
                                    });
                                } else {
                                    callback('1', "Review added successfully", true);
                                }
                            });
                        } else {
                            callback('0', "Review can not be added. Please try after sometime", null);
                        }
                    })
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },

    /**
     * Function for get customer retention ratio
    */
    getCustomerRetentionRatio: function (service_provider_id, request, callback) {
        var wherecon = "service_provider_id = '" + service_provider_id + "'";
        var wherecon1 = "service_provider_id = '" + service_provider_id + "'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
                wherecon1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN DATE_SUB(DATE('" + request.start_date + "'),INTERVAL DATEDIFF('" + request.end_date + "','" + request.start_date + "') DAY) AND '" + request.start_date + "'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
                wherecon1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN DATE_SUB(DATE('" + request.start_date + "'),INTERVAL 30 DAY) AND '" + request.start_date + "'";
            } else {
                wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
                wherecon1 += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 60 DAY) AND DATE_SUB(DATE(NOW()), INTERVAL 30 DAY)";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
                wherecon1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN DATE_SUB(DATE('" + request.start_date + "'),INTERVAL 7 DAY) AND '" + request.start_date + "'";
            } else {
                wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
                wherecon1 += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 14 DAY) AND DATE_SUB(DATE(NOW()), INTERVAL 7 DAY)";
            }
            //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
                wherecon1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_SUB(DATE('" + request.date + "'),INTERVAL 1 DAY)";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
                wherecon1 += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_SUB(DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'),INTERVAL 1 DAY)";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            //wherecon1 += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 60 DAY) AND DATE_SUB(DATE(NOW()), INTERVAL 30 DAY)";
        }
        //SELECT((SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE "+wherecon+") - (SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE "+wherecon+" AND user_id NOT IN(SELECT user_id FROM tbl_appointment_booking WHERE "+wherecon+"))) / (SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE "+wherecon+") * 100 as CRR
        con.query("SELECT (((IFNULL((SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE " + wherecon + " GROUP BY service_provider_id),0) - IFNULL((SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE service_provider_id = '" + service_provider_id + "' AND id IN (SELECT id FROM tbl_appointment_booking WHERE " + wherecon + " GROUP BY user_id,customer_id HAVING count(id) <= 1) GROUP BY service_provider_id),0)) / IFNULL((SELECT COUNT(DISTINCT user_id) FROM tbl_appointment_booking WHERE " + wherecon1 + " GROUP BY service_provider_id),0))*100) as CRR", function (err1, result1) {
            if (err1) {
                console.log("customer retention ratio", err1);
            }
            if (!err1 && result1[0] != undefined && result1[0].CRR != undefined) {
                callback(parseFloat(result1[0].CRR).toFixed(2));
            } else {
                callback(parseFloat(0).toFixed(2));
            }
        });
    },

    /**
     * Function for get earning per hour
     */
    getEarningPerHour: function (service_provider_id, request, callback) {
        var wherecon = "s.service_provider_id = '" + service_provider_id + "' AND s.is_deleted = '0' AND ab.service_provider_id = '" + service_provider_id + "'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "' AND s.day = DAYNAME('" + request.date + "')";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') AND DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) = DAYNAME(DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select s.*,IFNULL(SUM(HOUR(TIMEDIFF(s.from_time, s.to_time))),0) as total_hour,IFNULL(SUM(total_amount),0) as  total_earning from tbl_appointment_booking ab LEFT JOIN tbl_service_provider_available_slot s ON ab.service_provider_id = s.service_provider_id where " + wherecon + " GROUP BY ab.service_provider_id", function (err1, result1) {
            if (err1) {
                console.log("earning per hour", err1);
            }
            if (!err1 && result1[0] != undefined && result1[0].total_earning != undefined) {
                callback(parseFloat(result1[0].total_earning / result1[0].total_hour).toFixed(2))
            } else {
                callback(parseFloat(0).toFixed(2));
            }
        });
    },

    /**
     * Function for average ticket(booking) amount
     */
    getAverageTicketAmount: function (service_provider_id, request, callback) {
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.is_refund = '0'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("select IFNULL(AVG(ab.total_amount),0) as average_ticket_amount from tbl_appointment_booking ab where " + wherecon + " GROUP BY ab.service_provider_id", function (err1, result1) {
            if (err1) {
                console.log("average ticket(booking) amount", err1);
            }
            if (!err1 && result1[0] != undefined && result1[0].average_ticket_amount != undefined) {
                callback(parseFloat(result1[0].average_ticket_amount).toFixed(2));
            } else {
                callback(parseFloat(0).toFixed(2));
            }
        });
    },

    /**
     * Function for get retail percentage of sales
     */
    getRetailPercentageofsales: function (service_provider_id, request, callback) {
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        //SELECT ((select sum(total_amount) from tbl_appointment_booking where service_provider_id = " + service_provider_id + ") / ((select sum(total_amount) from tbl_appointment_booking))) * 100 as retail_percentage_of_sales FROM tbl_appointment_booking ab where ab.date = current_date() AND ab.is_refund = 0 AND service_provider_id = " + service_provider_id + "
        con.query("SELECT (IFNULL((SELECT SUM(abd.price * abd.quantity) FROM tbl_appointment_booking_detail as abd INNER JOIN tbl_appointment_booking as ab ON abd.appointment_id = ab.id WHERE abd.type = 'Product' AND " + wherecon + " GROUP BY ab.service_provider_id),0) * 100) / IFNULL((SELECT SUM(abd.price) FROM tbl_appointment_booking_detail as abd INNER JOIN tbl_appointment_booking as ab ON abd.appointment_id = ab.id WHERE abd.type = 'Service' AND " + wherecon + " GROUP BY ab.service_provider_id),0) as retail_percentage_of_sales", function (err1, result1) {
            if (err1) {
                console.log("retail percentage of sales", err1);
            }
            if (!err1 && result1[0] != undefined && result1[0].retail_percentage_of_sales != undefined) {
                callback(parseFloat(result1[0].retail_percentage_of_sales).toFixed(2) + "%");
            } else {
                callback(parseFloat(0).toFixed(2) + "%");
            }
        });
    },

    /**
     * Function for get Top Client
     */
    getTopClient: function (service_provider_id, request, callback) {
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("SELECT customer_id, user_id, SUM(total_amount) AS total_revenue,IF(ab.customer_name = '',IF(ab.user_id != '0', (SELECT CONCAT(first_name,' ',last_name) FROM tbl_user WHERE id = ab.user_id ORDER BY id DESC LIMIT 1),(SELECT c.customer_name FROM tbl_service_provider_client c WHERE c.id = ab.customer_id ORDER BY id DESC LIMIT 1)) , ab.customer_name) AS customer_name,IF(ab.user_id != '0',(select CONCAT(u.country_code,' ',u.phone) from tbl_user u where u.id = ab.user_id LIMIT 1),(select CONCAT(c.country_code,' ',c.phone) from tbl_service_provider_client c where c.id = ab.customer_id LIMIT 1)) as phone,IF(ab.user_id = '0',(SELECT CONCAT('" + global.S3_BUCKET_ROOT + global.USER_IMAGE + "','',c.profile_image) FROM tbl_service_provider_client c WHERE c.id = ab.client_id),(SELECT CONCAT('" + global.S3_BUCKET_ROOT + global.USER_IMAGE + "',profile_image) FROM tbl_user WHERE id = ab.user_id ORDER BY ID DESC LIMIT 1)) AS profile_image FROM tbl_appointment_booking ab where " + wherecon + " AND (user_id != 0 OR customer_id != 0) GROUP BY customer_id, user_id ORDER BY total_revenue DESC LIMIT 20", function (err1, result1) {
            if (err1) {
                console.log("Top Client", err1);
            }
            if (!err1 && result1.length > 0) {
                callback(result1);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Top Retail Products
     */
    getTopRetailProducts: function (service_provider_id, request, callback) {
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND abd.type = 'Product' AND abd.product_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("SELECT p.product_name,SUM(abd.price * quantity) AS price,GET_SERVICE_PROVIDER_RATING(ab.service_provider_id, NULL) AS ratting,CONCAT('" + global.S3_BUCKET_ROOT + global.PRODUCT_IMAGE + "','',p.product_image) AS product_image FROM tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id LEFT JOIN tbl_product p ON p.id = abd.product_id WHERE " + wherecon + " GROUP BY product_id order by price DESC LIMIT 10 ", function (err1, result1) {
            if (err1) {
                console.log("Top Retail Products", err1);
            }
            if (!err1 && result1.length > 0) {
                callback(result1);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Top Service
     */
    getTopService: function (service_provider_id, request, callback) {
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND abd.type = 'Service' AND abd.service_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            //} else if (request.period != undefined && request.period == 'Daily') {
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            //wherecon += " AND date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
        }
        con.query("SELECT s.service_name,SUM(abd.price) AS price,GET_SERVICE_PROVIDER_RATING(ab.service_provider_id, NULL) AS ratting FROM tbl_appointment_booking_detail abd  LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id LEFT JOIN tbl_service s ON s.id = abd.service_id WHERE " + wherecon + " GROUP BY service_id ORDER BY price DESC LIMIT 5", function (err1, result1) {
            if (err1) {
                console.log("Top Service", err1);
            }
            if (!err1 && result1.length > 0) {
                callback(result1);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get report tiles data
     */
    getreporttiles(service_provider_id, request, callback) {
        var finaltiles = [];
        if (request.type != undefined && request.type == 'old') {
            var sqlquery = "SELECT tmt.* FROM tbl_service_provider_tiles as tspt INNER JOIN tbl_master_tiles as tmt ON tspt.tiles_id = tmt.id WHERE tspt.service_provider_id = '" + service_provider_id + "' AND tmt.status = 'Active' AND tmt.user_type = 'ServiceProvider' AND tmt.is_deleted = '0' GROUP BY tmt.id ORDER BY tmt.id ASC";
        } else {
            var sqlquery = "SELECT tmt.* FROM tbl_master_tiles as tmt WHERE tmt.status = 'Active' AND tmt.user_type = 'ServiceProvider' AND tmt.is_deleted = '0' GROUP BY tmt.id ORDER BY tmt.id ASC";
        }
        con.query(sqlquery, function (error, tiles, fields) {
            if (!error && tiles.length > 0) {
                asyncloop(tiles, function (item, next) {
                    var tiledata = {
                        id: item.id,
                        name: item.name,
                        compare_value: 0,
                        value: 0,
                        arrayvalue: [],
                    }
                    if (item.id == 1) {
                        service_provider.getCustomerRetentionRatio(service_provider_id, request, function (customer_retention_ratio) {
                            tiledata["value"] = customer_retention_ratio;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 2) {
                        service_provider.getEarningPerHour(service_provider_id, request, function (earning_per_hour) {
                            tiledata["value"] = earning_per_hour;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 3) {
                        service_provider.getAverageTicketAmount(service_provider_id, request, function (average_ticket_amount) {
                            tiledata["value"] = average_ticket_amount;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 4) {
                        service_provider.getRetailPercentageofsales(service_provider_id, request, function (retail_percentage_of_sales) {
                            tiledata["value"] = retail_percentage_of_sales;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 5) {
                        service_provider.getTopClient(service_provider_id, request, function (top_client) {
                            tiledata["arrayvalue"] = top_client;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 6) {
                        service_provider.getTopRetailProducts(service_provider_id, request, function (top_retail_products) {
                            tiledata["arrayvalue"] = top_retail_products;
                            finaltiles.push(tiledata);
                            next();
                        });
                    } else if (item.id == 7) {
                        service_provider.getTopService(service_provider_id, request, function (top_service) {
                            tiledata['arrayvalue'] = top_service;
                            finaltiles.push(tiledata);
                            next()
                        });
                    } else {
                        next()
                    }
                }, function () {
                    callback(finaltiles);
                });
            } else {
                callback(finaltiles);
            }
        });
    },

    // Define a function to get the numerical representation of the day
    getDayIndex : function (dayName) {
        const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        return daysOfWeek.indexOf(dayName);
    },

    /**
     * Function for report calculation
     */
    report: function (request, service_provider_id, callback) {
        service_provider.getreporttiles(service_provider_id, request, function (finaltiles) {
            if (finaltiles.length > 0) {
                if (request.type != undefined && request.type == 'old') {
                    service_provider.getreportgraphs(service_provider_id, request, function (graphs) {
                        callback('1', "Report found successfully", { tiles: finaltiles, graph: graphs });
                    });
                } else {
                    callback('1', "Report found successfully", { tiles: finaltiles, graph: [] });
                }
            } else {
                callback('0', "Please add tiles to dashboard", null);
            }
        });
    },

    /**
     * Function for get Top of Services graph data
    */
    getTopofServicesgraph: function (service_provider_id, request, callback) {
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0' AND ab.booking_status IN ('Completed','Paid') AND ab.payment_status = 'paid' AND abd.type = 'Service' AND abd.service_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("select COUNT(abd.service_id) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getTopofServicesgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("select COUNT(abd.service_id) as main_count,DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("getTopofServicesgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("select COUNT(abd.service_id) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getTopofServicesgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            con.query("select COUNT(abd.service_id) as main_count,date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:00') as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("getTopofServicesgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for get Total Retail Earnings graph data
     */
    getTotalRetailEarningsgraph(service_provider_id, request, callback) {
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0' AND ab.booking_status IN ('Completed','Paid') AND ab.payment_status = 'paid' AND abd.type = 'Product' AND abd.product_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("SELECT sum(abd.price * abd.quantity) AS main_count, DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) AS day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getTotalRetailEarningsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("SELECT sum(abd.price * abd.quantity) AS main_count,DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) AS day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("getTotalRetailEarningsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("SELECT sum(abd.price * abd.quantity) AS main_count, DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) AS day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking AS ab  LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getTotalRetailEarningsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            con.query("SELECT sum(abd.price * abd.quantity) AS main_count, date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:00') AS day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking AS ab LEFT JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = ab.id WHERE " + wherecon + " GROUP BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("getTotalRetailEarningsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for get Total Service Earnings graph data
     */
    getTotalServiceEarningsgraph: function (service_provider_id, request, callback) {
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0' AND ab.booking_status IN ('Completed','Paid') AND ab.payment_status = 'paid' AND abd.type = 'Service' AND abd.service_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,sum(abd.price) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getTotalServiceEarningsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("select DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,sum(abd.price) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("getTotalServiceEarningsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,sum(abd.price) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                console.log('console results',results);
                if (error) {
                    console.log("getTotalServiceEarningsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            con.query("select date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:00') as day_name,sum(abd.price) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("getTotalServiceEarningsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for get Busiest Hours of the day graph data
     */
    getBusiestHoursofthedaygraph: function (service_provider_id, request, callback) {
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0' AND ab.booking_status IN ('Completed','Paid') AND ab.payment_status = 'paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
        }
        con.query("select date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:00') as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date,IFNULL(count(id),0) as main_count from tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function (error, results) {
            if (error) {
                console.log("Busiest Hours of the day graph data", error);
            }
            if (!error && results[0] != undefined) {
                // Sort the array based on the numerical representation of the day
                results.sort((a, b) => {
                    return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                });
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Busiest Days in a Week graph data
     */
    getBusiestDaysinaWeekgraph: function (service_provider_id, request, callback) {
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0' AND ab.booking_status IN ('Completed','Paid') AND ab.payment_status = 'paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
        }
        con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date,IFNULL(count(id),0) as main_count from tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
            if (error) {
                console.log("Busiest Days in a Week graph data", error);
            }
            if (!error && results[0] != undefined) {
                // Sort the array based on the numerical representation of the day
                results.sort((a, b) => {
                    return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                });
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for get Busiest Month of the Year graph data
     */
    getBusiestMonthoftheYeargraph: function (service_provider_id, request, callback) {
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0' AND ab.booking_status IN ('Completed','Paid') AND ab.payment_status = 'paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
        }
        con.query("select MONTHNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date,IFNULL(count(id),0) as main_count from tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY MONTHNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY MONTH(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
            if (error) {
                console.log("Busiest Month of the Year graph data", error);
            }
            if (!error && results[0] != undefined) {
                // Sort the array based on the numerical representation of the day
                results.sort((a, b) => {
                    return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                });
                callback(results);
            } else {
                callback([]);
            }
        });
    },

    /**
     * Function for online bookings graph data
     */
    getOnlineBookingsgraph: function (service_provider_id, request, callback) {
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.booking_type = 'online' AND ab.is_deleted = '0' AND ab.booking_status IN ('Completed','Paid') AND ab.payment_status = 'paid'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            //round((SUM(CASE WHEN booking_type = 'online' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) AS online_booking_percentage,round((SUM(CASE WHEN booking_type = 'walkin' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) as offline_booking_percentage
            con.query("SELECT SUM(ab.total_amount) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getOnlineBookingsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            //round((SUM(CASE WHEN booking_type = 'online' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) AS online_booking_percentage,round((SUM(CASE WHEN booking_type = 'walkin' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) as offline_booking_percentage
            con.query("SELECT SUM(ab.total_amount) as main_count,DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("getOnlineBookingsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            //round((SUM(CASE WHEN booking_type = 'online' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) AS online_booking_percentage,round((SUM(CASE WHEN booking_type = 'walkin' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) as offline_booking_percentage
            con.query("SELECT SUM(ab.total_amount) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getOnlineBookingsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            //round((SUM(CASE WHEN booking_type = 'online' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) AS online_booking_percentage,round((SUM(CASE WHEN booking_type = 'walkin' THEN 1 ELSE 0 END) / COUNT(*)) * 100,2) as offline_booking_percentage
            con.query("SELECT SUM(ab.total_amount) as main_count,date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:00') as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("getOnlineBookingsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for no shows graph data
     */
    getNoShowsgraph: function (service_provider_id, request, callback) {
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.booking_status ='No Show' AND ab.is_deleted = '0'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("SELECT count(id) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getNoShowsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("SELECT count(id) as main_count,DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("getNoShowsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("SELECT count(id) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getNoShowsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            con.query("SELECT count(id) as main_count,date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:00') as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("getNoShowsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for cancellations graph data
     */
    getCancellationsgraph: function (service_provider_id, request, callback) {
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.booking_status ='Cancelled' AND ab.is_deleted = '0'";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("SELECT count(id) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getCancellationsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("SELECT count(id) as main_count,DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("getCancellationsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("SELECT count(id) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getCancellationsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            con.query("SELECT count(id) as main_count,date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:00') as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("getCancellationsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for All Clients graph data
     */
    getAllClientsgraph: function (service_provider_id, request, callback) {
        var wherecon = "ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0' AND ab.booking_status IN ('Completed','Paid') AND ab.payment_status = 'paid' AND (ab.customer_id != '0' OR ab.user_id != '0')";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("SELECT COUNT(DISTINCT IF(user_id = 0,customer_id,user_id)) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DAYNAME(ab.date) ORDER BY DAYNAME(ab.date) ASC", function (error, results) {
                if (error) {
                    console.log("getAllClientsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("SELECT COUNT(DISTINCT IF(user_id = 0,customer_id,user_id)) as main_count,DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("getAllClientsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("SELECT COUNT(DISTINCT IF(user_id = 0,customer_id,user_id)) as main_count,DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getAllClientsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            con.query("SELECT COUNT(DISTINCT IF(user_id = 0,customer_id,user_id)) as main_count,date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:00') as day_name,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date FROM tbl_appointment_booking ab WHERE " + wherecon + " GROUP BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("getAllClientsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for All Retail Products graph data
     */
    getAllRetailProductsgraph: function (service_provider_id, request, callback) {
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0' AND ab.booking_status IN ('Completed','Paid') AND ab.payment_status = 'paid' AND abd.type = 'Product' AND abd.product_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,sum(abd.quantity) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getAllRetailProductsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("select DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,sum(abd.quantity) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("getAllRetailProductsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,sum(abd.quantity) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getAllRetailProductsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            con.query("select date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:00') as day_name,sum(abd.quantity) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("getAllRetailProductsgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for All Services graph data
     */
    getAllServicesgraph: function (service_provider_id, request, callback) {
        var wherecon = "abd.service_provider_id = '" + service_provider_id + "' AND ab.service_provider_id = '" + service_provider_id + "' AND ab.is_deleted = '0' AND ab.booking_status IN ('Completed','Paid') AND ab.payment_status = 'paid' AND abd.type = 'Service' AND abd.service_id != 0";
        if (request.period != undefined && request.period == 'Custom') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            }
            con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,count(abd.service_id) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getAllServicesgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Monthly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 30 DAY) AND current_date()";
            }
            con.query("select DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) as day_name,count(abd.service_id) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ORDER BY DATE_ADD(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'), INTERVAL(1-DAYOFWEEK(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d'))) DAY) ASC", function (error, results) {
                if (error) {
                    console.log("getAllServicesgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else if (request.period != undefined && request.period == 'Weekly') {
            if (request.start_date != undefined && request.start_date != "" && request.end_date != undefined && request.end_date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') BETWEEN '" + request.start_date + "' AND '" + request.end_date + "'";
            } else {
                wherecon += " AND ab.date BETWEEN DATE_SUB(DATE(NOW()), INTERVAL 7 DAY) AND current_date()";
            }
            con.query("select DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) as day_name,count(abd.service_id) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ORDER BY DAYNAME(DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')) ASC", function (error, results) {
                if (error) {
                    console.log("getAllServicesgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        } else {
            if (request.date != undefined && request.date != "") {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = '" + request.date + "'";
            } else {
                wherecon += " AND DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') = DATE_FORMAT(CONVERT_TZ(now(),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d')";
            }
            con.query("select date_format(CONCAT(ab.date,' ',ab.slot_time),'%H:00') as day_name,count(abd.service_id) as main_count,DATE_FORMAT(CONVERT_TZ(CONCAT(ab.date,' ',ab.slot_time),'+00:00','"+request.timezone_diff+"'),'%Y-%m-%d') as date from tbl_appointment_booking_detail abd LEFT JOIN tbl_appointment_booking ab ON ab.id = abd.appointment_id where " + wherecon + " GROUP BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ORDER BY date_format(CONCAT(ab.date,' ',ab.slot_time),'%H') ASC", function (error, results) {
                if (error) {
                    console.log("getAllServicesgraph", error);
                }
                if (!error && results.length > 0) {
                    // Sort the array based on the numerical representation of the day
                    results.sort((a, b) => {
                        return service_provider.getDayIndex(a.day_name) - service_provider.getDayIndex(b.day_name);
                    });
                    callback(results);
                } else {
                    callback([]);
                }
            });
        }
    },

    /**
     * Function for get report graphs data
     */
    getreportgraphs(service_provider_id, request, callback) {
        var finalgraphs = [];
        var comparerequest = {timezone:request.timezone,timezone_diff:request.timezone_diff};
        if (request.period != undefined && request.period != "") {
            comparerequest["period"] = request.period;
        }
        if (request.compare_date != undefined && request.compare_date != "") {
            comparerequest["date"] = request.compare_date;
        }
        if (request.compare_start_date != undefined && request.compare_start_date != "" && request.compare_end_date != undefined && request.compare_end_date != "") {
            comparerequest["start_date"] = request.compare_start_date;
            comparerequest["end_date"] = request.compare_end_date;
        }
        con.query("SELECT tmg.* FROM tbl_service_provider_graph as tspg INNER JOIN tbl_master_graph as tmg ON tspg.graph_id = tmg.id WHERE tspg.service_provider_id = '" + service_provider_id + "' AND tmg.status = 'Active' AND tmg.user_type = 'ServiceProvider' AND tmg.is_deleted = '0' GROUP BY tmg.id ORDER BY tmg.id ASC", function (error, graphs, fields) {
            if (!error && graphs.length > 0) {
                asyncloop(graphs, function (item, next) {
                    var tiledata = {
                        id: item.id,
                        name: item.name,
                        values: [],
                    }
                    if (item.id == 1) {
                        service_provider.getTopofServicesgraph(service_provider_id, request, function (Top_of_services) {
                            if (Top_of_services.length > 0) {
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getTopofServicesgraph(service_provider_id, comparerequest, function (compare_Top_of_services) {
                                        if (compare_Top_of_services.length > 0) {
                                            service_provider.compare_value(Top_of_services, compare_Top_of_services, function (compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = Top_of_services;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = Top_of_services;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 2) {
                        service_provider.getTotalRetailEarningsgraph(service_provider_id, request, function (total_retail_earnings) {
                            if (total_retail_earnings.length > 0) {
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getTotalRetailEarningsgraph(service_provider_id, comparerequest, function (compare_total_retail_earnings) {
                                        if (compare_total_retail_earnings.length > 0) {
                                            service_provider.compare_value(total_retail_earnings, compare_total_retail_earnings, function (compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = total_retail_earnings;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = total_retail_earnings;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 3) {
                        service_provider.getTotalServiceEarningsgraph(service_provider_id, request, function (total_service_earnings) {
                            if (total_service_earnings.length > 0) {
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getTotalServiceEarningsgraph(service_provider_id, comparerequest, function (compare_total_service_earnings) {
                                        if (compare_total_service_earnings.length > 0) {
                                            service_provider.compare_value(total_service_earnings, compare_total_service_earnings, function (compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = total_service_earnings;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = total_service_earnings;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 4) {
                        if (request.period == 'Daily') {
                            service_provider.getBusiestHoursofthedaygraph(service_provider_id, request, function (hoursofthe_day) {
                                if (hoursofthe_day.length > 0) {
                                    if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                        service_provider.getBusiestHoursofthedaygraph(service_provider_id, comparerequest, function (compare_hoursofthe_day) {
                                            if (compare_hoursofthe_day.length > 0) {
                                                service_provider.compare_multiple_value(hoursofthe_day, compare_hoursofthe_day, function (compared_value) {
                                                    tiledata["values"] = compared_value;
                                                    finalgraphs.push(tiledata);
                                                    next();
                                                });
                                            } else {
                                                tiledata["values"] = hoursofthe_day;
                                                finalgraphs.push(tiledata);
                                                next();
                                            }
                                        });
                                    } else {
                                        tiledata["values"] = hoursofthe_day;
                                        finalgraphs.push(tiledata);
                                        next();
                                    }
                                } else {
                                    tiledata["values"] = [];
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            });
                        } else {
                            next();
                        }
                    } else if (item.id == 5) {
                        if (request.period == 'Weekly' || request.period == 'Custom') {
                            service_provider.getBusiestDaysinaWeekgraph(service_provider_id, request, function (daysofthe_week) {
                                if (daysofthe_week.length > 0) {
                                    if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                        service_provider.getBusiestDaysinaWeekgraph(service_provider_id, comparerequest, function (compare_daysofthe_week) {
                                            if (compare_daysofthe_week.length > 0) {
                                                service_provider.compare_multiple_value(daysofthe_week, compare_daysofthe_week, function (compared_value) {
                                                    tiledata["values"] = compared_value;
                                                    finalgraphs.push(tiledata);
                                                    next();
                                                });
                                            } else {
                                                tiledata["values"] = daysofthe_week;
                                                finalgraphs.push(tiledata);
                                                next();
                                            }
                                        });
                                    } else {
                                        tiledata["values"] = daysofthe_week;
                                        finalgraphs.push(tiledata);
                                        next();
                                    }
                                } else {
                                    tiledata["values"] = [];
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            });
                        } else {
                            next();
                        }
                    } else if (item.id == 6) {
                        if (request.period == 'Monthly') {
                            service_provider.getBusiestMonthoftheYeargraph(service_provider_id, request, function (weekofthe_year) {
                                if (weekofthe_year.length > 0) {
                                    if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                        service_provider.getBusiestMonthoftheYeargraph(service_provider_id, comparerequest, function (compare_weekofthe_year) {
                                            if (compare_weekofthe_year.length > 0) {
                                                service_provider.compare_multiple_value(weekofthe_year, compare_weekofthe_year, function (compared_value) {
                                                    tiledata["values"] = compared_value;
                                                    finalgraphs.push(tiledata);
                                                    next();
                                                });
                                            } else {
                                                tiledata["values"] = weekofthe_year;
                                                finalgraphs.push(tiledata);
                                                next();
                                            }
                                        });
                                    } else {
                                        tiledata["values"] = weekofthe_year;
                                        finalgraphs.push(tiledata);
                                        next();
                                    }
                                } else {
                                    tiledata["values"] = [];
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            });
                        } else {
                            next();
                        }
                    } else if (item.id == 7) {
                        service_provider.getOnlineBookingsgraph(service_provider_id, request, function (online_bookings) {
                            if (online_bookings.length > 0) {
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getOnlineBookingsgraph(service_provider_id, comparerequest, function (compare_online_bookings) {
                                        if (compare_online_bookings.length > 0) {
                                            service_provider.compare_value(online_bookings, compare_online_bookings, function (compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = online_bookings;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = online_bookings;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 8) {
                        service_provider.getNoShowsgraph(service_provider_id, request, function (no_shows) {
                            if (no_shows.length > 0) {
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getNoShowsgraph(service_provider_id, comparerequest, function (compare_no_shows) {
                                        if (compare_no_shows.length > 0) {
                                            service_provider.compare_value(no_shows, compare_no_shows, function (compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = no_shows;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = no_shows;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 9) {
                        service_provider.getCancellationsgraph(service_provider_id, request, function (cancellations) {
                            if (cancellations.length > 0) {
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getCancellationsgraph(service_provider_id, comparerequest, function (compare_cancellations) {
                                        if (compare_cancellations.length > 0) {
                                            service_provider.compare_value(cancellations, compare_cancellations, function (compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = cancellations;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = cancellations;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 10) {
                        service_provider.getAllClientsgraph(service_provider_id, request, function (all_clients) {
                            if (all_clients.length > 0) {
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getAllClientsgraph(service_provider_id, comparerequest, function (compare_all_clients) {
                                        if (compare_all_clients.length > 0) {
                                            service_provider.compare_value(all_clients, compare_all_clients, function (compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = all_clients;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = all_clients;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 11) {
                        service_provider.getAllRetailProductsgraph(service_provider_id, request, function (all_retail_products) {
                            if (all_retail_products.length > 0) {
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getAllRetailProductsgraph(service_provider_id, comparerequest, function (compare_all_retail_products) {
                                        if (compare_all_retail_products.length > 0) {
                                            service_provider.compare_value(all_retail_products, compare_all_retail_products, function (compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = all_retail_products;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = all_retail_products;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else if (item.id == 12) {
                        service_provider.getAllServicesgraph(service_provider_id, request, function (all_services) {
                            if (all_services.length > 0) {
                                if ((request.compare_date != undefined && request.compare_date != "") || ((request.compare_start_date != undefined && request.compare_start_date != "") && (request.compare_end_date != undefined && request.compare_end_date != ""))) {
                                    service_provider.getAllServicesgraph(service_provider_id, comparerequest, function (compare_all_services) {
                                        if (compare_all_services.length > 0) {
                                            service_provider.compare_value(all_services, compare_all_services, function (compared_value) {
                                                tiledata["values"] = compared_value;
                                                finalgraphs.push(tiledata);
                                                next();
                                            });
                                        } else {
                                            tiledata["values"] = all_services;
                                            finalgraphs.push(tiledata);
                                            next();
                                        }
                                    });
                                } else {
                                    tiledata["values"] = all_services;
                                    finalgraphs.push(tiledata);
                                    next();
                                }
                            } else {
                                tiledata["values"] = [];
                                finalgraphs.push(tiledata);
                                next();
                            }
                        });
                    } else {
                        next();
                    }
                }, function () {
                    callback(finalgraphs);
                });
            } else {
                callback(finalgraphs);
            }
        });
    },

    /**
     * Function for compare value
     */
    compare_value: function (result1, result2, callback) {
        let result = {};
        result1.forEach(item => {
            if (result[item.day_name]) {
                result[item.day_name].main_count += item.main_count;
            } else {
                result[item.day_name] = { main_count: item.main_count, date: item.date };
            }
        });
        result2.forEach(item => {
            if (result[item.day_name]) {
                result[item.day_name].compare_count = item.main_count;
                result[item.day_name].compare_date = item.date;
            } else {
                result[item.day_name] = { compare_count: item.main_count, date: item.date, compare_date: item.date };
            }
        });
        let finalResult = Object.keys(result).map(key => {
            return { day_name: key, date: result[key].date, compare_date: (result[key].compare_date != undefined) ? result[key].compare_date : '', main_count: (result[key].main_count != undefined) ? result[key].main_count : 0, compare_count: (result[key].compare_count != undefined) ? result[key].compare_count : 0 };
        });
        callback(finalResult);
    },

    /**
     * Function for compare multiple values
     */
    compare_multiple_value: function (result1, result2, callback) {
        let result = {};
        result1.forEach(item => {
            if (result[item.day_name]) {
                result[item.day_name].online_booking_percentage = item.online_booking_percentage;
                result[item.day_name].offline_booking_percentage = item.offline_booking_percentage;
            } else {
                result[item.day_name] = { online_booking_percentage: item.online_booking_percentage, offline_booking_percentage: item.offline_booking_percentage, date: item.date };
            }
        });
        result2.forEach(item => {
            if (result[item.day_name]) {
                result[item.day_name].online_booking_compare_percentage = item.online_booking_percentage;
                result[item.day_name].offline_booking_compare_percentage = item.offline_booking_percentage;
                result[item.day_name].compare_date = item.date;
            } else {
                result[item.day_name] = { online_booking_compare_percentage: item.online_booking_percentage, offline_booking_compare_percentage: item.offline_booking_compare_percentage, date: item.date };
            }
        });
        let finalResult = Object.keys(result).map(key => {
            return { day_name: key, date: result[key].date, online_booking_percentage: (result[key].online_booking_percentage != undefined) ? result[key].online_booking_percentage : 0, offline_booking_percentage: (result[key].offline_booking_percentage != undefined) ? result[key].offline_booking_percentage : 0, online_booking_compare_percentage: (result[key].online_booking_compare_percentage != undefined) ? result[key].online_booking_compare_percentage : 0, offline_booking_compare_percentage: (result[key].offline_booking_compare_percentage != undefined) ? result[key].offline_booking_compare_percentage : 0 };
        });
        callback(finalResult);
    },

    /**
     * Function for get product list name
     */
    get_product_list: function (service_provider_id, callback) {
        con.query("select id as product_id,product_name from tbl_product p where p.service_provider_id = " + service_provider_id + " AND is_deleted = '0' AND publish = 'Yes'", function (err, result) {
            if (!err) {
                if (result.length > 0) {
                    callback('1', "product list found successfully", result);
                } else {
                    callback('0', "Product list not found", null);
                }
            } else {
                callback('0', t('restapi_globals_error'), null);
            }
        })
    },
    getserviceproviderfollowerslist: (request, callback) => {
      const page = request.page - 1
      const limit = page * global.PER_PAGE
      const per_page = global.PER_PAGE
      const filter = request.word ? `
          AND (
            LOWER(tu.first_name) LIKE '%${ request.word.toLowerCase() }%' 
            OR LOWER(tu.last_name) LIKE '%${ request.word.toLowerCase() }%' 
            OR LOWER(tu.email) LIKE '%${ request.word.toLowerCase() }%'
          )
        ` :
        ''
      con.query(`
        SELECT 
          u.*,
          u.id AS user_id,
          b.id AS follow_id,
          DATE_FORMAT(b.insertdate, '%Y-%m-%d %H:%i:%s') AS following_datetime,
          CONCAT('${ global.S3_BUCKET_ROOT }${ global.USER_IMAGE }', u.profile_image) AS follower_image,
          CONCAT(u.first_name, ' ', u.last_name) AS follower_name
        FROM tbl_bookmark AS b
        JOIN tbl_user AS u ON b.user_id = u.id
        WHERE b.service_provider_id = ${ request.service_provider_id }
        AND u.status = 'Active'
        AND NOT u.is_deleted
        AND b.status
        ${ filter }
        ORDER BY b.id DESC
        LIMIT ${ limit }, ${ per_page }
      `, function(err, result) {
        if (!err && result[0]) {
          callback(result)
        } else {
          callback(null)
        }
      })
    },
    saveclientreviews: function (request, callback) {
        common.update_data_condition("tbl_appointment_booking", "id = '" + request.order_id + "'", { user_rating: request.ratting }, function (is_updated) {
            var wherecon = "is_deleted = '0' AND order_id = '" + request.order_id + "' AND service_provider_id = '" + request.service_provider_id + "'";
            if (request.rateto_type == 'Client') {
                wherecon += " AND client_id = '" + request.rateto_id + "'";
            } else {
                wherecon += " AND user_id = '" + request.rateto_id + "'";
            }
            con.query("select * from tbl_client_reviews WHERE " + wherecon, function (err, result) {
                if (!err && result.length > 0) {
                    var updparam = {
                        ratting: request.ratting,
                        updated_datetime: datetime.create().format('Y-m-d H:M:S'),
                    }
                    common.update_data_condition("tbl_client_reviews", "id = '" + result[0].id + "'", updparam, function (is_updated) {
                        if (request.rateto_type == 'Client') {
                            common.select_data("tbl_service_provider_client", "id = '" + request.rateto_id + "'", function (clientdata) {
                                if (!clientdata) {
                                    callback('1', "Review has been submitted successfully", null);
                                } else {
                                    con.query("select count(id) as total_reviews,AVG(ratting) as avg_rating from tbl_client_reviews WHERE is_deleted = '0' AND client_id = '" + request.rateto_id + "' GROUP BY client_id", function (err1, result1) {
                                        if (!err1 && result1[0] != undefined) {
                                            common.update_data_condition("tbl_service_provider_client", "id = '" + request.rateto_id + "'", { total_reviews: result1[0].total_reviews, avg_rating: result1[0].avg_rating, updatetime: datetime.create().format('Y-m-d H:M:S') }, function (is_updated) {
                                                if (clientdata[0].is_user_app > 0) {
                                                    con.query("select count(id) as total_reviews,AVG(ratting) as avg_rating from tbl_client_reviews WHERE is_deleted = '0' AND user_id = '" + clientdata[0].is_user_app + "' GROUP BY user_id", function (err2, result2) {
                                                        if (!err1 && result2[0] != undefined) {
                                                            common.update_data_condition("tbl_user", "id = '" + clientdata[0].is_user_app + "'", { total_reviews: result2[0].total_reviews, avg_rating: result2[0].avg_rating, updatetime: datetime.create().format('Y-m-d H:M:S') }, function (is_updated) {
                                                                callback('1', "Review has been submitted successfully", null);
                                                            });
                                                        } else {
                                                            callback('1', "Review has been submitted successfully", null);
                                                        }
                                                    });
                                                } else {
                                                    callback('1', "Review has been submitted successfully", null);
                                                }
                                            });
                                        } else {
                                            callback('1', "Review has been submitted successfully", null);
                                        }
                                    });

                                }
                            });
                        } else {
                            con.query("select count(id) as total_reviews,AVG(ratting) as avg_rating from tbl_client_reviews WHERE is_deleted = '0' AND user_id = '" + request.rateto_id + "' GROUP BY user_id", function (err1, result1) {
                                if (!err1 && result1[0] != undefined) {
                                    common.update_data_condition("tbl_user", "id = '" + request.rateto_id + "'", { total_reviews: result1[0].total_reviews, avg_rating: result1[0].avg_rating, updatetime: datetime.create().format('Y-m-d H:M:S') }, function (is_updated) {
                                        con.query("select count(id) as total_reviews,AVG(ratting) as avg_rating from tbl_client_reviews WHERE is_deleted = '0' AND user_id = '" + request.rateto_id + "' AND service_provider_id = '" + request.service_provider_id + "' GROUP BY user_id", function (err2, result2) {
                                            if (!err1 && result2[0] != undefined) {
                                                common.update_data_condition("tbl_service_provider_client", "is_deleted = '0' AND is_user_app = '" + request.rateto_id + "' AND service_provider_id = '" + request.service_provider_id + "'", { total_reviews: result1[0].total_reviews, avg_rating: result1[0].avg_rating, updatetime: datetime.create().format('Y-m-d H:M:S') }, function (is_updated) {
                                                    callback('1', "Review has been submitted successfully", null);
                                                });
                                            } else {
                                                callback('1', "Review has been submitted successfully", null);
                                            }
                                        });
                                    });
                                } else {
                                    callback('1', "Review has been submitted successfully", null);
                                }
                            });
                        }
                    });
                } else {
                    if (request.rateto_type == 'Client') {
                        common.select_data("tbl_service_provider_client", "id = '" + request.rateto_id + "'", function (clientdata) {
                            if (!clientdata) {
                                callback('0', "No client details found", null);
                            } else {
                                var rateparam = {
                                    order_id: request.order_id,
                                    service_provider_id: request.service_provider_id,
                                    client_id: request.rateto_id,
                                    user_id: clientdata[0].is_user_app,
                                    ratting: request.ratting,
                                    is_deleted: '0',
                                    updated_datetime: datetime.create().format('Y-m-d H:M:S'),
                                    inserted_datetime: datetime.create().format('Y-m-d H:M:S'),
                                }
                                common.single_insert_data("tbl_client_reviews", rateparam, function (ratedata) {
                                    con.query("select count(id) as total_reviews,AVG(ratting) as avg_rating from tbl_client_reviews WHERE is_deleted = '0' AND client_id = '" + request.rateto_id + "' GROUP BY client_id", function (err1, result1) {
                                        if (!err1 && result1[0] != undefined) {
                                            common.update_data_condition("tbl_service_provider_client", "id = '" + request.rateto_id + "'", { total_reviews: result1[0].total_reviews, avg_rating: result1[0].avg_rating, updatetime: datetime.create().format('Y-m-d H:M:S') }, function (is_updated) {
                                                if (clientdata[0].is_user_app > 0) {
                                                    con.query("select count(id) as total_reviews,AVG(ratting) as avg_rating from tbl_client_reviews WHERE is_deleted = '0' AND user_id = '" + clientdata[0].is_user_app + "' GROUP BY user_id", function (err2, result2) {
                                                        if (!err1 && result2[0] != undefined) {
                                                            common.update_data_condition("tbl_user", "id = '" + clientdata[0].is_user_app + "'", { total_reviews: result2[0].total_reviews, avg_rating: result2[0].avg_rating, updatetime: datetime.create().format('Y-m-d H:M:S') }, function (is_updated) {
                                                                callback('1', "Review has been submitted successfully", null);
                                                            });
                                                        } else {
                                                            callback('1', "Review has been submitted successfully", null);
                                                        }
                                                    });
                                                } else {
                                                    callback('1', "Review has been submitted successfully", null);
                                                }
                                            });
                                        } else {
                                            callback('1', "Review has been submitted successfully", null);
                                        }
                                    });
                                });
                            }
                        });
                    } else {
                        common.select_data("tbl_service_provider_client", "is_deleted = '0' AND is_user_app = '" + request.rateto_id + "' AND service_provider_id = '" + request.service_provider_id + "'", function (clientdata) {
                            var rateparam = {
                                order_id: request.order_id,
                                service_provider_id: request.service_provider_id,
                                client_id: (!clientdata) ? 0 : clientdata[0].id,
                                user_id: request.rateto_id,
                                ratting: request.ratting,
                                is_deleted: '0',
                                updated_datetime: datetime.create().format('Y-m-d H:M:S'),
                                inserted_datetime: datetime.create().format('Y-m-d H:M:S'),
                            }
                            common.single_insert_data("tbl_client_reviews", rateparam, function (ratedata) {
                                con.query("select count(id) as total_reviews,AVG(ratting) as avg_rating from tbl_client_reviews WHERE is_deleted = '0' AND user_id = '" + request.rateto_id + "' GROUP BY user_id", function (err1, result1) {
                                    if (!err1 && result1[0] != undefined) {
                                        common.update_data_condition("tbl_user", "id = '" + request.rateto_id + "'", { total_reviews: result1[0].total_reviews, avg_rating: result1[0].avg_rating, updatetime: datetime.create().format('Y-m-d H:M:S') }, function (is_updated) {
                                            con.query("select count(id) as total_reviews,AVG(ratting) as avg_rating from tbl_client_reviews WHERE is_deleted = '0' AND user_id = '" + request.rateto_id + "' AND service_provider_id = '" + request.service_provider_id + "' GROUP BY user_id", function (err2, result2) {
                                                if (!err1 && result2[0] != undefined) {
                                                    common.update_data_condition("tbl_service_provider_client", "is_deleted = '0' AND is_user_app = '" + request.rateto_id + "' AND service_provider_id = '" + request.service_provider_id + "'", { total_reviews: result1[0].total_reviews, avg_rating: result1[0].avg_rating, updatetime: datetime.create().format('Y-m-d H:M:S') }, function (is_updated) {
                                                        callback('1', "Review has been submitted successfully", null);
                                                    });
                                                } else {
                                                    callback('1', "Review has been submitted successfully", null);
                                                }
                                            });
                                        });
                                    } else {
                                        callback('1', "Review has been submitted successfully", null);
                                    }
                                });
                            });
                        });
                    }
                }
            })
        });
    },

    /*
     Function for add clients from imported contacts list
    */
    addclientsfromimportedcontacts: function (request, callback) {
        if (request.contacts != null && request.contacts != undefined && request.contacts.length > 0) {
            var clients = [];
            asyncloop(request.contacts, function (item, next) {
                con.query("SELECT * FROM tbl_user WHERE CONCAT(country_code,phone) = '" + item.phone + "' AND is_deleted = '0'", function (error, user) {
                    con.query("SELECT * FROM tbl_service_provider_client WHERE service_provider_id = '" + request.service_provider_id + "' AND CONCAT(country_code,phone) = '" + item.phone + "' AND is_deleted = '0'", function (error1, client) {
                        if (!error1 && client[0] != undefined) {
                            next();
                        } else {
                            if (item.name != "") {
                                var user_id = (!error && user[0] != undefined) ? user[0].id : 0;
                                var country_code = (!error && user[0] != undefined) ? user[0].country_code : '';
                                var phone = (!error && user[0] != undefined) ? user[0].phone : item.phone;
                                var email = (item.email != undefined && item.email != "") ? item.email : "";
                                var tempdata = [request.service_provider_id, item.name, 'default-user.png', country_code, phone, email, '', 0, 0, user_id, 0, datetime.create().format('Y-m-d H:M:S'), datetime.create().format('Y-m-d H:M:S')];
                                clients.push(tempdata);
                                next();
                            } else {
                                next();
                            }
                        }
                    });
                });
            }, function () {
                if (clients.length > 0) {
                    con.query("INSERT INTO tbl_service_provider_client(service_provider_id,customer_name,profile_image,country_code,phone,email,notes,total_reviews,avg_rating,is_user_app,is_deleted,updatetime,insertdate) VALUES ?", [clients], function (err, result, fields) {
                        callback('1', 'Contact has been imported succcessfully', null);
                    });
                } else {
                    callback('0', 'Selected contacts already added in your my clients', null);
                }
            });
        } else {
            callback('0', 'No Contact Imported', null);
        }
    },

    /*
    * Function for check block slot availability; probably Deprecated
    */
    checkblockslotavailability: function (request, callback) {
        console.log(`${request.date} : ${request.start_time} : ${request.timezone}`)
        var localstarttime = momentTimezone(request.date + ' ' + request.start_time).tz(request.timezone).format("YYYY-MM-DD HH:mm");
        var localendtime = momentTimezone(request.date + ' ' + request.end_time).tz(request.timezone).format("YYYY-MM-DD HH:mm");
        const query = `
        SELECT *
        FROM tbl_appointment_booking
        WHERE is_deleted = '0'
        AND service_provider_id = '${request.service_provider_id}'
        AND booking_status NOT IN ('Cancelled','No Show','Paid','Completed')
        AND (
            (('${localstarttime}' BETWEEN DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00', '${request.timezone_diff}'),'%Y-%m-%d %H:%i')
                AND DATE_FORMAT(CONVERT_TZ(end_datetime,'+00:00', ${request.timezone_diff}),'%Y-%m-%d %H:%i'))
                OR ('${localendtime}' BETWEEN DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00', '${request.timezone_diff}'),'%Y-%m-%d %H:%i')
                    AND DATE_FORMAT(CONVERT_TZ(end_datetime,'+00:00', '${request.timezone_diff}'),'%Y-%m-%d %H:%i')))
                OR ((DATE_FORMAT(CONVERT_TZ(CONCAT(date,' ',slot_time),'+00:00', '${request.timezone_diff}'),'%Y-%m-%d %H:%i') BETWEEN '${localstarttime}' AND '${localendtime}')
                OR (DATE_FORMAT(CONVERT_TZ(end_datetime,'+00:00','${request.timezone_diff}'),'%Y-%m-%d %H:%i') BETWEEN '${localstarttime}' AND '${localendtime}'))
        )
        `
        con.query(query, [], function(err, results, fields) {
                if (!err && results[0] != undefined) {
                    callback(results[0]);
                } else {
                    callback(null);
                }
            }
        );

    },

    /*
    *Function for get service provider list for review
    */
    getallreviewoptionlist: function(request, callback) {
        con.query("select *,id as review_id from tbl_review_list where user_type = '"+request.user_type+"' AND status = 'Active' AND is_deleted = '0'", function(err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else if (result[0] != undefined && result[0] != "") {
                callback('1', 'Review list found successfully', result);
            } else {
                callback('0', 'Review list not found', null);
            }
        })
    },

    /*
    * Function for get service provider slider image
    */
    getspsliderimages:function(service_provider_id,callback){
        con.query("SELECT ti.id as image_id,CONCAT('"+global.S3_BUCKET_ROOT+global.SP_IMAGE+"',ti.image_name) as image FROM tbl_image as ti WHERE ti.is_deleted = '0' AND ti.type_id = '"+service_provider_id+"' AND ti.type = 'serviceprovider_sliderimage' ORDER BY ti.id ASC", function(err, result) {
            if (err) {
                console.log("getspsliderimages",err);
                callback('0', t('restapi_globals_error'), null);
            } else if (result[0] != undefined && result[0] != "") {
                callback('1', 'Slider image list found successfully', result);
            } else {
                callback('2', 'Slider image list not found', null);
            }
        });
    },

    /*
    * Function for save sp slider image
    */
    savesp_sliderimage:function(request,callback){
        var slider_image = {
            image_name: request.image_name,
            type_id: request.service_provider_id,
            type: 'serviceprovider_sliderimage',
            is_deleted: '0',
            insertdate: datetime.create().format('Y-m-d H:M:S'),
        }
        if(request.image_id != undefined && request.image_id != ""){
            common.update_data('tbl_image', request.image_id, slider_image, function (is_update) {
                service_provider.getspsliderimages(request.service_provider_id,function(code,message,responsedata){
                    callback(code,message,responsedata);
                });
            });
        } else {
            common.single_insert_data("tbl_image",slider_image,function(image_id){
                service_provider.getspsliderimages(request.service_provider_id,function(code,message,responsedata){
                    callback(code,message,responsedata);
                });
            });
        }
    },

    /*
    * Function for remove sp slider image
    */
    removesp_sliderimage:function(request,callback){
        common.update_data('tbl_image', request.image_id, {is_deleted:'1',insertdate: datetime.create().format('Y-m-d H:M:S')}, function (is_update) {
            service_provider.getspsliderimages(request.service_provider_id,function(code,message,responsedata){
                callback(code,message,responsedata);
            });
        });
    },
}

module.exports = service_provider;
