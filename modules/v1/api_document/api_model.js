var con = require('../../../config/database');
var GLOBALS = require('../../../config/constant');

var API = {

    /**
     * Function to get api users list
     * 04-06-2021
     * @param {Function} callback
     */
    apiuserList: function(callback) {

        con.query("SELECT u.*,concat('" + GLOBALS.S3_BUCKET_ROOT + GLOBALS.USER_IMAGE + "','',u.profile_image) as profile_image,IFNULL(ut.device_token,'') as device_token,IFNULL(ut.device_type,'') as device_type,IFNULL(ut.token,'') as token FROM tbl_user u LEFT JOIN tbl_user_deviceinfo as ut ON u.id = ut.user_id WHERE u.status = 'Active' AND u.is_deleted='0' GROUP BY u.id", function(err, result, fields) {
            if (!err) {
                callback(result);
            } else {
                callback(null, err);
            }
        });
    },
    apispList: function(callback) {

        con.query("SELECT u.*,concat('" + GLOBALS.S3_BUCKET_ROOT + GLOBALS.SP_IMAGE + "','',u.profile_image) as profile_image,IFNULL(ut.device_token,'') as device_token,IFNULL(ut.device_type,'') as device_type,IFNULL(ut.token,'') as token FROM tbl_service_provider u LEFT JOIN tbl_user_deviceinfo as ut ON u.id = ut.user_id WHERE u.status = 'Active' AND u.is_deleted='0' GROUP BY u.id", function(err, result, fields) {
            if (!err) {
                callback(result);
            } else {
                callback(null, err);
            }
        });
    }
}

module.exports = API;
