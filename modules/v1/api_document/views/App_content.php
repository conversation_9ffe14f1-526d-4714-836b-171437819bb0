<?php
defined('BASEPATH') OR exit('No direct script access allowed');
/*
** Project      : Homecuts
** Date         : 06-10-2020
** Modified On  : -
*/
class App_content extends MY_Controller {

    /*
    * Default constructor
    */
    private $viewfolder = 'authpanel/app_content/';
	function __construct()
    {
    	parent::__construct();
        $this->load->model('authpanel/content_model');
    }

    /*
    * load a about us page
    */
    function index() {
        redirect('authpanel/app_content/about_us');
    }

    /*
    * load a about us page
    */
    function about_us() {

        $data['app_content']=$this->content_model->get_app_content();
        $this->load->view($this->viewfolder.'about_us',$data);
    }

    /*
    * update a about us content
    */
    function aboutus_update() {

        $data['app_content']=$this->content_model->get_app_content();
        $Allpostdata=$this->input->post();
        if($Allpostdata) {

            $this->form_validation->set_rules('about_content','About Us Content', 'trim|required');
            $this->form_validation->set_error_delimiters('<div class="error">', '</div>');
            if($this->form_validation->run()) {

                $param = array(
                    'about_content'=>$this->input->post('about_content'),

                );
                $this->session->set_flashdata("success_msg",$this->lang->line('text_rest_authpanelaboutupdate_success'));
                $this->content_model->save_app_content($param);

            } else {
                $this->load->view($this->viewfolder.'aboutus_update',$data);
            }
            redirect('authpanel/app_content/about_us');

        } else {
            $this->load->view($this->viewfolder.'aboutus_update',$data);
        }
    }

    /*
    * load a privacy policy us page
    */
    function privacy_policy() {

        $data['app_content']=$this->content_model->get_app_content();
        $this->load->view($this->viewfolder.'privacy_policy',$data);
    }

    /*
    * update privacy policy content
    */
    function privacypolicy_update() {

        $data['app_content']=$this->content_model->get_app_content();
        $Allpostdata=$this->input->post();
        if($Allpostdata) {

            $this->form_validation->set_rules('policy_content','Privacy Policy Content', 'trim|required');

            $this->form_validation->set_error_delimiters('<div class="error">', '</div>');
            if($this->form_validation->run()) {

                $param = array(
                    'policy_content'=>$this->input->post('policy_content'),
                );
                $this->session->set_flashdata("success_msg",$this->lang->line('text_rest_authpanelprivacyupdate_success'));
                $this->content_model->save_app_content($param);
            } else {
                $this->load->view($this->viewfolder.'privacypolicy_update',$data);
            }
            redirect('authpanel/app_content/privacy_policy');
        } else {

            $this->load->view($this->viewfolder.'privacypolicy_update',$data);
        }
    }

    /*
    * load a terms condition us page
    */
    function terms_condition() {
        $data['app_content']=$this->content_model->get_app_content();
        $this->load->view($this->viewfolder.'terms_condition',$data);
    }

    /*
    * update terms condition content
    */
    function termscondition_update() {

        $data['app_content']=$this->content_model->get_app_content();
        $Allpostdata=$this->input->post();
        if($Allpostdata) {

            $this->form_validation->set_rules('condition_content','Terms & Condition', 'trim|required');

            $this->form_validation->set_error_delimiters('<div class="error">', '</div>');
            if($this->form_validation->run()) {

                $param = array(
                    'condition_content'=>$this->input->post('condition_content'),
                );
                $this->session->set_flashdata("success_msg",$this->lang->line('text_rest_authpaneltermsupdate_success'));
                $this->content_model->save_app_content($param);
            } else {
                $this->load->view($this->viewfolder.'termscondition_update',$data);
            }
            redirect('authpanel/app_content/terms_condition');

        } else {
            $this->load->view($this->viewfolder.'termscondition_update',$data);
        }
    }
}
