<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
    <head>
        <meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
        <link href="https://unpkg.com/vuetify@1.0.17/dist/vuetify.min.css" type="text/css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Material+Icons" rel="stylesheet">
        <link rel="shortcut icon" href="<%= GLOBALS.BASE_URL_WITHOUT_API + GLOBALS.LOGO%>">
        <title>
            <%= GLOBALS.APP_NAME %> API LIST</title>
        <style type="text/css">
            .medium-text {
                font-size: 18px;
                color: white;
            }
            .js div#preloader {
                position: fixed;
                left: 0;
                top: 0;
                z-index: 999;
                width: 100%;
                height: 100%;
                overflow: visible;
                background: #fff url('<%= GLOBALS.BASE_URL_WITHOUT_API %>api/public/logo/ripple.gif') no-repeat center center;
            }
        </style>
    </head>
    <body class="js">
        <div id="preloader"></div>
        <div id="app">
            <v-app id="inspire">
                <v-layout justify column>
                    <v-flex style="margin:10px auto 10px auto; padding:0px; text-align:center;" center right sm2 xs2 md2 lg2 xl2>
                        <!-- <img src='<%= GLOBALS.BASE_URL_WITHOUT_API + GLOBALS.LOGO %>' width="120px"><br> -->
                        <strong style="font-size:24px;"><%= GLOBALS.APP_NAME %> API LIST</strong><br><br>
                        <v-alert :value="true" outline color="white" style="padding:5px;">
                            <v-chip color="black" text-color="white">
                                <v-avatar>
                                    <v-icon>bookmark</v-icon>
                                </v-avatar><b>TOTAL {{messages.length}} APIs</b>
                            </v-chip>
                        </v-alert>
                        <v-alert :value="true" outline color="white" style="padding:5px;">
                            <v-chip color="black" text-color="white">
                                <v-avatar>
                                    <v-icon>label_important</v-icon>
                                </v-avatar><b>If Device Token not get, pass 0 only</b>
                            </v-chip>
                            <v-chip color="black" text-color="white">
                                <v-avatar>
                                    <v-icon>label_important</v-icon>
                                </v-avatar><b>Upload images on S3 bucket</b>
                            </v-chip>
                        </v-alert>
                    </v-flex>
                    <v-flex style="margin:0px auto; padding:0px;" center right sm2 xs2 md2 lg2 xl2>

                        <div id="main1" style="margin:0px auto; position:sticky;">
                            <table border="0" cellpadding="10" cellspacing="10" width="100%" style="background: linear-gradient(150deg, #121213 50%, #1a1c27) !important;color: #fff;font-weight: bold;">
                                <tr>
                                    <td><b>Server URL </b></td>
                                    <td>&nbsp;&nbsp;<b><%= GLOBALS.PORT_BASE_URL %>v1/</b></td>
                                </tr>
                                <tr>
                                    <td><b>Header Parameters </b></td>
                                    <td>&nbsp;&nbsp;<b>API-KEY, TOKEN, accept-language</b></td>
                                </tr>
                                <tr>
                                    <td><b>API-KEY </b></td>
                                    <td>&nbsp;&nbsp;<b><%= GLOBALS.API_KEY %></b></td>
                                </tr>
                                <tr>
                                    <td><b>KEY </b></td>
                                    <td>&nbsp;&nbsp;<b><%= GLOBALS.key %></b></td>
                                </tr>
                                <tr>
                                    <td><b>IV </b></td>
                                    <td>&nbsp;&nbsp;<b><%= GLOBALS.iv %></b></td>
                                </tr>
                            </table>
                            <br> <br>
                        </div>
                    </v-flex>
                    <v-flex style="margin:0px auto;" center right sm2 xs2 md2 lg2 xl2>
                        <a target="_blank" href='/v1/api_document/user_list'>
                            <v-btn color="blue-grey" class="white--text">
                                USER LIST
                                <v-icon right dark>people</v-icon>
                            </v-btn>
                        </a>
                        <a target="_blank" href='/v1/api_document/sp_list'>
                            <v-btn color="blue-grey" class="white--text">
                                SP LIST
                                <v-icon right dark>people</v-icon>
                            </v-btn>
                        </a>
                        <a target="_blank" href='/v1/api_document/code'>
                            <v-btn color="blue-grey" class="white--text">
                                CODE REFERENCE
                                <v-icon right dark>code</v-icon>
                            </v-btn>
                        </a>
                        <a target="_blank" href='<%= GLOBALS.BASE_URL %>api/enc_dec.php'>
                            <v-btn color="blue-grey" class="white--text">
                                ENCRYPTION URL
                                <v-icon right dark>lock</v-icon>
                            </v-btn>
                        </a>
                    </v-flex>

                    <v-expansion-panel popout>
                        <v-expansion-panel-content v-for="(message, i) in messages" :key="i" hide-actions>
                            <v-layout slot="header" align-center row spacer>
                                <v-flex sm1 xs1 md1 lg1 xl1>
                                    <v-avatar slot="activator" size="36px">
                                        <v-icon :color="message.color">{{ message.icon }}</v-icon>
                                    </v-avatar>
                                </v-flex>
                                <v-flex sm3 xs3 md3 lg3 xl3>
                                    <strong v-html="message.title"></strong>
                                    <v-chip v-if="message.meth" :color="`${message.color} lighten-4`" label small class="ml-0">
                                        {{ message.meth }}
                                    </v-chip>
                                </v-flex>
                                <v-flex sm3 xs3 md3 lg3 xl3>
                                    <strong v-html="message.name"></strong><br>
                                    <span v-if="message.updated" style="color:orange">{{ message.updated }}</span>
                                    <span v-if="message.new_added" style="color:green"><span v-html="message.new_added"></span></span>
                                </v-flex>
                                <v-flex sm4 xs4 md4 lg4 xl4>
                                    <v-flex v-if="message.link" class="grey--text" ellipsis hidden-sm-and-down>
                                        {{ message.link }}
                                    </v-flex>
                                    <v-flex class="grey--text" ellipsis hidden-sm-and-down>
                                        <span v-if="message.imp" style="color:darkred">SHOW IMPORTANT NOTE INSIDE</span>
                                    </v-flex>
                                </v-flex>
                            </v-layout>
                            <v-card dark>
                                <v-container>
                                    <v-layout v-if="message.new_tag === '0'">
                                        <v-flex xs12 align-end flexbox>
                                            <v-alert v-if="message.new_added" :value="true" outline color="white" style="padding:5px;">
                                                <v-chip color="green" text-color="white">
                                                    <v-avatar>
                                                        <v-icon>fiber_new</v-icon>
                                                    </v-avatar> NEW
                                                </v-chip>
                                                <span v-html="message.new_added"></span>
                                            </v-alert>
                                            <v-alert v-if="message.updated" :value="true" outline color="white" style="padding:5px;">
                                                <v-chip color="orange" text-color="white">
                                                    <v-avatar>
                                                        <v-icon>update</v-icon>
                                                    </v-avatar> UPDATED
                                                </v-chip>
                                                <span>{{ message.updated }}</span>
                                            </v-alert>
                                            <v-alert :value="true" outline color="white" style="padding:5px;">
                                                <v-chip color="teal" text-color="white">
                                                    <v-avatar>
                                                        <v-icon>check_circle</v-icon>
                                                    </v-avatar> MANDATORY
                                                </v-chip>
                                                {{message.mandatory}}
                                            </v-alert>
                                            <v-alert :value="true" outline color="white" style="padding:5px;">
                                                <v-chip outline color="#F0F4C3">
                                                    <v-avatar>
                                                        <v-icon>check_circle</v-icon>
                                                    </v-avatar>OPTIONAL
                                                </v-chip>
                                                <span>{{ message.optional }}
    											</v-alert>
    											<v-alert v-if="message.link" :value="true" outline color="white" style="padding:5px;">
    												<v-chip color="light-green darken-2" text-color="white">
    													<v-avatar>
    														<v-icon>link</v-icon>
    													</v-avatar> LINK
    												</v-chip>
    												<span>{{ message.link }}
    											</v-alert>
    											<v-alert v-if="message.imp" :value="true" outline color="white" style="padding:5px;">
    												<v-chip color="red darken-2" text-color="white">
    													<v-avatar>
    														<v-icon>new_releases</v-icon>
    													</v-avatar> IMPORTANT
    												</v-chip>
    												<p v-html="message.imp"></p>
    											</v-alert>
    											<v-alert v-if="message.notes" :value="true" outline color="white" style="padding:5px;">
    												<v-chip color="blue lighten-1" text-color="white">
    													<v-avatar>
    														<v-icon>bookmark</v-icon>
    													</v-avatar> NOTES
    												</v-chip>
    												<span>{{ message.notes }}
    											</v-alert>
    											<v-alert v-if="message.example" :value="true" outline color="white" style="padding:5px;">
    												<v-chip color="red darken-2" text-color="white">
    													<v-avatar>
    														<v-icon>check_circle</v-icon>
    													</v-avatar> EXAMPLE
    												</v-chip>
    												<p v-html="message.example"></p>
    											</v-alert>
    											<v-layout style="margin-top:-4px">
    												<v-flex xs6 align-end flexbox>
    													<v-alert :value="true" outline color="yellow" icon="priority_high" style="padding:5px;">
    															<span class="grey--text">HEADER : {{ message.is_header }}, PUSH : {{ message.is_push }}</span><br>
                                                <span>HEADER : {{ message.header }}</span>
                                            </v-alert>
                                        </v-flex>
                                        <v-flex xs6 align-end flexbox>
                                            <v-alert style="margin-left:4px; padding:5px;" :value="true" outline color="yellow" icon="priority_high">
                                                STATUS : <span v-html="message.status"></span>
                                            </v-alert>
                                        </v-flex>
                                    </v-layout>
                                    </v-flex>
                </v-layout>
                </v-container>
                <v-card-actions>
                </v-card-actions>
                </v-card>
                </v-expansion-panel-content>
                </v-expansion-panel>
                </v-layout>
            </v-app>
        </div>
        <div id="asd"></div>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/2.5.0/vue.min.js"></script>
        <script src="https://unpkg.com/vuetify@1.0.17/dist/vuetify.min.js"></script>
        <script>
            jQuery(document).ready(function($) {
                $('#preloader').fadeOut('slow', function() {
                    $(this).remove();
                });
            });

            var base_url = "<%= GLOBALS.PORT_BASE_URL %>api/v1/";
            var base_url_without_api = "<%= GLOBALS.BASE_URL_WITHOUT_API %>";
            let i = 1;

            new Vue({
                el: '#app',
                data: () => ({
                    messages: [

                        {
                            new_tag: '1',
                            color: 'teal',
                            title: 'COMMON API',
                            icon: 'person',
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'COUNTRY LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/country_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get country list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'AMENITIES',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/get_amenities",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get amenities list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'WORKSPACE CATEGORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/get_workspace_category",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get workspace category list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'TAX PECENTAGE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/tax_percentage",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get tax percentage",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'TRANSACTION CHARGE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/transaction_charge",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get Transaction Charge",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'Booth Rent Tax',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/booth_rent_tax",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get Booth Rent Tax",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'Booth Rent Cleaning Fees',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/booth_rent_cleaning_fees",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get Booth Rent Cleaning Fees",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'POST COMMENTS LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/get_post_comments",
                            mandatory: "post_id,page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get post comments",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'COMMENT IN POST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/post_comment",
                            mandatory: "post_id,comment,type(user,service_provider)",
                            optional: "reply_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to add post comments",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'LIKE POST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/post_like",
                            mandatory: "post_id,type(user,service_provider),status(0,1)",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to like post",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'POST REPORT LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/post_report_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get post report list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'TILES LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/tiles_list",
                            mandatory: "",
                            optional: "is_boothowner['0','1']",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get tiles name for report",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'SAVE TILES',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/save_tiles",
                            mandatory: "",
                            optional: "is_boothowner['0','1']",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to save tiles for service provider",
                            example: '{"tiles":[{"tiles_id":"1"},{"tiles_id":"2"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'GRAPH LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/graph_list",
                            mandatory: "",
                            optional: "is_boothowner['0','1']",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get graph list for service provider",
                            example: '{"tiles":[{"tiles_id":"1"},{"tiles_id":"2"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'SAVE GRAPH',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/save_graph",
                            mandatory: "",
                            optional: "is_boothowner['0','1']",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to save graph for service provider",
                            example: '{"graph":[{"graph_id":"1"},{"graph_id":"2"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'Read Notification',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/readnotification",
                            mandatory: "notification_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to save Read Notification",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'person',
                            title: i++ + ' : COMMON',
                            name: 'Check App Update',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/common/checkappupdate",
                            mandatory: "user_type[Service_provider,User],device_type[A,I],app_version",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to Check App Update",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        },{
                            new_tag: '1',
                            color: 'blue',
                            title: 'USER API',
                            icon: 'person',
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'SIGNUP',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/sign_up",
                            mandatory: "first_name,last_name,country_code,phone,email,login_type[S,F,G,A],device_type[A,I],device_token",
                            optional: "password,latitude,longitude,uuid,ip,os_version,model_name,social_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to SIGNUP",
                            example: 'IN login type S for simple, F for Facebook , G for google, A for Apple',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'LOGIN',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/login",
                            mandatory: "device_type[A,I],device_token,login_type[S,F,G,A]",
                            optional: "email,phone,password,latitude,longitude,uuid,ip,os_version,model_name,social_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to LOGIN, Social id required if login type is not S",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "code => 0 Invalid id or password, code => 1 Login sucess,code => 3 Inactive user, code => 4 OTP verify Pending  ",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Send user otp',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/send_otp",
                            mandatory: "user_id,status(signup,forgotpassword)",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to Verify users otp",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Verify user otp',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/verify_otp",
                            mandatory: "user_id,otp,status(signup,forgotpassword)",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to Verify users otp",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Get Users Details',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/user_details",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to Get Users Details",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Forgot Password',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/forgot_password",
                            mandatory: "email",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to Forgot Password",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Change Password',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/change_password",
                            mandatory: "old_password,new_password",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to Change Password",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Create New Password',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/newpassword",
                            mandatory: "password,user_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to create New Password",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Update Location',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/updatedeviceinfo",
                            mandatory: "latitude,longitude,device_token,device_type",
                            optional: "uuid,ip,os_version,model_name",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to Update Location",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Edit profile',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/edit_profile",
                            mandatory: "first_name,last_name,email,country_code,phone",
                            optional: "profile_image",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to Edit user profile",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Home screen',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/homescreen",
                            mandatory: "page,latitude,longitude",
                            optional: "word,alphabetical,ratting,distance_sort,price_sort,start_radius,end_radius,,start_price(for price range pass start and end price both),end_price,category_id,category_type(Product,Service),time,time_sort[ASC,DESC],min_discount,max_discount",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used get home screen data",
                            example: 'In alphabetical,price_sort AND ratting pass (ASC,DESC) ASC for low to high or A to Z and DESC for high to low or Z to a',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'SERVICE PROVIDER DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/service_provider_detail",
                            mandatory: "service_provider_id,page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used get sp detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'USER BOOKING',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/make_userbookingpayment",
                            mandatory: "service_provider_id,business_location_id,description,tax,sub_total,is_tip_after_service,payment_from(ApplePay,GooglePay),total_duration,transaction_id",
                            optional: "time_slot,service,tip,promocode,discount,date,wallet_amount,total_amount,product,payment_mode(pay_later,pay_now)",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to change workspace status",
                            example: '{"service_provider_id":"1","business_location_id":"1","is_tip_after_service":"1","payment_mode":"pay_now","wallet_amount":"0","card_id":"1","time_slot":"12:00","description":"This is description","tip":"200","tax":"100","promocode":"Testing","sub_total":"10000","discount":"200","total_amount":"1100","service":[{"service_id":"1","price":"100"},{"service_id":"2","price":"200"}],"image":[{"image_name":"default-user.png"},{"image_name":"default-user.png"}],"product":[{"product_id":"1","price":"100","quantity":"1","subproduct_id":"2"},{"product_id":"2","price":"200","subproduct_id":"2","quantity":"2"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        },{
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'USER BOOKING',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/user_booking",
                            mandatory: "service_provider_id,business_location_id,description,tax,sub_total,is_tip_after_service,payment_from(Cash,Card,Wallet,ApplePay,GooglePay),total_duration",
                            optional: "time_slot,service,tip,promocode,discount,date,wallet_amount,total_amount,product,card_id,payment_mode(pay_later,pay_now),transaction_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to change workspace status",
                            example: '{"service_provider_id":"1","business_location_id":"1","is_tip_after_service":"1","payment_mode":"pay_now","wallet_amount":"0","card_id":"1","time_slot":"12:00","description":"This is description","tip":"200","tax":"100","promocode":"Testing","sub_total":"10000","discount":"200","total_amount":"1100","service":[{"service_id":"1","price":"100"},{"service_id":"2","price":"200"}],"image":[{"image_name":"default-user.png"},{"image_name":"default-user.png"}],"product":[{"product_id":"1","price":"100","quantity":"1","subproduct_id":"2"},{"product_id":"2","price":"200","subproduct_id":"2","quantity":"2"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'LOGOUT',
                            meth: 'GET',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/logout",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to LOGOUT",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'ADD UPDATE BOOKMARK',
                            meth: 'GET',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/add_update_bookmark",
                            mandatory: "service_provider_id,status",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to add / update bookmark",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'ALL SERVICE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/all_service",
                            mandatory: "service_provider_id",
                            optional: "page",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get all service",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'ALL PRODUCT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/all_product",
                            mandatory: "service_provider_id,page",
                            optional: "category_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get all product",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'GET AVAILABLE SLOT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/get_available_slot",
                            mandatory: "business_location_id,date,service_provider_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get all product",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'CATEGORY LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/category_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get all product category",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'ADD PRODUCT/SERVICE BOOKMARK',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/product_service_bookmark",
                            mandatory: "type(product,service),status,type_id(if type is product then pass product id,if type is service then pass service id)",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to add product / service to bookmark",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'ADD USER CARD',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/add_card",
                            mandatory: "holder_name,number,expiry_month,expiry_year,cvv",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to add user card",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'UPDATE USER CARD',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/update_card",
                            mandatory: "card_id,type(delete,update)",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to update user card",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'CUSTOMER CARD LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/customer_card_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get user card list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'APPLY PROMOCODE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/applypromocode",
                            mandatory: "promocode,sub_total,service_provider_id",
                            optional: "product_id,service_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to apply promocode",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'MY BOOKING',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/my_booking",
                            mandatory: "page",
                            optional: "word,start_date,end_date",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get my service booking",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'MY PRODUCT BOOKING',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/my_product_booking",
                            mandatory: "page",
                            optional: "word,start_date,end_date",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get my product booking",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'SP FAVOURITE LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/favourite_list",
                            mandatory: "page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get service provider favourite list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'GIFTCARD LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/giftcard_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get giftcard list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'BOOKING DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/booking_detail",
                            mandatory: "appointment_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get booking detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'PORTFOLIO LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/portfolio_list",
                            mandatory: "page,service_provider_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get portfolio list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'PRODUCT DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/product_detail",
                            mandatory: "product_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get product detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'WALLET AMOUNT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/wallet_amount",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get wallet amount",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'REDEEM GIFTCARD',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/redeem_giftcard",
                            mandatory: "giftcard_code",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to redeem giftcard",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'MY GIFTCARD',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/my_giftcard",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to my giftcard list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'GIFTCARD DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/giftcard_detail",
                            mandatory: "giftcard_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get giftcard detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'PURCHASE GIFTCARD',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/purchase_giftcard",
                            mandatory: "giftcard_id,sub_total,transaction_charge,total_amount,card_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to redeem giftcard",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'CANCEL REASON LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/cancel_reason_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get cancel reason list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'CANCEL APPOINTMENT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/cancel_appointment",
                            mandatory: "appointment_id",
                            optional: "cancel_id,cancel_reason",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to cancel appointment",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'REPORT LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/report_reason_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get report reason list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'REPORT SERVICE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/report_service",
                            mandatory: "appointment_id",
                            optional: "report_id,report_reason",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to report service/product",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                            // }, {
                            //     new_tag: '0',
                            //     color: 'blue',
                            //     icon: 'person',
                            //     title: i++ + ' : USER',
                            //     name: 'SERVICE PROVIDER LIST FOR REVIEW',
                            //     meth: 'POST',
                            //     link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/service_provider_review_list",
                            //     mandatory: "",
                            //     optional: "",
                            //     is_header: "YES",
                            //     is_push: "NO",
                            //     header: "API-KEY,TOKEN",
                            //     notes: "This api is used to get review list",
                            //     example: '',
                            //     status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            //     imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'SERVICE PROVIDER DETAIL REVIEW',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/service_provider_detail_review",
                            mandatory: "service_provider_id,page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get sp detail review",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'ADD USER RATTING/REVIEW',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/add_review",
                            mandatory: "ratting,service_provider_id,review_id,type_id,type,order_id",
                            optional: "review",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to add review",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "review id pass in comma seperated",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Update USER RATTING/REVIEW',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/edit_review",
                            mandatory: "ratting,service_provider_id,review_id,type_id,type,order_id",
                            optional: "review",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to Update review",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "review id pass in comma seperated",
                        },{
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'FEED/PORTFOLIO LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/feed_list",
                            mandatory: "page",
                            optional: "service_provider_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get feed list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'PROMOTION LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/get_promotion",
                            mandatory: "page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get promotion list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'PROMOTION DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/promotion_detail",
                            mandatory: "promotion_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get promotion detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'WALLET HISTORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/wallet_history",
                            mandatory: "page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get wallet history",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'PRODUCT/SERVICE FAVOURITE LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/product_favourite_list",
                            mandatory: "page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get product/service favourite list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'USER BOOKING',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/product_booking",
                            mandatory: "service_provider_id,business_location_id,description,tax,sub_total,payment_from(Cash,Card,Wallet,ApplePay,GooglePay)",
                            optional: "tip,promocode,discount,wallet_amount,total_amount,product,card_id,payment_mode(pay_later,pay_now),total_duration",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to change workspace status",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'BOOKING PAYMENT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/booking_payment",
                            mandatory: "appointment_id,tax,sub_total,card_id,total_amount",
                            optional: "tip,promocode,discount,wallet_amount",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to payment booking",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'RESCHEDULE APPOINTMENT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/reschedule_appointment",
                            mandatory: "appointment_id,business_location_id,time_slot,date,booking_status,service_provider_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to reschedule appointment",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'SERVICE PROVIDER LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/service_provider_list",
                            mandatory: "page,latitude,longitude",
                            optional: "word,time,time_sort[ASC,DESC]",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to sp list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'NOTIFICATION LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/notification_list",
                            mandatory: "page",
                            optional: "is_boothowner['0','1']",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get notification list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'CONTACT US',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/contact_us",
                            mandatory: "subject,email,description",
                            optional: "word",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used contact us",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'USER REVIEW LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/user_review_list",
                            mandatory: "page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get user review list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'POST DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/post_detail",
                            mandatory: "post_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get post details",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'REPORT POST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/report_post",
                            mandatory: "post_id",
                            optional: "report_id,report_reason",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to report post",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'PRODUCT AND SERVICE CATEGORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/product_service_category",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get category of product and service",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'AVAILABLE NOW SLOT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/avilable_now_slot",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get currrently avilable slot",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'AVAILABLE CALENDER DATE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/available_calender_date",
                            mandatory: "month,year,business_location_id,service_provider_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get available calender date",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Follow & Unfollow',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/followunfollow",
                            mandatory: "service_provider_id,action_type[Follow,Unfollow]",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Follow & Unfollow",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Get Following List',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/getfollowinglist",
                            mandatory: "page",
                            optional: "word",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Get Following List",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Delete Account',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/deleteaccount",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Delete Account",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Set Default Location',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/setdefaultlocation",
                            mandatory: "long_address,short_address,latitude,longitude",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Set Default Location",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'Service History',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/user/service_history",
                            mandatory: "page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Service History",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'ABOUT US',
                            meth: 'POST',
                            link: "<%= GLOBALS.SERVER_URL %>home/about_us",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get about us",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'TERMS AND CONDITION',
                            meth: 'POST',
                            link: "<%= GLOBALS.SERVER_URL %>home/terms_and_conditions",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get terms and conditions",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'PRIVACY POLICY',
                            meth: 'POST',
                            link: "<%= GLOBALS.SERVER_URL %>home/privacy_policy",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get privacy policy",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        },  {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'CANCELLATION POLICY',
                            meth: 'POST',
                            link: "<%= GLOBALS.SERVER_URL %>home/cancellation_policy",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get cancellation policy",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'blue',
                            icon: 'person',
                            title: i++ + ' : USER',
                            name: 'FAQ',
                            meth: 'POST',
                            link: "<%= GLOBALS.SERVER_URL %>home/faq",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to get faq",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '1',
                            color: 'green',
                            title: 'SERVICE PROVIDER API',
                            icon: 'person',
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'SIGN UP',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/sign_up",
                            mandatory: "first_name,last_name,country_code,phone,email,login_type[S,F,G,A],account_type(professional,booth_renter,both),device_type[A,I],device_token",
                            optional: "password,latitude,longitude,uuid,ip,os_version,model_name,social_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to sign up",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'COMPLETE PROFILE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/complete_profile",
                            mandatory: "service_provider_id,account_type(professional,booth_renter,both),profile_image,bio,state_issue_id,",
                            optional: "experience,professional_license,business_location,signed_lease,tax",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to complete profile",
                            example: '{ "service_provider_id":6, "profile_image":"default.png", "bio":"This is testing user", "experience":"1", "professional_license":"ABCDEFSD", "state_issue_id":"ADSA45DS", "business_location":[ { "name":"Abcd", "latitude":"", "longitude":"","country_name":"","state_name":"","city_name":"","postal_code":"", "slot_available":[ { "day":"Monday", "from_time":"12:00", "to_time":"12:00" },{ "day":"Tuesday", "from_time":"11:00", "to_time":"01:00" },{ "day":"Wednesday", "from_time":"11:00", "to_time":"01:00" } ], "block_availability":[ { "date":"2022-08-31", "booked_slot":[ { "from_time":"11:00", "to_time":"12:00" },{ "from_time":"4:00", "to_time":"5:00" } ] }, { "date":"2022-09-11", "booked_slot":[ { "from_time":"11:00", "to_time":"12:00" },{ "from_time":"4:00", "to_time":"5:00" } ] } ] } ] }',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "optional field are required when account type is professional AND signed lease required when account type is both renter or both",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'FORGOT PASSWORD',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/forgot_password",
                            mandatory: "email",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to forgot password",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'ADD BANK DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/add_bank_details",
                            mandatory: "service_provider_id,bank_document,additional_document,bank_name,account_holder_name,account_number,routing_number,SSN",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to add bank detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'EDIT PROFILE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/edit_profile",
                            mandatory: "first_name,last_name,email,country_code,phone,account_type(professional,booth_renter,both)",
                            optional: "tax",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to edit profile",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'EDIT SERVICE PROFILE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/edit_service_profile",
                            mandatory: "account_type,bio,state_issue_id,",
                            optional: "experience,professional_license,signed_lease,business_location,remove_business_location_ids",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to edit service profile <br/> remove_business_location_ids pass 1,2,3",
                            example: '{"profile_image":"default.png", "bio":"This is testing user", "experience":"1", "professional_license":"ABCDEFSD", "state_issue_id":"ADSA45DS", "business_location":[ { "id":"","name":"Abcd", "latitude":"", "longitude":"","country_name":"","state_name":"","city_name":"","postal_code":"","slot_available":[ { "day":"Monday", "from_time":"12:00", "to_time":"12:00" },{ "day":"Tuesday", "from_time":"11:00", "to_time":"01:00" },{ "day":"Wednesday", "from_time":"11:00", "to_time":"01:00" } ], "block_availability":[ { "date":"2022-08-31", "booked_slot":[ { "from_time":"11:00", "to_time":"12:00" },{ "from_time":"4:00", "to_time":"5:00" } ] }, { "date":"2022-09-11", "booked_slot":[ { "from_time":"11:00", "to_time":"12:00" },{ "from_time":"4:00", "to_time":"5:00" } ] } ] } ] }',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "optional field are required when account type is professional AND signed lease required when account type is both renter or both",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Save Location Availability',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/savelocationavailability",
                            mandatory: "business_location",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to Save Location Availability",
                            example: '{"business_location":[ { "id":"","name":"Abcd", "latitude":"", "longitude":"","country_name":"","state_name":"","city_name":"","postal_code":"","slot_available":[ { "day":"Monday", "from_time":"12:00", "to_time":"12:00" },{ "day":"Tuesday", "from_time":"11:00", "to_time":"01:00" },{ "day":"Wednesday", "from_time":"11:00", "to_time":"01:00" } ], "block_availability":[ { "date":"2022-08-31", "booked_slot":[ { "from_time":"11:00", "to_time":"12:00" },{ "from_time":"4:00", "to_time":"5:00" } ] }, { "date":"2022-09-11", "booked_slot":[ { "from_time":"11:00", "to_time":"12:00" },{ "from_time":"4:00", "to_time":"5:00" } ] } ] } ] }',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "optional field are required when account type is professional AND signed lease required when account type is both renter or both",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'CHANGE PASSWORD',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/change_password",
                            mandatory: "old_password,new_password",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,TOKEN",
                            notes: "This api is used to change password",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'LOGIN',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/login",
                            mandatory: "device_type[A,I],device_token,login_type[S,F,G,A]",
                            optional: "email,phone,password,latitude,longitude,uuid,ip,os_version,model_name,social_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to LOGIN, Social id required if login type is not S",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "code => 0 Invalid id or password, code => 1 Login sucess,code => 3 Inactive user, code => 4 OTP verify Pending  ",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'LOGOUT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/logout",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used logout",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'UPDATE DEVICE INFO',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/updatedeviceinfo",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used update device info",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'SERVICE PROVIDER DETAILS',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/service_provider_details",
                            mandatory: "",
                            optional: "service_provider_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get service provider details",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'SET FINANCIAL TARGET',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/set_financial_target",
                            mandatory: "amount,target,show_home_target",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to set financial target of service provider",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'GET FINANCIAL TARGET',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/get_financial_target",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get financial target of service provider",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'ADD NEW CLIENT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/add_new_client",
                            mandatory: "profile_image,customer_name,phone,country_code,email,notes",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to add new client",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'ALL CLIENT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/all_client",
                            mandatory: "page",
                            optional: "word",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get all client",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'GET CLIENT DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/get_client_detail",
                            mandatory: "client_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get client detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'EDIT CLIENT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/edit_client",
                            mandatory: "profile_image,customer_name,phone,country_code,email,notes,client_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "   ",
                            notes: "This api is used to edit client",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'PRODUCT CATEGORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/product_category_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get product category",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'SERVICE CATEGORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/service_category_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get service category",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'ADD STORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/add_story",
                            mandatory: "story_name,story_image",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to add story",
                            example: '{"story_name": "Nirav","story_image": [{"image_name": "default.png"},{"image_name": "default.png"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'ADD STORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/edit_story",
                            mandatory: "story_id.story_name,story_image",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to edit story",
                            example: '{"story_name": "Nirav","story_image": [{"image_name": "default.png"},{"image_name": "default.png"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'ADD STORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/get_story_detail",
                            mandatory: "story_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get story detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'DELETE STORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/delete_story",
                            mandatory: "story_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to delete story",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'ALL STORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/story_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get all story",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'ADD PRODUCT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/add_product",
                            mandatory: "product_name,description,category_id,publish,variant_list,product_images",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to add product",
                            example: '{ "product_name": "Test", "description": "Testersf", "category_id": 1, "publish": "yes", "variant_list": [ { "product_size_id": "1","size": "500", "quantity": 100, "price": 200 }, { "product_size_id": "2","size": "100", "quantity": 10, "price": 400 } ], "product_images": [ { "image_name": "default-user.png" },{ "image_name": "default-user.png" },{ "image_name": "default-user.png" } ] }',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'GET PRODUCT SIZE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/get_product_size",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get product size",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'EDIT PRODUCT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/edit_product",
                            mandatory: "product_id,product_name,description,category_id,publish,variant_list,product_images",
                            optional: "remove_variant_ids",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to edit product",
                            example: '{ ""product_id":"1",product_name": "Test", "description": "Testersf", "category_id": 1, "publish": "yes", "variant_list": [ { "subproduct_id":"1","product_size_id": "1","size":"500", "quantity": 100, "price": 200 }, { "subproduct_id":"0","product_size_id": "2","size": "100", "quantity": 10, "price": 400 } ], "product_images": [ { "image_name": "default-user.png" },{ "image_name": "default-user.png" },{ "image_name": "default-user.png" } ] }',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'DELETE PRODUCT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/delete_product",
                            mandatory: "product_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to delete product",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'PRODUCT DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/product_detail",
                            mandatory: "product_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get product detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'ALL PRODUCT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/all_product",
                            mandatory: "page",
                            optional: "category_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get all product",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'ADD SERVICE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/add_service",
                            mandatory: "service_name,description,category_id,price,duration,service_images",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to add service",
                            example: '{ "service_name": "Test", "duration":"08:20","description": "This is demo", "category_id": 1,"price":100, "service_images": [ { "image_name": "default-user.png" },{ "image_name": "default-user.png" },{ "image_name": "default-user.png" } ] }',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'EDIT SERVICE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/edit_service",
                            mandatory: "service_id,service_images,service_name,description,category_id,price,duration",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to edit service",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'DELETE SERVICE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/delete_service",
                            mandatory: "service_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to delete service",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'SERVICE DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/service_detail",
                            mandatory: "service_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get service detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'SERVICE LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/all_service",
                            mandatory: "",
                            optional: "category_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get all service",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'CREATE NEW BOOKING',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/create_new_booking",
                            mandatory: "customer_name,business_location_id,client_id,date,time_slot,description,tax,sub_total,total_duration",
                            optional: "service,tip,promocode,discount,total_amount,payment_mode(Cash,Card),payment_intent_id(required when payment mode is card)",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to booked service",
                            example: '{"customer_name":"Nirav","business_location_id":"1","date":"2022-11-11","time_slot":"12:00","description":"This is description","tip":"200","tax":"100","sub_total":"10000","discount":"200","total_amount":"1100","service":[{"service_id":"1","price":"100"},{"service_id":"2","price":"200"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'QUICK CHECKOUT BOOKING',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/quick_checkout_booking",
                            mandatory: "customer_id,business_location_id,description,tax,sub_total,payment_mode(Cash,Card),payment_intent_id(required when payment mode is card),total_duration",
                            optional: "service,tip,promocode,discount,wallet_amount,total_amount,product,image",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to booked service",
                            example: '{"customer_id":"1","business_location_id":"1","description":"This is description","tip":"200","tax":"100","sub_total":"10000","discount":"200","total_amount":"1100","service":[{"service_id":"1","price":"100"},{"service_id":"2","price":"200"}],"image":[{"image_name":"default-user.png"},{"image_name":"default-user.png"}],"product":[{"product_id":"1","price":"100","subproduct_id":"2","quantity":"1"},{"product_id":"2","price":"200","subproduct_id":"2","quantity":"2"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'PAYMENT INTENT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/create_payment_intent",
                            mandatory: "payment_from[Card,ApplePay,GooglePay],amount,sub_total,tax,total_amount",
                            optional: "card_holder_name,transaction_id,card_number,expiry_month,expiry_year,cvv,discount,order_type[Booking,Booth],cleaning_fee",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to payment intent",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'MY APPOINTMENT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/my_appointments",
                            mandatory: "",
                            optional: "last_name,first_name,phone_number,email,date,sort_by_name[ASC,DESC]",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to payment intent",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'BOOKING DETAILS',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/booking_details",
                            mandatory: "appointment_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get booking detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'CANCEL REASON LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/cancel_reason_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to cancel reason list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'CANCEL APPOINTMENT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/cancel_appointment",
                            mandatory: "appointment_id",
                            optional: "cancel_id,cancel_reason",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to cancel appointment",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'HOME SCREEN',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/homescreen",
                            mandatory: "",
                            optional: "date",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get home screen data",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'APPLY PROMOCODE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/applypromocode",
                            mandatory: "promocode,sub_total",
                            optional: "product_id,service_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to apply promocode",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'CREATE POST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/create_post",
                            mandatory: "post_images,caption,description",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to create post",
                            example: '{ "caption": "Hair style", "description":"this is trending hair style","post_images": [ { "image_name": "default-user1.png" },{ "image_name": "default-user.png" },{ "image_name": "default-user.png" } ] }',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'POST LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/post_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get post list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'POST DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/post_detail",
                            mandatory: "post_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get post details",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'CREATE OCCATIONAL PROMO',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/create_occational_promo",
                            mandatory: "occational_promo_image,discount_type[Percentage,Flat],discount_amount,description,promocode,valid_till",
                            optional: "title,max_discount_amount,applied_on,applied_service_on",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to create occational promo",
                            example: '{"occational_promo_image":"default-user.png","discount_amount":"100","description":"This is discription","promocode":"TEST","valid_till":"2022-10-23","applied_on":"1","applied_service_on":"1","client_list":[{"client_id":1},{"client_id":3}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'GET OCCATIONAL PROMO LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/get_occational_promo",
                            mandatory: "page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get occational promo",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'DELETE OCCATIONAL PROMO',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/delete_occational_promo",
                            mandatory: "occational_promo_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to delete occational promo",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'OCCATIONAL PROMO DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/occational_promo_detail",
                            mandatory: "occational_promo_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get occational promo details",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'GET AVAILABLE SLOTS',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/get_available_slot",
                            mandatory: "business_location_id,date,total_duration",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get available slots",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'CHANGE BOOKING STATUS',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/change_booking_status",
                            mandatory: "appointment_id,status",
                            optional: "image(pass when status is complete),description_notes",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to change booking status",
                            example: '{"image":[{"image_name":"default-user.png"},{"image_name":"default-user.png"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'NOTIFICATION LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/notification_list",
                            mandatory: "page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get notification list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'SERVICE HISTORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/service_history",
                            mandatory: "client_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get service history",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'PRODUCT HISTORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/product_history",
                            mandatory: "client_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get product history",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'CALENDER DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/calender_detail",
                            mandatory: "month,year",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get calender detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'DELETE POST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/delete_post",
                            mandatory: "post_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used delete post",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'CLIENT APPOINTMENT DETAILS',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/client_appointment_details",
                            mandatory: "appointment_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get client appointment detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'PRODUCT PURCHASE DETAILS',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/product_purchase_detail",
                            mandatory: "appointment_detail_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get product purchase detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'REVIEW LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/review_list",
                            mandatory: "page",
                            optional: "word(for name),ratting,sorting(pass ASC for oldest and pass DESC for newest),date,category_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get review list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'REVIEW DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/review_detail",
                            mandatory: "review_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get review detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'REVIEW REPLY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/review_reply",
                            mandatory: "review_id,reply",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used add review reply",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'SALES HISTORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/sales_history",
                            mandatory: "page",
                            optional: "word(pass name),date",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get sales history",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'RENT BOOTH',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/rent_booth",
                            mandatory: "workspace_category_id,amenities_id,latitude,longitude",
                            optional: "start_date,end_date,days,date,name",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get rent booth",
                            example: '{"workspace_category_id":"2","amenities_id":"1,2,3","start_date":"2023-01-01","end_date":"2023-01-31","days":"Sunday,Tuesday,Wednesday,Thursday,Friday,Saturday"}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'BOOTH RENTING',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/booth_renting",
                            mandatory: "booth_location_id,workspace_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get booth renting",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'BOOTH RENT DAYE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/available_booth_rent_date",
                            mandatory: "booth_location_id,workspace_id,month,year",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get rented booth dates",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'BOOTH RENT TIME SLOT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/booth_rent_timeslot",
                            mandatory: "booth_location_id,workspace_id,date",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get booth time slots",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'BOOTH PAYMENT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/booth_rent_payment",
                            mandatory: "booth_location_id,workspace_id,booth_owner_id,date,start_slot_time,end_date,end_slot_time,price,sub_total,tax,total_amount,cleaning_fee,payment_mode(Cash,Card),payment_mode(Cash,Card),payment_intent_id(required when payment mode is card)",
                            optional: "transaction_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used payment booth rent",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'BOOTH HISTORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/booth_history",
                            mandatory: "page",
                            optional: "date,location_id,sort_by[Oldest,Newest]",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get booth history",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'REFUND AMOUNT',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/refund_amount",
                            mandatory: "amount,appointment_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used refund",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'BOOTH DETAIL (INSIDE HISTORY)',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/booth_detail",
                            mandatory: "booth_rent_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get booth details",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'RATE BOOTH',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/rate_booth",
                            mandatory: "booth_rent_id,booth_owner_id,ratting",
                            optional: "review,review_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used add ratting for booth",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'REPORTS',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/report",
                            mandatory: "type(new,old)",
                            optional: "period(Daily,Weekly,Monthly,Custom(start_date,end_date)),compare_date,compare_start_date,compare_end_date",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used add ratting for booth",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'PRODUCT LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/get_product_list",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used add ratting for get product list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Get Followers List',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/getfollowerslist",
                            mandatory: "page",
                            optional: "word",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used add ratting for Get Followers List",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Remove Follower',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/removefollower",
                            mandatory: "user_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Remove Follower",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Delete Account',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/deleteaccount",
                            mandatory: "user_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Delete Account",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Save Client Rate Review',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/saveratereview",
                            mandatory: "rateto_id,rateto_type[User,Client],ratting,order_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Save Client Rate Review",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Save Notification Setting',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/savenotificationsetting",
                            mandatory: "chat_notify[On,Off],incomingcall_notify[On,Off],newappointment_notify[On,Off],newreview_notify[On,Off],beforeshitreminder_notify[On,Off],beforeappointmentreminder_notify[On,Off]",
                            optional: "beforeshitreminder_time,beforeappointmentreminder_time",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Save Notification Setting",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Save Cancellation Setting',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/savecancellationsetting",
                            mandatory: "customize_cancellation_charges[Yes,No]",
                            optional: "cancellationwithin_hours,cancellation_charges,autocancellation_charges",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Save Cancellation Setting",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Save Tip Amount',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/savetipamount",
                            mandatory: "tip_amount1,tip_amount2,tip_amount3",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Save Tip Amount",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'User Details',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/user_details",
                            mandatory: "user_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used User Details",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Create Client Specific Promo',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/create_clientspecific_promo",
                            mandatory: "promo_image,discount_type[Percentage,Flat],discount_amount,max_discount_amount,promocode,valid_till,client_list",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Create Client Specific Promo",
                            example: '"client_list":[{"client_id":1,"user_id":"1"},{"client_id":3,"user_id":"2"}]',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Get Client Specific Promo',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/get_clientspecific_promo",
                            mandatory: "page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Get Client Specific Promo",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Import Contacts',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/importcontacts",
                            mandatory: "contacts",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Import Contacts",
                            example: '{"contacts":[{name:"test demo",phone:"+914545454545",email:"<EMAIL>"},{name:"demo",phone:"+9454545454545",email:""}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Upload Customer Bio',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/uploadcustomerbio",
                            mandatory: "appointment_id",
                            optional: "description_notes,image",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Import Contacts",
                            example: '{"image":[{"image_name":"default-user.png"},{"image_name":"default-user.png"}]}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Check Block Availability',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/checkblockavailability",
                            mandatory: "date,start_time,end_time,timezone,timezone_diff",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is Check Block Availability",
                            example: '{"timezone_diff":"+05:30","timezone":"Asia/calcuta","end_time":"15:00","start_time":"06:00","date":"2023-06-15"}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        },  {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Cancel Booth Rent Reason List',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/cancelrentboothreason",
                            mandatory: "type[service_provider,booth_host]",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is Cancel Booth Rent Reason List",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        },   {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Cancel Booth Rent',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/cancelboothrent",
                            mandatory: "booth_rent_id",
                            optional: "cancel_id,cancel_reason",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is Cancel Booth Rent",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'My Booth REVIEW LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/myboothreviews",
                            mandatory: "page",
                            optional: "word(for name),ratting,sorting(pass ASC for oldest and pass DESC for newest),date",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get My Booth REVIEW LIST",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        },  {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Get Review Option List',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/getreviewoptionlist",
                            mandatory: "user_type[service_provider,booth_user,booth_host]",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get Get Review Option List",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        },  {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Get Booth Host Reviews List',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/boothhostreviewslist",
                            mandatory: "booth_owner_id,page",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get Get Booth Host Reviews List",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Get Service Provider Slider Image List',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/getspsliderimage",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get Get Service Provider Slider Image List",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Save Service Provider Slider Image',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/savesliderimage",
                            mandatory: "image_name",
                            optional: "image_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get Save Service Provider Slider Image",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'green',
                            icon: 'person',
                            title: i++ + ' : SERVICE PROVIDER',
                            name: 'Remove Service Provider Slider Image',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/service_provider/removesliderimage",
                            mandatory: "image_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get Remove Service Provider Slider Image",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '1',
                            color: 'yellow',
                            title: 'BOOTH OWNER API',
                            icon: 'person',
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'ADD LOCATION',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/add_location",
                            mandatory: "name,address,latitude,longitude,description,start_availability,end_availability,workspace,location_schedule,location_image,location_amenities",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to add booth location",
                            example: '{ "name":"nirav", "address":"ABCD","latitude":"123.12","longitude":"123.12", "description":"Test", "start_availability":"2022-11-08", "end_availability":"2022-11-30", "workspace":[ { "workspace_category_id":2,"quantity":"2", "tag_name":"This is chair", "description":"Testing", "start_availability":"2022-11-08", "end_availability":"2022-11-30","hourly_rate":"10","weekly_rate":"10","monthly_rate":"10", "workspace_shedule":[ { "day":"Monday", "status":"open", "slot":[ { "open":"10:00", "close":"12:00" }, { "open":"14:00", "close":"16:00" } ] }, { "day":"Tuesday", "status":"open", "slot":[ { "open":"11:00", "close":"12:00" }, { "open":"15:00", "close":"16:00" } ] }, { "day":"Wednesday", "status":"close", "slot":[] } ], "workspace_image":[ { "image_name":"default-user.png" }, { "image_name":"default-user.png" } ], "workspace_amenities":[ { "amenities_id":1 }, { "amenities_id":2 } ] } ], "location_schedule": [ { "day": "Monday", "status": "open", "slot": [ { "open": "10:00", "close": "12:00" }, { "open": "14:00", "close": "16:00" } ] }, { "day": "Tuesday", "status": "open", "slot": [ { "open": "11:00", "close": "12:00" }, { "open": "15:00", "close": "16:00" } ] }, { "day": "Wednesday", "status": "close", "slot": [] } ], "location_image": [ { "image_name": "default-user.png" }, { "image_name": "default-user.png" } ], "location_amenities": [ { "amenities_id": 1 }, { "amenities_id": 2 } ] }',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'LOCATION LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/all_booth_location",
                            mandatory: "",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get booth location",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'CHANGE LOCATION STATUS',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/change_location",
                            mandatory: "location_id,status(Active,Inactive)",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to change booth location status",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'LOCATION DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/location_detail",
                            mandatory: "location_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get booth location detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'CHANGE WORKSPACE STATUS',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/change_workspace_status",
                            mandatory: "workspace_id,status",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to change workspace status",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'WORKSPACE DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/workspace_detail",
                            mandatory: "workspace_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get workspace detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'EDIT WORKSPACE',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/edit_workspace",
                            mandatory: "workspace_id,booth_location_id,workspace_category_id,tag_name,description,start_availability,end_availability,hourly_rate,weekly_rate,monthly_rate,workspace_shedule,workspace_image,workspace_amenities",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to det workspace detail",
                            example: '{"workspace_id":"1","booth_location_id":"1","workspace_category_id":"3","tag_name":"Chiar","description":"description","start_availability":"2022-11-15","end_availability":"2022-11-20","hourly_rate":"10","weekly_rate":"20","monthly_rate":"100","workspace_shedule":[ { "day":"Monday", "status":"open", "slot":[ { "open":"10:00", "close":"12:00" }, { "open":"14:00", "close":"16:00" } ] }, { "day":"Tuesday", "status":"open", "slot":[ { "open":"11:00", "close":"12:00" }, { "open":"15:00", "close":"16:00" } ] }, { "day":"Wednesday", "status":"close", "slot":[] } ], "workspace_image":[ { "image_name":"default-user.png" }, { "image_name":"default-user.png" } ], "workspace_amenities":[ { "amenities_id":1 }, { "amenities_id":2 } ] }',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'BOOKING HISTORY',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/booking_history",
                            mandatory: "page",
                            optional: "date,booth_location_id,sort_by[Oldest,Newest]",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to get booking history",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'REVIEW LIST',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/review_list",
                            mandatory: "page",
                            optional: "word(for name),ratting,sorting(pass ASC for oldest and pass DESC for newest),date",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get booth owner review list",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'REVIEW DETAIL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/review_detail",
                            mandatory: "review_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get review detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'BOOTH SCHEDULE CALENDER',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/booth_schedule_calender",
                            mandatory: "location_id,month,year",
                            optional: "workspace_category_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used get review detail",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'Delete Location',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/delete_location",
                            mandatory: "location_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Delete Location",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'Delete Workspace',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/delete_workspace",
                            mandatory: "workspace_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used Delete Workspace",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'Update LOCATION',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/update_location",
                            mandatory: "location_id,name,address,latitude,longitude,description,start_availability,end_availability,workspace,location_schedule,location_image,location_amenities",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Update booth location",
                            example: '{ "name":"nirav", "address":"ABCD","latitude":"123.12","longitude":"123.12", "description":"Test", "start_availability":"2022-11-08", "end_availability":"2022-11-30", "workspace":[ { "workspace_category_id":2,"quantity":"2", "tag_name":"This is chair", "description":"Testing", "start_availability":"2022-11-08", "end_availability":"2022-11-30","hourly_rate":"10","weekly_rate":"10","monthly_rate":"10", "workspace_shedule":[ { "day":"Monday", "status":"open", "slot":[ { "open":"10:00", "close":"12:00" }, { "open":"14:00", "close":"16:00" } ] }, { "day":"Tuesday", "status":"open", "slot":[ { "open":"11:00", "close":"12:00" }, { "open":"15:00", "close":"16:00" } ] }, { "day":"Wednesday", "status":"close", "slot":[] } ], "workspace_image":[ { "image_name":"default-user.png" }, { "image_name":"default-user.png" } ], "workspace_amenities":[ { "amenities_id":1 }, { "amenities_id":2 } ] } ], "location_schedule": [ { "day": "Monday", "status": "open", "slot": [ { "open": "10:00", "close": "12:00" }, { "open": "14:00", "close": "16:00" } ] }, { "day": "Tuesday", "status": "open", "slot": [ { "open": "11:00", "close": "12:00" }, { "open": "15:00", "close": "16:00" } ] }, { "day": "Wednesday", "status": "close", "slot": [] } ], "location_image": [ { "image_name": "default-user.png" }, { "image_name": "default-user.png" } ], "location_amenities": [ { "amenities_id": 1 }, { "amenities_id": 2 } ] }',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'Accept Booth Renter',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/acceptboothrenter",
                            mandatory: "booth_rent_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Accept Booth Renter",
                            example: '{"booth_rent_id":"1"}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'Reject Booth Renter',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/rejectboothrenter",
                            mandatory: "booth_rent_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Reject Booth Renter",
                            example: '{"booth_rent_id":"1"}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'Cancel Booth Renter',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/cancelboothrenter",
                            mandatory: "booth_rent_id",
                            optional: "cancel_id,cancel_reason",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Cancel Booth Renter",
                            example: '{"booth_rent_id":"1"}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'Add Booth Rent Review',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/addreview",
                            mandatory: "booth_rent_id,service_provider_id,ratting",
                            optional: "review,review_id",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Add Booth Rent Review",
                            example: '{"booth_rent_id":"1"}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'Get Booked Dates',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/getbookeddates",
                            mandatory: "booth_location_id,month,year",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Get Booked Dates",
                            example: '{"booth_rent_id":"1"}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        },  {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'Get Booth Booked List',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/getboothbookedlist",
                            mandatory: "booth_location_id,date",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Get Booth Booked List",
                            example: '{"booth_rent_id":"1"}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        },  {
                            new_tag: '0',
                            color: 'yellow',
                            icon: 'person',
                            title: i++ + ' : BOOTH OWNER',
                            name: 'Get Booth Owner Report',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/booth_owner/report",
                            mandatory: "type(new,old)",
                            optional: "period(Daily,Weekly,Monthly,Custom(start_date,end_date)),compare_date,compare_start_date,compare_end_date",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY,token",
                            notes: "This api is used to Get Booth Owner Report",
                            example: '{"booth_rent_id":"1"}',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '1',
                            color: 'teal',
                            title: 'CHAT API',
                            icon: 'chat_bubble',
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'chat_bubble',
                            title: i++ + ' : CHAT',
                            name: 'CREATE CHAT ROOM',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/chat/createchatroom",
                            mandatory: "receiver_id,receiver_type(customer,service_provider),sender_type(customer,service_provider)",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is create chat room",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'chat_bubble',
                            title: i++ + ' : CHAT',
                            name: 'GENERATE ACCESS TOKEN',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/chat/getaccesstoken",
                            mandatory: "identity,sender_type(customer,service_provider),device_type,device_token",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to generate acess token",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'chat_bubble',
                            title: i++ + ' : CHAT',
                            name: 'GENERATE AUDIO TOKEN',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/chat/generate_audio_token",
                            mandatory: "identity,sender_type(customer,service_provider)",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to generate audio call token",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'chat_bubble',
                            title: i++ + ' : CHAT',
                            name: 'GENERATE VIDEO TOKEN AND ROOM',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/chat/generate_video_token",
                            mandatory: "identity,sender_type(customer,service_provider),receiver_id,receiver_type(customer,service_provider)",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to generate video call token and video call room",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'chat_bubble',
                            title: i++ + ' : CHAT',
                            name: 'GENERATE VIDEO ROOM DETAILS',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/chat/get_room_details",
                            mandatory: "room_id",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to get video call room details",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'chat_bubble',
                            title: i++ + ' : CHAT',
                            name: 'END VIDEO CALL',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/chat/end_videocall",
                            mandatory: "room_id,receiver_id,receiver_type",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to end video call",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }, {
                            new_tag: '0',
                            color: 'teal',
                            icon: 'chat_bubble',
                            title: i++ + ' : CHAT',
                            name: 'Remove Channel Data',
                            meth: 'POST',
                            link: "<%= GLOBALS.PORT_BASE_URL %>v1/chat/removechanneldata",
                            mandatory: "channel_sid",
                            optional: "",
                            is_header: "YES",
                            is_push: "NO",
                            header: "API-KEY",
                            notes: "This api is used to Remove Channel Data",
                            example: '',
                            status: "<br>1. HTTP_OK[200] - code : 0 <br>2. HTTP_OK[200] - code : 1 <br>3. HTTP_OK[200] - code : 2",
                            imp: "",
                        }
                    ]
                })
            })
        </script>
    </body>
</html>
