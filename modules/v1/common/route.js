var express = require('express');
var common = require('../../../config/common');
var globals = require('../../../config/constant');
var common_model = require('../common/common_model');
var router = require('express-promise-router')();
const { t } = require('localizify');
const con = require('../../../config/database');
const { logger } = require('../../../src/utils');
const {
  decrypt,
  validate,
  encrypt,
  getSettings,
  USER_TYPE,
  updateUser,
  updateServiceProvider,
} = require('../../../src/service')

/**
 * Common counry list api
 */
router.post("/country_list", function (req, res) {
  require('../../../config/common').decryption(req.body, function (request) {
    require("../user/user_model").country_list(request, function (response, message, code) {
      if (!response) {
        common.send_response(req, res, code, message, response);
      } else {
        common.send_response(req, res, code, message, response);
      }
    });
  });
});

/**
 * Common amenities
 */
router.post("/get_amenities", function (req, res) {
  var condition = "is_deleted = '0' AND status = 'Active'";
  var field = " *,id as amenities_id,CONCAT('" + globals.S3_BUCKET_ROOT + globals.BOOTH_OWNER + "','',image_name) as image_name";
  common.selct_data_with_field('tbl_master_amenities', condition, field, function (amenities) {
    if (amenities) {
      common.send_response(req, res, '1', 'Amenities found successfully', amenities);
    } else {
      common.send_response(req, res, '0', t('restapi_globals_error'), null);
    }
  })
});

/**
 * Common workspace category
 */
router.post("/get_workspace_category", function (req, res) {
  var condition = "is_deleted = '0' AND status = 'Active'";
  common.select_data('tbl_master_workspace_category', condition, function (category) {
    if (category) {
      common.send_response(req, res, '1', 'Cateory found successfully', category);
    } else {
      common.send_response(req, res, '0', t('restapi_globals_error'), null);
    }
  })
});

/**
 * Common tax percentage
 */
router.post("/tax_percentage", function (req, res) {
  var condition = ' title = "tax" AND is_active = "1" AND is_deleted = "0"';
  common.select_data('tbl_settings', condition, function (response) {
    if (response) {
      common.send_response(req, res, '1', 'Tax percentage found successfully', response[0]);
    } else {
      common.send_response(req, res, '0', 'Tax percentage not found', null);
    }
  })
});

/**
 * Api for Common transaction charge
 */
router.post("/transaction_charge", function (req, res) {
  var condition = ' title = "transaction_charge" AND is_active = "1" AND is_deleted = "0"';
  common.select_data('tbl_settings', condition, function (response) {
    if (response) {
      common.send_response(req, res, '1', 'Transaction Charge found successfully', response[0]);
    } else {
      common.send_response(req, res, '0', 'Transaction Charge not found', null);
    }
  })
});

/**
 * Api for Common booth rent tax
 */
router.post("/booth_rent_tax", function (req, res) {
  var condition = ' title = "booth_rent_tax" AND is_active = "1" AND is_deleted = "0"';
  common.select_data('tbl_settings', condition, function (response) {
    if (response) {
      common.send_response(req, res, '1', 'Booth Rent Tax found successfully', response[0]);
    } else {
      common.send_response(req, res, '0', 'Booth Rent Tax not found', null);
    }
  })
});

/**
 * Api for Common booth rent Cleaning Fees
 */
router.post("/booth_rent_cleaning_fees", function (req, res) {
  var condition = ' title = "booth_rent_cleaning_fees" AND is_active = "1" AND is_deleted = "0"';
  common.select_data('tbl_settings', condition, function (response) {
    if (response) {
      common.send_response(req, res, '1', 'Booth Rent Cleaning Fees found successfully', response[0]);
    } else {
      common.send_response(req, res, '0', 'Booth Rent Cleaning Fees not found', null);
    }
  })
});


/**Api for comment list*/
router.post("/get_post_comments", function (req, res) {
  //console.log('get_post_comments');
  common.decryption(req.body, function (request) {
    var rules = {
      post_id: 'required',
      page: 'required'
    };
    var message = {
      'required': t('required'),
    };
    var keywords = {
      'post_id': 'Post Id'
    }
    //checks all validation
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.service_provider_id = req.user_id;
      request.service_provider_uuid = req.user_uuid;
      request.type = req.user_type
      require("../service_provider/service_provider_model").get_post_comments(request, function (response, message, code) {
        common.send_response(req, res, code, message, response);
      })
    }
  });
});


/**Api for user comment */
router.post("/post_comment", function (req, res) {
  common.decryption(req.body, function (request) {
    var rules = {
      post_id: 'required',
      type: 'required',
      comment: 'required',
    };
    const messages = {
      'required': t('required'),
    };
    var keywords = {
      'page': t('keyword_page'),
    };
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.user_id = req.user_id;
      var add_comment = {
        post_id: request.post_id,
        comment: request.comment,
        user_id: req.user_id,
        type: req.user_type,
        is_deleted: '0',
        reply_id: (request.reply_id != undefined && request.reply_id != "") ? request.reply_id : 0
      }
      common.single_insert_data(
        'tbl_comment',
        add_comment,
        is_comment_added => {
          if (is_comment_added) {
            con.query(`
              SELECT
                c.id,
                c.post_id,
                c.comment,
                c.user_id,
                IF(
                  c.type = 'service_provider',
                  s.first_name,
                  u.first_name
                ) AS first_name,
                IF(
                  c.type = 'service_provider',
                  s.last_name,
                  u.last_name
                ) AS last_name,
                DATE_FORMAT(c.insertdate, '%Y-%m-%d %H:%m:%s') AS insert_date,
                IF(
                  c.type = 'service_provider',
                  CONCAT(s.first_name, ' ', s.last_name),
                  CONCAT(u.first_name, ' ', u.last_name)
                ) AS user_name,
                IF(
                  c.type = 'service_provider',
                  IF(
                    s.profile_image IS NOT NULL AND s.profile_image != '',
                    CONCAT('${globals.S3_BUCKET_ROOT}${globals.SP_IMAGE}', s.profile_image),
                    ''
                  ),
                  IF(
                    u.profile_image IS NOT NULL AND u.profile_image != '',
                    CONCAT('${globals.S3_BUCKET_ROOT}${globals.USER_IMAGE}', u.profile_image),
                    ''
                  )
                ) AS user_image,
                (
                  SELECT COUNT(c1.id)
                  FROM tbl_comment c1
                  WHERE c1.is_deleted = '0'
                  AND c1.post_id = ${request.post_id}
                ) AS total_comment
              FROM tbl_comment c
              LEFT JOIN tbl_user u ON u.id = c.user_id
                AND c.type = 'user'
              LEFT JOIN tbl_service_provider s ON s.id = c.user_id
                AND c.type = 'service_provider'
              WHERE c.post_id = ${request.post_id}
              AND c.is_deleted = '0'
              AND c.user_id = ${request.user_id}
              ORDER BY c.id DESC
              LIMIT 1
            `,
             (err, result) => {
              if (err) {
                logger.error({ error: err, message: 'Failed to query for comment', meta: request })
                common.send_response(req, res, '0', t('restapi_globals_error'), false);
              } else if (result.length < 0 || result[0].total_comment < 0) {
                common.send_response(req, res, '0', "Post comment not found", {});
              } else {
                common.send_response(req, res, '1', 'Comment added successfully', result[0]);
              }
            });
          } else {
            logger.error({ message: 'Failed to add a comment', meta: request })
            common.send_response(req, res, '0', t('restapi_globals_error'), false);
          }
        },
      )
    }
  });
});


/**
 * Api for user like
 */
router.post("/post_like", function (req, res) {
  //console.log('post_like');
  common.decryption(req.body, function (request) {
    var rules = {
      post_id: 'required',
      type: 'required',
      status: 'required',
    };
    var message = {
      'required': t('required'),
    };
    var keywords = {
      'post_id': 'Post Id'
    }
    //checks all validation
    if (common.check_validation(request, res, rules, message, keywords)) {
      request.user_id = req.user_id;
      require("../user/user_model").post_like(request, function (response, message, code) {
        common.send_response(req, res, code, message, response);
      })
    }
  });
});

/**Api for post report reason */
router.post("/post_report_list", function (req, res) {
  var condition = ' status = "Active" AND is_deleted = "0"';
  common.select_data('tbl_post_report_reason', condition, function (response) {
    if (response) {
      common.send_response(req, res, '1', 'Post report reason found successfully', response);
    } else {
      common.send_response(req, res, '0', 'Post report reason not found', null);
    }
  })
});

/**
 * API for get graph tiles
 */
router.post("/tiles_list", function (req, res) {
  common.decryption(req.body, function (request) {
    request.service_provider_id = req.user_id;
    common_model.tiles_list(request, function (msgcode, message, response) {
      common.send_response(req, res, msgcode, message, response);
    });
  });
});

/**
 * API for add save tiles to service provider
 */
router.post("/save_tiles", function (req, res) {
  //console.log('save_tiles');
  common.decryption(req.body, function (request) {
    var rules = {
      tiles: 'required'
    };
    var messages = {
      'required': t('required'),
    };
    var keywords = {};
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id;
      common_model.save_tiles(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response);
      });
    }
  });
});

/**
 * API for get graph name list
 */
router.post("/graph_list", function (req, res) {
  common.decryption(req.body, function (request) {
    request.service_provider_id = req.user_id;
    common_model.graph_list(request, function (msgcode, message, response) {
      common.send_response(req, res, msgcode, message, response);
    });
  });
});

/**
 * API for save graph to service provider
 */
router.post("/save_graph", function (req, res) {
  //console.log('save_graph');
  common.decryption(req.body, function (request) {
    var rules = {
      graph: 'required'
    };
    var messages = {
      'required': t('required'),
    };
    var keywords = {};
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id;
      common_model.save_graph(request, function (msgcode, message, response) {
        common.send_response(req, res, msgcode, message, response);
      });
    }
  });
});

/**
 * API for get tiles for booth owner
 */
router.post("/booth_owner_tiles_list", function (req, res) {
  common_model.booth_owner_tiles_list(req.user_id, function (msgcode, message, response) {
    common.send_response(req, res, msgcode, message, response);
  });
});


/**
 * API for get tiles for booth owner
 */
router.post("/booth_owner_graph_list", function (req, res) {
  common_model.booth_owner_graph_list(req.user_id, function (msgcode, message, response) {
    common.send_response(req, res, msgcode, message, response);
  });
});

/*
* API for read notification
*/
router.post("/readnotification", function (req, res) {
  //console.log('save_graph');
  common.decryption(req.body, function (request) {
    var rules = {
      notification_id: 'required'
    };
    var messages = {
      'required': t('required'),
    };
    var keywords = {};
    if (common.check_validation(request, res, rules, messages, keywords)) {
      request.service_provider_id = req.user_id;
      common.update_data_condition("tbl_notification", "id = '" + request.notification_id + "'", { status: 'Read' }, function (is_update) {
        if (is_update) {
          common.send_response(req, res, "1", "Notification read successfully", null);
        } else {
          common.send_response(req, res, "0", t('restapi_globals_error'), null);
        }
      });
    }
  });
});

router.post(
  '/checkappupdate',
  async (req, res) => {
    const rawData = decrypt(req.body)
    const rules = {
      user_type: 'required|in:Service_provider,User',
      device_type: 'required|in:A,I',
      app_version: 'required',
    }
    const messages = { 'required': t('required') }
    const errors = validate(rawData, rules, messages, {})
    if (errors) {
      sendError(res, errors)
      return
    }

    const settings = await getSettings()
    const isSP = rawData.user_type?.toLowerCase() === USER_TYPE.SERVICE_PROVIDER
    if (rawData.user_id) {
      const updateData = { app_version: rawData.app_version }
      isSP ? await updateServiceProvider(rawData.user_id, updateData) : await updateUser(rawData.user_id, updateData)
    }

    const isAndroid = rawData.device_type === 'A'

    const settingsAndroidVersion = isSP ? settings.serviceprovider_androidapp_version : settings.user_androidapp_version
    const settingsIosVersion = isSP ? settings.serviceprovider_iosapp_version : settings.user_iosapp_version
    const settingsAndroidType = isSP ? settings.serviceprovider_androidapp_update_type : settings.user_androidapp_update_type
    const settingsIosType = isSP ? settings.serviceprovider_iosapp_update_type : settings.user_iosapp_update_type

    const settingsVersion = isAndroid ? settingsAndroidVersion : settingsIosVersion
    const settingsType = isAndroid ? settingsAndroidType : settingsIosType

    const isNewer = settingsVersion > rawData.app_version

    sendSuccess(
      res,
      {
        message: 'Your App version is old, please update to latest version.',
        data: {
          update_status: isNewer ? settingsVersion : 0,
          update_type: settingsType,
        },
      },
    )
  })

router.post(
  '/checkappupdate_old',
  (req, res, next) => {
    try {
      common.decryption(
        req.body,
        rawData => {
          const rules = {
            user_type: 'required|in:Service_provider,User',
            device_type: 'required|in:A,I',
            app_version: 'required'
          };
          const messages = { 'required': t('required') };
          const keywords = {};

          if (common.check_validation(rawData, res, rules, messages, keywords)) {
            common.get_app_setting(
              (settings) => {
                var responsedata = { "update_status": "0", "update_type": "force_update" };
                if (rawData.user_type == 'Service_provider') {
                  common.update_data(
                    "tbl_service_provider",
                    req.user_id,
                    { app_version: rawData.app_version },
                    is_update => {
                      if (rawData.device_type === 'A') {
                        if (settings.serviceprovider_androidapp_version > rawData.app_version) {
                          responsedata['update_status'] = settings.serviceprovider_androidapp_version;
                        }
                        responsedata['update_type'] = settings.serviceprovider_androidapp_update_type;
                        common.send_response(req, res, "1", t('restapi_checkappversion_success'), responsedata);
                      } else {
                        if (settings.serviceprovider_iosapp_version > rawData.app_version) {
                          responsedata['update_status'] = settings.serviceprovider_iosapp_version;
                        }
                        responsedata['update_type'] = settings.serviceprovider_iosapp_update_type;
                        common.send_response(req, res, "1", t('restapi_checkappversion_success'), responsedata);
                      }
                    });
                } else {
                  common.update_data(
                    "tbl_user",
                    req.user_id,
                    { app_version: rawData.app_version },
                    is_update => {
                      if (rawData.device_type === 'A') {
                        if (settings.user_androidapp_version > rawData.app_version) {
                          responsedata['update_status'] = settings.user_androidapp_version;
                        }
                        responsedata['update_type'] = settings.user_androidapp_update_type;
                        common.send_response(req, res, "1", t('restapi_checkappversion_success'), responsedata);
                      } else {
                        if (settings.user_iosapp_version > rawData.app_version) {
                          responsedata['update_status'] = settings.user_iosapp_version;
                        }
                        responsedata['update_type'] = settings.user_iosapp_update_type;
                        common.send_response(req, res, "1", t('restapi_checkappversion_success'), responsedata);
                      }
                    });
                }
              });
          }
        });
    } catch (e) {
      next(e)
    }
  });

export function sendSuccess(res, data = {}, code = 1) {
  if (res.encryption?.toLowerCase() === "none") {
    return res.status(200).json({ code, ...data })
  }
  res.status(200).json(encrypt({ code, ...data }))
}

export function sendError(res, errors = {}, code = 0) {
  if (res.encryption?.toLowerCase() === "none") {
    return res.status(400).json(errors)
  }
  res.status(400).json(encrypt({ code, ...errors }))
}

module.exports = router
