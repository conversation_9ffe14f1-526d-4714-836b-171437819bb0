const con = require("../../../config/database")
const asyncloop = require('node-async-loop')
const datetime = require('node-datetime')
const { t } = require('localizify')
const moment = require('moment')
const common = require("../../../config/common")
const stripe = require("../../../config/payment")
const { USER_TYPE } = require('@/src/service')

const data = {
    cancel_appointment: function() {
        con.query("SELECT ab.*,TIMESTAMPDIFF(MINUTE,ab.end_datetime,DATE_ADD(NOW(),INTERVAL 0 MINUTE)) AS minute_diff,tsp.autocancellation_charges,IFNULL((SELECT SUM(price) as total_service_amount FROM tbl_appointment_booking_detail WHERE appointment_id = ab.id AND type = 'Service' AND is_deleted = '0' GROUP BY appointment_id),0) as total_service_amount FROM tbl_appointment_booking ab LEFT JOIN tbl_service_provider as tsp ON ab.service_provider_id = tsp.id WHERE ab.date <= CURDATE() AND ab.booking_status IN ('Accepted','Reschedule Request') AND ab.is_cancel = '0' AND ab.is_only_product = '0' GROUP BY ab.id HAVING minute_diff >= 30", function(err, result, fields) {
            if (!err && result[0] != undefined) {
                asyncloop(result, function(item, next) {
                    require("../service_provider/service_provider_model").cancelcreditproductquantity({appointment_id:item.id},function(){
                        if(item.payment_from == 'Wallet'){
                            //var refund_amount = parseFloat(item.wallet_amount) - ((parseFloat(item.wallet_amount) * parseFloat(item.autocancellation_charges)) / 100);
                            var cancellation_charges_amount = (parseFloat(item.total_service_amount) * parseFloat(item.autocancellation_charges)) / 100;
                            cancellation_charges_amount += parseFloat(item.merchant_fees_amount) + parseFloat(item.app_fees_amount);
                            if(cancellation_charges_amount > parseFloat(item.wallet_amount)){
                                var refund_amount = 0;
                            } else {
                                var refund_amount = parseFloat(item.wallet_amount) - cancellation_charges_amount;
                            }
                            var wallet_insert = {
                                user_id: item.user_id,
                                card_id: 0,
                                order_id: item.id,
                                amount: refund_amount,
                                title: 'refund cancel booking',
                                transaction_id: '',
                                status: 'credit'
                            }
                            require('../user/user_model').insert_wallet_history(wallet_insert, function(is_wallet_insert) {
                                var update_param = {
                                    is_cancel: '1',
                                    booking_status: item.booking_status == 'Reschedule Request' ? 'Cancelled' : 'No Show',
                                    updatetime: datetime.create().format('Y-m-d H:M:S'),
                                    cancel_reason: "Auto cancelled",
                                    cancel_id: `sp1_${moment().format('YYYYMMDDHHmmss')}`,
                                    is_refund: '1',
                                    payment_status:'paid',
                                    payment_datetime:datetime.create().format('Y-m-d H:M:S'),
                                    refund_amount: refund_amount,
                                    refund_id: 'Blookd'+datetime.create().format('YmdHMS'),
                                    refund_status: 'succeeded',
                                    refund_object: {},
                                    refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                }
                                require('../../../config/common').update_data('tbl_appointment_booking', item.id, update_param, function() {
                                    var message = {
                                        sender_id: item.service_provider_id,
                                        sender_type: USER_TYPE.SERVICE_PROVIDER,
                                        receiver_id: item.user_id,
                                        type: 'user',
                                        receiver_type: USER_TYPE.USER,
                                        message: 'Your booking has been cancelled',
                                        title: 'Cancelled Appointment Booking',
                                        isaction_id:item.id,
                                        tag: 'booking',
                                        appointment_id: item.id
                                    }
                                    require('../../../config/common').prepare_customer_notification(item.user_id, message, function(notification) {
                                        next()
                                    });
                                });
                            });
                        } else {
                            if(item.payment_mode == 'pay_later' && item.payment_status == 'unpaid'){
                                //var refund_amount = parseFloat(item.total_amount) - ((parseFloat(item.total_amount) * parseFloat(item.autocancellation_charges)) / 100);
                                //var cancellation_charges = (parseFloat(item.total_amount) * parseFloat(item.autocancellation_charges)) / 100;
                                var cancellation_charges = (parseFloat(item.total_service_amount) * parseFloat(item.autocancellation_charges)) / 100;
                                cancellation_charges += parseFloat(item.merchant_fees_amount) + parseFloat(item.app_fees_amount);
                                if(cancellation_charges > parseFloat(item.total_amount)){
                                    var refund_amount = 0;
                                } else {
                                    var refund_amount = parseFloat(item.total_amount) - cancellation_charges;
                                }
                                stripe.captureStripeCharge(item.transaction_id, cancellation_charges,function(code, message, refund) {
                                    if (code == '0') {
                                        console.log("Auto Cancel capture Error",message);
                                        console.log("Auto Cancel capture Error",refund);
                                    }
                                    var update_param = {
                                        is_cancel: '1',
                                        booking_status: item.booking_status == 'Reschedule Request' ? 'Cancelled' : 'No Show',
                                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                                        cancel_reason: "Auto cancelled",
                                        cancel_id: `sp2_${moment().format('YYYYMMDDHHmmss')}`,
                                        is_refund: '1',
                                        payment_status:'paid',
                                        payment_datetime:datetime.create().format('Y-m-d H:M:S'),
                                        refund_amount: refund_amount,
                                        refund_id: (refund != null) ? refund.id : "",
                                        refund_status: "succeeded",
                                        refund_object: (refund != null) ? JSON.stringify(refund) : "",
                                        refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                    }
                                    require('../../../config/common').update_data('tbl_appointment_booking', item.id, update_param, function() {
                                        var message = {
                                            sender_id: item.service_provider_id,
                                            sender_type: USER_TYPE.SERVICE_PROVIDER,
                                            receiver_id: item.user_id,
                                            type: 'user',
                                            receiver_type: USER_TYPE.USER,
                                            message: 'Your booking has been cancelled',
                                            title: 'Cancelled Appointment Booking',
                                            isaction_id:item.id,
                                            tag: 'booking',
                                            appointment_id: item.id
                                        }
                                        require('../../../config/common').prepare_customer_notification(item.user_id, message, function(notification) {
                                            next()
                                        });
                                    });
                                });
                            } else {
                                //var refund_amount = parseFloat(item.total_amount) - ((parseFloat(item.total_amount) * parseFloat(item.autocancellation_charges)) / 100);
                                var cancellation_charges = (parseFloat(item.total_service_amount) * parseFloat(item.autocancellation_charges)) / 100;
                                cancellation_charges += parseFloat(item.merchant_fees_amount) + parseFloat(item.app_fees_amount);
                                if(cancellation_charges > parseFloat(item.total_amount)){
                                    var refund_amount = 0;
                                } else {
                                    var refund_amount = parseFloat(item.total_amount) - cancellation_charges;
                                }
                                if(refund_amount > 0){
                                    stripe.createChargeRefund(item.transaction_id, refund_amount, function(code, message, refund) {
                                        if(code == '0'){
                                            console.log("Auto Cancel refund Error",message);
                                            console.log("Auto Cancel refund Error",refund);
                                        }
                                        var update_param = {
                                            is_cancel: '1',
                                            booking_status: item.booking_status == 'Reschedule Request' ? 'Cancelled' : 'No Show',
                                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                                            cancel_reason: "Auto cancelled",
                                            cancel_id: `sp3_${moment().format('YYYYMMDDHHmmss')}`,
                                            is_refund: '1',
                                            payment_status:'paid',
                                            payment_datetime:datetime.create().format('Y-m-d H:M:S'),
                                            refund_amount: refund_amount,
                                            refund_id: (refund != null) ? refund.charge : "",
                                            refund_status: (refund != null) ? refund.status : "succeeded",
                                            refund_object: (refund != null) ? JSON.stringify(refund) : "",
                                            refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                        }
                                        require('../../../config/common').update_data('tbl_appointment_booking', item.id, update_param, function() {
                                            var message = {
                                                sender_id: item.service_provider_id,
                                                sender_type: USER_TYPE.SERVICE_PROVIDER,
                                                receiver_id: item.user_id,
                                                type: 'user',
                                                receiver_type: USER_TYPE.USER,
                                                message: 'Your booking has been cancelled',
                                                title: 'Cancelled Appointment Booking',
                                                isaction_id:item.id,
                                                tag: 'booking',
                                                appointment_id: item.id
                                            }
                                            require('../../../config/common').prepare_customer_notification(item.user_id, message, function(notification) {
                                                next()
                                            });
                                        });
                                    });
                                }else{
                                    var update_param = {
                                        is_cancel: '1',
                                        booking_status: item.booking_status == 'Reschedule Request' ? 'Cancelled' : 'No Show',
                                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                                        cancel_reason: "Auto cancelled",
                                        cancel_id: `sp4_${moment().format('YYYYMMDDHHmmss')}`,
                                        is_refund: '0',
                                        payment_status:'paid',
                                        payment_datetime:datetime.create().format('Y-m-d H:M:S'),
                                        refund_amount: 0,
                                        refund_id:"no_refund",
                                        refund_status: "no_refund",
                                        refund_object: "",
                                        refund_datetime: datetime.create().format('Y-m-d H:M:S')
                                    }
                                    require('../../../config/common').update_data('tbl_appointment_booking', item.id, update_param, function() {
                                        var message = {
                                            sender_id: item.service_provider_id,
                                            sender_type: USER_TYPE.SERVICE_PROVIDER,
                                            receiver_id: item.user_id,
                                            type: 'user',
                                            receiver_type: USER_TYPE.USER,
                                            message: 'Your booking has been cancelled',
                                            title: 'Cancelled Appointment Booking',
                                            isaction_id:item.id,
                                            tag: 'booking',
                                            appointment_id: item.id
                                        }
                                        require('../../../config/common').prepare_customer_notification(item.user_id, message, function(notification) {
                                            next()
                                        });
                                    });
                                }
                            }
                        }
                    });
                }, function() {
                    console.log("appointment auto cancelled success!");
                })
            } else {
                //console.log("error", err);
                console.log("NO appointment found for auto cancelled");
            }
        });
    },
    sendreminder_completeappointment:function(){
        con.query("SELECT tab.* FROM tbl_appointment_booking as tab WHERE tab.is_deleted = '0' AND tab.booking_status = 'In Progress' AND DATE_ADD(tab.end_datetime,INTERVAL 30 MINUTE) <= now() AND tab.is_completereminder_send = '0'",function(err, result, fields) {
            if (!err && result[0] != undefined) {
                asyncloop(result, function(item, next) {
                    var update_param = {
                        is_completereminder_send: '1',
                        reminder_date_time: datetime.create().format('Y-m-d H:M:S')
                    }
                    require('../../../config/common').update_data('tbl_appointment_booking', item.id, update_param, function() {
                        var spmessage = {
                            sender_id: item.user_id,
                            sender_type: USER_TYPE.USER,
                            receiver_id: item.service_provider_id,
                            type: 'service_provider',
                            receiver_type: USER_TYPE.SERVICE_PROVIDER,
                            message: 'Please complete your '+item.booking_id+' appointment',
                            title: 'Reminder Appointment Booking',
                            isaction_id:item.id,
                            tag: 'completereminder_notification',
                            appointment_id: item.id
                        }
                        require('../../../config/common').prepare_notification(item.service_provider_id, spmessage, function(notification) {
                            next();
                        });
                    });
                },function(){
                    console.log("reminder for complete appointment sent!");
                });
            } else {
                console.log("No appointment found for complete reminder");
            }
        });
    },
    tiles_list: function(request, callback) {
        var usertypecon = "mt.user_type = 'ServiceProvider'";
        if(request.is_boothowner != undefined && request.is_boothowner == '1'){
            usertypecon = "mt.user_type = 'BoothOwner'";
        }
        con.query("select *,IFNULL((SELECT 1 FROM tbl_service_provider_tiles t WHERE t.tiles_id = mt.id AND t.service_provider_id = '" +request.service_provider_id + "'),0) AS is_checked from tbl_master_tiles mt where mt.status = 'Active' AND mt.is_deleted = '0' AND "+usertypecon, function(err, result) {
            if (err) {
                console.log(err);
                callback('0', t('restapi_globals_error'), null);
            } else if (result.length < 0) {
                callback('0', "Tiles list not found", null);
            } else {
                callback('1', "Tiles found successfully", result);
            }
        })
    },
    graph_list: function(request, callback) {
        var usertypecon = "mg.user_type = 'ServiceProvider'";
        if(request.is_boothowner != undefined && request.is_boothowner == '1'){
            usertypecon = "mg.user_type = 'BoothOwner'";
        }
        var sql = con.query("select *,IFNULL((SELECT 1 FROM tbl_service_provider_graph g WHERE g.graph_id = mg.id AND g.service_provider_id = " + request.service_provider_id + "),0) AS is_checked from tbl_master_graph mg where mg.status = 'Active' AND mg.is_deleted = '0' AND "+usertypecon, function(err, result) {
            if (err) {
                console.log(err);
                callback('0', t('restapi_globals_error'), null);
            } else if (result.length < 0) {
                callback('0', "Graph list not found", null);
            } else {
                callback('1', "Graph found successfully", result);
            }
        })
    },
    save_tiles: function(request, callback) {
        var usertypecon = "user_type = 'ServiceProvider'";
        if(request.is_boothowner != undefined && request.is_boothowner == '1'){
            usertypecon = "user_type = 'BoothOwner'";
        }
        var condition = "service_provider_id = " + request.service_provider_id + " AND "+usertypecon;
        common.delete_data("tbl_service_provider_tiles", condition, function(is_tile_delete) {
            if (request.tiles) {
                asyncloop(request.tiles, function(item, next) {
                    if (item) {
                        var add_param = {
                            tiles_id: item.tiles_id,
                            service_provider_id: request.service_provider_id,
                            user_type:(request.is_boothowner != undefined && request.is_boothowner == '1') ? 'BoothOwner' : 'ServiceProvider',
                            insertdate: datetime.create().format('Y-m-d H:M:S')
                        }
                        common.single_insert_data('tbl_service_provider_tiles', add_param, function(is_inserted) {
                            next();
                        })
                    } else {
                        next();
                    }
                }, function() {
                    callback('1', "Tiles added successfully", true);
                })
            } else {
                callback('0', "Tiles can not be null", null);
            }
        })
    },
    save_graph: function(request, callback) {
        var usertypecon = "user_type = 'ServiceProvider'";
        if(request.is_boothowner != undefined && request.is_boothowner == '1'){
            usertypecon = "user_type = 'BoothOwner'";
        }
        var condition = "service_provider_id = " + request.service_provider_id + " AND "+usertypecon;
        common.delete_data("tbl_service_provider_graph", condition, function(is_tile_delete) {
            if (request.graph) {
                asyncloop(request.graph, function(item, next) {
                    if (item) {
                        var add_param = {
                            graph_id: item.graph_id,
                            service_provider_id: request.service_provider_id,
                            user_type:(request.is_boothowner != undefined && request.is_boothowner == '1') ? 'BoothOwner' : 'ServiceProvider',
                            insertdate: datetime.create().format('Y-m-d H:M:S')
                        }
                        common.single_insert_data('tbl_service_provider_graph', add_param, function(is_inserted) {
                            next();
                        })
                    } else {
                        next();
                    }
                }, function() {
                    callback('1', "Graph added successfully", true);
                })
            } else {
                callback('0', "Graph can not be null", null);
            }
        })
    },
    booth_owner_tiles_list: function(request, callback) {
        con.query("select * from tbl_master_booth_tiles where status = 'Active' AND is_deleted ='0'", function(err, result) {
            if (err) {
                callback('0', t('restapi_globals_error'), null);
            } else if (result.length < 0) {
                callback('0', "Tiles list not found", null);
            } else {
                callback('1', "Tiles found successfully", result);
            }
        })
    },
    autostartboothrentbooking:function(){
        con.query("SELECT * FROM tbl_rent_booth WHERE is_deleted = '0' AND status = 'Accepted' AND DATE_FORMAT(now(),'%Y-%m-%d %H:%i') >= DATE_FORMAT(CONCAT(date,' ',start_slot_time),'%Y-%m-%d %H:%i') GROUP BY id ORDER BY id ASC",function(error,result,fields){
            if(!error && result[0] != undefined){
                asyncloop(result,function(item,next){
                    var updparams = {
                        status : 'Processing',
                        processing_datetime: datetime.create().format('Y-m-d H:M:S'),
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                    }
                    common.update_data('tbl_rent_booth',item.id,updparams,function(is_updated){
                        if(is_updated){
                            var message = {
                                sender_id: item.service_provider_id,
                                sender_type: USER_TYPE.SERVICE_PROVIDER,
                                receiver_id: item.booth_owner_id,
                                type: 'booth_owner',
                                receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                message: 'Your booth rent booking has been started by booth rentee',
                                title: 'Start Booth Rent',
                                isaction_id: item.id,
                                tag: 'startboothrentbooking',
                            }
                            require('../../../config/common').prepare_notification(item.booth_owner_id, message, function (notification) {
                                var message = {
                                    sender_id: item.booth_owner_id,
                                    sender_type: USER_TYPE.SERVICE_PROVIDER,
                                    receiver_id: item.service_provider_id,
                                    type: 'service_provider',
                                    receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                    message: "Your booth rent booking has been started by booth renter",
                                    title: "Start Booth Rent",
                                    isaction_id:item.id,
                                    tag: 'startboothrentbooking',
                                }
                                require('../../../config/common').prepare_notification(item.service_provider_id, message, function(notification) {
                                    next();
                                });
                            });
                        } else {
                            next();
                        }
                    });
                },function(){
                    console.log("Booth rent found for start");
                });
            } else {
                console.log("No Booth rent found for start");
            }
        });
    },
    autocompleteboothrentbooking:function(){
        con.query("SELECT * FROM tbl_rent_booth WHERE is_deleted = '0' AND status = 'Processing' AND DATE_FORMAT(now(),'%Y-%m-%d %H:%i') >= DATE_FORMAT(CONCAT(end_date,' ',end_slot_time),'%Y-%m-%d %H:%i') GROUP BY id ORDER BY id ASC",function(error,result,fields){
            if(!error && result[0] != undefined){
                asyncloop(result,function(item,next){
                    var updparams = {
                        status : 'Completed',
                        completed_datetime: datetime.create().format('Y-m-d H:M:S'),
                        updatetime: datetime.create().format('Y-m-d H:M:S'),
                    }
                    common.update_data('tbl_rent_booth',item.id,updparams,function(is_updated){
                        if(is_updated){
                            var message = {
                                sender_id: item.service_provider_id,
                                sender_type: USER_TYPE.SERVICE_PROVIDER,
                                receiver_id: item.booth_owner_id,
                                type: 'booth_owner',
                                receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                message: 'Your booth rent booking has been completed by booth rentee',
                                title: 'Complete Booth Rent',
                                isaction_id: item.id,
                                tag: 'completeboothrentbooking',
                            }
                            require('../../../config/common').prepare_notification(item.booth_owner_id, message, function (notification) {
                                var message = {
                                    sender_id: item.booth_owner_id,
                                    sender_type: USER_TYPE.SERVICE_PROVIDER,
                                    receiver_id: item.service_provider_id,
                                    type: 'service_provider',
                                    receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                    message: "Your booth rent booking has been completed by booth renter",
                                    title: "Complete Booth Rent",
                                    isaction_id:item.id,
                                    tag: 'completeboothrentbooking',
                                }
                                require('../../../config/common').prepare_notification(item.service_provider_id, message, function(notification) {
                                    next();
                                });
                            });
                        } else {
                            next();
                        }
                    });
                },function(){
                    console.log("Booth rent found for complete");
                });
            } else {
                console.log("No Booth rent found for complete");
            }
        });
    },
    autocancelledboothrentrequest:function(){
        con.query("SELECT * FROM tbl_rent_booth WHERE is_deleted = '0' AND status = 'Waiting' AND DATE_FORMAT(now(),'%Y-%m-%d %H:%i') >= DATE_FORMAT(CONCAT(date,' ',start_slot_time),'%Y-%m-%d %H:%i') GROUP BY id ORDER BY id ASC",function(error,result,fields){
            if(!error && result[0] != undefined){
                asyncloop(result,function(item,next){
                    if(item.payment_mode == 'Card' && item.payment_intent_id != ""){
                        stripe.createChargeRefund(item.payment_intent_id,item.total_amount,function(rescode,resmessage,responsedata){
                            var updparams = {
                                status : 'Cancelled',
                                cancel_by : 'booth_host',
                                cancel_id : '0',
                                cancel_reason : 'Auto Cancelled By System',
                                cancel_datetime: datetime.create().format('Y-m-d H:M:S'),
                                updatetime: datetime.create().format('Y-m-d H:M:S'),
                            }
                            common.update_data('tbl_rent_booth',item.id,updparams,function(is_updated){
                                if(is_updated){
                                    var message = {
                                        sender_id: item.service_provider_id,
                                        sender_type: USER_TYPE.SERVICE_PROVIDER,
                                        receiver_id: item.booth_owner_id,
                                        type: 'booth_owner',
                                        receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                        message: 'Your booth rent booking has been cancelled by booth rentee',
                                        title: 'Cancelled Booth Rent',
                                        isaction_id: item.id,
                                        tag: 'cancelboothrentbooking',
                                    }
                                    require('../../../config/common').prepare_notification(item.booth_owner_id, message, function (notification) {
                                        var message = {
                                            sender_id: item.booth_owner_id,
                                            sender_type: USER_TYPE.SERVICE_PROVIDER,
                                            receiver_id: item.service_provider_id,
                                            type: 'service_provider',
                                            receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                            message: "Your booth rent booking has been cancelled by booth renter",
                                            title: "Cancelled Booth Rent",
                                            isaction_id:item.id,
                                            tag: 'cancelboothrentbooking',
                                        }
                                        require('../../../config/common').prepare_notification(item.service_provider_id, message, function(notification) {
                                            next();
                                        });
                                    });
                                } else {
                                    next();
                                }
                            });
                        });
                    } else {
                        var updparams = {
                            status : 'Cancelled',
                            cancel_by : 'booth_host',
                            cancel_id : '0',
                            cancel_reason : 'Auto Cancelled By System',
                            cancel_datetime: datetime.create().format('Y-m-d H:M:S'),
                            updatetime: datetime.create().format('Y-m-d H:M:S'),
                        }
                        common.update_data('tbl_rent_booth',item.id,updparams,function(is_updated){
                            if(is_updated){
                                var message = {
                                    sender_id: item.service_provider_id,
                                    sender_type: USER_TYPE.SERVICE_PROVIDER,
                                    receiver_id: item.booth_owner_id,
                                    type: 'booth_owner',
                                    receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                    message: 'Your booth rent booking has been cancelled by booth rentee',
                                    title: 'Cancelled Booth Rent',
                                    isaction_id: item.id,
                                    tag: 'cancelboothrentbooking',
                                }
                                require('../../../config/common').prepare_notification(item.booth_owner_id, message, function (notification) {
                                    var message = {
                                        sender_id: item.booth_owner_id,
                                        sender_type: USER_TYPE.SERVICE_PROVIDER,
                                        receiver_id: item.service_provider_id,
                                        type: 'service_provider',
                                        receiver_type: USER_TYPE.SERVICE_PROVIDER,
                                        message: "Your booth rent booking has been cancelled by booth renter",
                                        title: "Cancelled Booth Rent",
                                        isaction_id:item.id,
                                        tag: 'cancelboothrentbooking',
                                    }
                                    require('../../../config/common').prepare_notification(item.service_provider_id, message, function(notification) {
                                        next();
                                    });
                                });
                            } else {
                                next();
                            }
                        });
                    }
                },function(){
                    console.log("Booth rent found for cancelled");
                });
            } else {
                console.log("No Booth rent found for cancelled");
            }
        });
    },
}
module.exports = data;
