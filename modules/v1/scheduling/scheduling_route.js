var router = require('express-promise-router')();
var common = require('../../../../config/common');
var sp_model = require('../service_provider_model');


/**Api for cancel qppointment */
router.post("/cancel_appointment", function(req, res) {
  common.decryption(req.body, function(request) {
      var rules = {
          appointment_id: 'required',
          cancel_id: '',
          cancel_reason: '',
      };
      var messages = {
          'required': t('required'),
      };
      var keywords = {
          'appointment_id': 'Appointment id',
      };
      if (common.check_validation(request, res, rules, messages, keywords)) {
          request.service_provider_id = req.user_id;
          sp_model.cancel_appointment(request, function(msgcode, message, response) {
              common.send_response(req, res, msgcode, message, response);
          });
      }
  });
});
