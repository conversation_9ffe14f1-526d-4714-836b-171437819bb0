var user = require("./user/route");
var common = require("./common/route");
var service_provider = require("./service_provider/route");
var booth_owner = require("./booth_owner/route");
var chat = require("./chat/chat_route");
var router = require('express-promise-router')();

router.use("/user/", user);
router.use("/common/", common);
router.use("/service_provider/", service_provider);
router.use("/booth_owner/", booth_owner);
router.use("/chat/", chat);

module.exports = router;
