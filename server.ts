import 'module-alias/register'
import dotenv from 'dotenv'
import { expand } from 'dotenv-expand'
expand(dotenv.config())
import express, { Router, Request, Response, NextFunction } from 'express'
import { initExpressLogger, logger, initTracer } from './src/utils'
import { autoCancelAppointments, decrypt } from './src/service'
import {
  sendAptReminderNotification,
  sendShiftReminderNotification,
  sendAptReviewReminder,
} from './src/service/notifications'
import { Global } from './src/constants'
import swaggerUi from 'swagger-ui-express'
import swaggerDocument from './swagger-docs.json'

const app = express()
initTracer(app)
initExpressLogger(app)

import bodyParser from 'body-parser'
import path from 'path'
import localizify from 'localizify'
import common from './config/common'
import en from './language/en.json'
import * as commonModel from './modules/v1/common/common_model'
import { Server } from 'http'
import { Server as SocketIOServer } from 'socket.io'
import v1 from './modules/v1/index'
import v2 from './modules/v2'
import { encrypt, startTasks } from '@/src/service'
import { omit } from 'lodash'
import { validateToken } from '@/src/middleware'

// FIXME: currently, typescript can't figure out this `declare module` type when
// I add it to its own file under v2/@types/express.d.ts - so having it here for
// now. One day will need to fix that.
declare global {
  namespace Express {
    interface Request {
      user_id?: string
      user_uuid?: string
      user_type?: string
      login_language?: string
    }
  }
}

const NODE_ENV = Global.NODE_ENV

startTasks('*/5 * * * *', [
  {
    task: sendAptReminderNotification,
    taskDescription: 'send an appointment notification',
  },
  {
    task: commonModel.sendreminder_completeappointment,
    taskDescription: 'send a reminder of an appointment completion',
  },
  { task: autoCancelAppointments, taskDescription: 'auto cancel appointments' },
  {
    task: sendShiftReminderNotification,
    taskDescription: 'send a reminder of a shift start',
  },
  {
    task: commonModel.autostartboothrentbooking,
    taskDescription: 'auto start a booth rent booking',
  },
  {
    task: commonModel.autocompleteboothrentbooking,
    taskDescription: 'auto complete a booth rent booking',
  },
  {
    task: commonModel.autocancelledboothrentrequest,
    taskDescription: 'auto cancel a booth rent request',
  },
  {
    task: sendAptReviewReminder,
    taskDescription: 'send an appointment review notification',
  },
])

localizify.add('en', en)

app.use((req: Request, res: Response, next: NextFunction) => {
  const originalSend = res.send
  res.send = function (body: any) {
    let rawBody: any
    try {
      const decrypted = body ? decrypt(body, true) : {}
      rawBody = omit(decrypted, 'password')
    } catch (e) {
      rawBody = body
    }

    logger.info({
      type: 'Response',
      message: `${ res.statusCode } HTTP ${ req.method } ${ req.url }`,
      requestHeaders: req.headers,
      body: rawBody,
    })

    return originalSend.call(this, body)
  }

  next()
})

// Combine v1 and v2 routes into a single router, where v1 would override any
// v2 routes if there is a namespace collision. Originally, we hoped to slowly
// start moving away from /v1/ namespace into /v2/, but it turned out to be more
// complicated with the IOS app for the time being.
const router = Router()
router.use(v1)
router.use(v2)
app.use('/v2/webhooks/stripe', express.raw({ type: '*/*' }))
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument))
app.set('view engine', 'ejs')
app
  .use(bodyParser.text(), bodyParser.json(), express.json())
  .use(bodyParser.urlencoded({ extended: true }))
  .use((request: Request, response: Response, next: NextFunction) => {
    const lang =
      localizify.detectLocale(request.headers['accept-language']) || 'en'
    localizify.setLocale(lang)

    if (request.headers['encryption']) {
      response['encryption'] = request.headers['encryption']
    }

    next()
  })
  .use(express.static(path.join(__dirname, 'public')))
  .use(common.validate_token)
  .use(validateToken)
  .use('/v1', router)
  // iOS has started using /v2/ namespace only for the document endpoints - so
  // keeping it here for now until iOS will be moved back to /v1/ namespace for this.
  .use('/v2', v2)

app.get('/.well-known/apple-app-site-association', (req, res) => {
  const filePath = path.join(
    __dirname,
    'public/apple-app-site-association.json'
  )
  res.setHeader('Content-Type', 'application/json')
  res.sendFile(filePath)
})

// wildcard for /home/<USER>/* and /home/<USER>/* endpoints
app.get(/^\/home\/<USER>\/]+)(\/.*)?$/, (req, res) => {
  // Redirect to the App Store if not handled by the app
  res.send(`
      <!DOCTYPE html>
      <html>
      <head>
          <title>Open in App</title>
          <meta http-equiv="refresh" content="0 url=https://apps.apple.com/app/id6469029334">
      </head>
      <body>
          <p>If you are not redirected, <a href="https://apps.apple.com/app/id6469029334">click here to open in the App Store</a>.</p>
      </body>
      </html>
  `)
})

app.use((err, req, res, next) => {
  logger.error(`Server error ${req.path}.`, err)

  if (res.headersSent) {
    return next(err)
  }

  res
    .status(err.status || 400)
    .json(encrypt({ code: 0, message: err.message }))
})

try {
  logger.info(`Starting [${NODE_ENV}] api server...`)
  const server: Server = app.listen(8080)
  new SocketIOServer(server, { cors: { origin: '*' } })
  logger.info(`Api server started on port 8080 [${NODE_ENV}]`)
} catch {
  logger.error(`Failed to start api server on port 8080 [${NODE_ENV}]`)
}
