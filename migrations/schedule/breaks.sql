ALTER TABLE tbl_service_provider_available_slot
ADD COLUMN uuid VARCHAR(36);

UPDATE tbl_service_provider_available_slot
SET uuid = UUID()
WHERE uuid IS NULL;

ALTER TABLE tbl_service_provider_available_slot
CHANGE COLUMN uuid uuid VARCHAR(36) DEFAULT (UUID()) NOT NULL UNIQUE;

CREATE TABLE break (
  id VARCHAR(36) DEFAULT (UUID()) NOT NULL UNIQUE PRIMARY KEY,
  availableSlotId VARCHAR(36) NOT NULL,
  startTime VARCHAR(5) NOT NULL,
  endTime VARCHAR(5) NOT NULL,
  createdAt TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3),
  updatedAt TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  isDeleted TINYINT DEFAULT 0
);

CREATE INDEX idx_break_available_slot_deleted
ON break(availableSlotId, isDeleted);
