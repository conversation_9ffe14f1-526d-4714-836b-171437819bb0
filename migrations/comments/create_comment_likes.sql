CREATE TABLE IF NOT EXISTS comment_likes (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    userId CHAR(36) NOT NULL,
    commentId INT NOT NULL,
    createdDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for finding likes by user and comment (used in our findOne query)
CREATE INDEX idx_comment_likes_user_comment
ON comment_likes(userId, commentId);

-- Index for finding all likes for a specific comment
CREATE INDEX idx_comment_likes_comment
ON comment_likes(commentId);

-- Index for finding all likes by a specific user
CREATE INDEX idx_comment_likes_user
ON comment_likes(userId);

-- Add a unique constraint to prevent multiple likes from the same user on the same comment
ALTER TABLE comment_likes
ADD CONSTRAINT unique_user_comment_like UNIQUE (userId, commentId);
