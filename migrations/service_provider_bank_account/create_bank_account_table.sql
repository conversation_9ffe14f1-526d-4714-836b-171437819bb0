CREATE TABLE IF NOT EXISTS service_provider_bank_accounts
(
    service_provider_id    INT         NOT NULL,
    stripe_bank_account_id VARCHAR(50) NOT NULL,
    last4                  VARCHAR(4)  NOT NULL,
    routing_number         VARCHAR(9)  NOT NULL,
    bank_name              VA<PERSON>HAR(50) NOT NULL,
    account_holder_name    VARCHAR(50) DEFAULT NULL,
    account_holder_type    VARCHAR(50) DEFAULT NULL,
    country                VARCHAR(2)  DEFAULT NULL,
    currency               VARCHAR(3)  DEFAULT NULL,
    created_at             TIMESTAMP   DEFAULT CURRENT_TIMESTAMP,
    is_deleted             TINYINT     DEFAULT FALSE,
    <PERSON>IMARY KEY (service_provider_id, stripe_bank_account_id),
    FOREIGN KEY (service_provider_id) REFERENCES tbl_service_provider (id) ON DELETE CASCADE
);
