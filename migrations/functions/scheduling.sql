CREATE FUNCTION get_user_reviewed_services_list(
    userId INT,
    appointmentId INT
)
    RETURNS TEXT
    DETERMINISTIC
BEGIN
    DECLARE data TEXT;
    SELECT JSON_ARRAYAGG(JSON_OBJECT(
        'name', s.service_name,
        'id', s.id
    )) INTO data
    FROM tbl_user_review r
    JOIN tbl_appointment_booking_detail abd ON abd.appointment_id = r.order_id
    JOIN tbl_service s ON s.id = abd.service_id
    WHERE abd.type = 'Service'
    AND NOT abd.is_deleted
    AND r.user_id = userId
    AND r.order_id = appointmentId;
    RETURN COALESCE(data, '[]');
END;

drop FUNCTION get_user_reviewed_services_list
