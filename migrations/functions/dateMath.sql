CREATE FUNCTION IF NOT EXISTS get_week_of_the_year(date TEXT) RETURNS TEXT
RETURN CONCAT(STR_TO_DATE(CONCAT(YEARWEEK(date), ' Sunday'), '%X%V %W'), ' 00:00');

CREATE FUNCTION IF NOT EXISTS add_date_by_scope(date TEXT,scope TEXT) RETURNS TEXT
RETURN DATE_FORMAT(
    CASE WHEN LOWER(scope) = 'by_hour'
        THEN DATE_ADD(DATE_FORMAT(date, '%Y-%m-%d %H:00'), INTERVAL 1 HOUR)
    WHEN LOWER(scope) = 'by_day'
        THEN DATE_ADD(DATE_FORMAT(date, '%Y-%m-%d 00:00'), INTERVAL 1 DAY)
    WHEN LOWER(scope) = 'by_month'
        THEN DATE_ADD(DATE_FORMAT(date, '%Y-%m-01 00:00'), INTERVAL 1 MONTH)
    WHEN LOWER(scope) = 'by_year'
        THEN DATE_ADD(DATE_FORMAT(date, '%Y-01-01 00:00'), INTERVAL 1 YEAR)
    ELSE DATE_ADD(DATE_FORMAT(date, '%Y-%m-%d %H:00'), INTERVAL 1 HOUR)
    END,
    '%Y-%m-%d %H:00'
);

CREATE FUNCTION IF NOT EXISTS get_date_formatted_by_scope(date TEXT, scope TEXT) RETURNS TEXT
RETURN CASE WHEN LOWER(scope) = 'by_hour'
    THEN DATE_FORMAT(date, '%Y-%m-%d %H:00')
WHEN LOWER(scope) = 'by_day'
    THEN DATE_FORMAT(date, '%Y-%m-%d 00:00')
WHEN LOWER(scope) = 'by_month'
    THEN DATE_FORMAT(date, '%Y-%m-01 00:00')
WHEN LOWER(scope) = 'by_year'
    THEN DATE_FORMAT(date, '%Y-01-01 00:00')
ELSE DATE_FORMAT(date, '%Y-%m-%d %H:00')
END;
