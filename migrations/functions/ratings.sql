ALTER TABLE tbl_client_reviews
MODIFY COLUMN is_deleted TINYINT DEFAULT 0;

CREATE FUNCTION get_user_rating(
    userId INT,
    serviceProviderId INT,
    appointmentId INT
)
    RETURNS DECIMAL
    DETERMINISTIC
BEGIN
    DECLARE data DECIMAL;
    SELECT ROUND(AVG(ratting), 2) INTO data
    FROM tbl_client_reviews
    WHERE user_id = userId
    AND IF(serviceProviderId IS NULL, TRUE, service_provider_id = serviceProviderId)
    AND IF(appointmentId IS NULL, TRUE, order_id = appointmentId)
    AND NOT is_deleted;
    RETURN COALESCE(data, 0);
END;

CREATE FUNCTION get_user_review_count(
    userId INT,
    serviceProviderId INT,
    appointmentId INT
)
    RETURNS DECIMAL
    DETERMINISTIC
BEGIN
    DECLARE data DECIMAL;
    SELECT COUNT(id) INTO data
    FROM tbl_client_reviews
    WHERE user_id = userId
    AND IF(serviceProviderId IS NULL, TRUE, service_provider_id = serviceProviderId)
    AND IF(appointmentId IS NULL, TRUE, order_id = appointmentId)
    AND NOT is_deleted;
    RETURN COALESCE(data, 0);
END;

CREATE FUNCTION get_client_rating(
    clientId INT,
    serviceProviderId INT,
    appointmentId INT
)
    RETURNS DECIMAL
    DETERMINISTIC
BEGIN
    DECLARE data DECIMAL;
    SELECT ROUND(AVG(ratting), 2) INTO data
    FROM tbl_client_reviews
    WHERE client_id = clientId
    AND IF(serviceProviderId IS NULL, TRUE, service_provider_id = serviceProviderId)
    AND IF(appointmentId IS NULL, TRUE, order_id = appointmentId)
    AND NOT is_deleted;
    RETURN COALESCE(data, 0);
END;

CREATE FUNCTION get_client_review_count(
    clientId INT,
    serviceProviderId INT,
    appointmentId INT
)
    RETURNS DECIMAL
    DETERMINISTIC
BEGIN
    DECLARE data DECIMAL;
    SELECT COUNT(id) INTO data
    FROM tbl_client_reviews
    WHERE client_id = clientId
    AND IF(serviceProviderId IS NULL, TRUE, service_provider_id = serviceProviderId)
    AND IF(appointmentId IS NULL, TRUE, order_id = appointmentId)
    AND NOT is_deleted;
    RETURN COALESCE(data, 0);
END;

CREATE FUNCTION get_service_provider_rating(
    serviceProviderId INT,
    appointmentId INT
)
    RETURNS DECIMAL
    DETERMINISTIC
BEGIN
    DECLARE data DECIMAL;
    SELECT ROUND(AVG(ratting), 2) INTO data
    FROM tbl_user_review
    WHERE service_provider_id = serviceProviderId
    AND type = 'service_provider'
    AND IF(appointmentId IS NULL, TRUE, order_id = appointmentId)
    AND NOT is_deleted;
    RETURN COALESCE(data, 0);
END;

CREATE FUNCTION get_service_provider_review_count(
    serviceProviderId INT,
    appointmentId INT
)
    RETURNS DECIMAL
    DETERMINISTIC
BEGIN
    DECLARE data DECIMAL;
    SELECT COUNT(id) INTO data
    FROM tbl_user_review
    WHERE service_provider_id = serviceProviderId
    AND type = 'service_provider'
    AND IF(appointmentId IS NULL, TRUE, order_id = appointmentId)
    AND NOT is_deleted;
    RETURN COALESCE(data, 0);
END;

CREATE FUNCTION get_service_provider_followers_count(
    serviceProviderId INT
)
    RETURNS INT
    DETERMINISTIC
BEGIN
    DECLARE data INT;
    SELECT COUNT(DISTINCT b.user_id) INTO data
    FROM tbl_bookmark AS b
    JOIN tbl_user u ON b.user_id = u.id
        AND u.status = 'Active'
        AND NOT u.is_deleted
    JOIN tbl_service_provider sp ON sp.id = b.service_provider_id
        AND sp.is_deleted = 0
    WHERE b.service_provider_id = serviceProviderId
    AND b.status;
    RETURN COALESCE(data, 0);
END;
