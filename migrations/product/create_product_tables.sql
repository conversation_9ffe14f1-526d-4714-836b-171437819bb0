CREATE TABLE IF NOT EXISTS `blookddb`.`product_category` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NOT NULL,
  `image` VARCHAR(255) NULL,
  `is_active` TINYINT NULL DEFAULT 0,
  `is_deleted` TINYINT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);


CREATE TABLE IF NOT EXISTS `blookddb`.`measure_unit` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `longname` VARCHAR(255) NOT NULL,
  `shortname` VARCHAR(10) NOT NULL,
  `is_active` TINYINT NULL DEFAULT 0,
  `is_deleted` TINYINT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);


CREATE TABLE IF NOT EXISTS `blookddb`.`product` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `service_provider_id` INT NOT NULL,
  `category_id` INT,
  `name` VARCHAR(255) NOT NULL,
  `description` VARCHAR(255) DEFAULT '',
  `is_active` TINYINT NULL DEFAULT 0,
  `is_deleted` TINYINT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (category_id) REFERENCES product_category(id)
);


CREATE TABLE IF NOT EXISTS `blookddb`.`product_variant` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `product_id` INT NOT NULL,
  `measure_unit_id` INT NOT NULL,
  `price` FLOAT NOT NULL,
  `size` FLOAT NOT NULL,
  `quantity` INT NOT NULL,
  `is_active` TINYINT NULL DEFAULT 0,
  `is_deleted` TINYINT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (product_id) REFERENCES product(id),
  FOREIGN KEY (measure_unit_id) REFERENCES measure_unit(id)
);


CREATE TABLE IF NOT EXISTS `blookddb`.`product_gallery` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `product_id` INT,
  `image` VARCHAR(255) NOT NULL,
  `is_deleted` TINYINT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (product_id) REFERENCES product(id)
);


CREATE TABLE IF NOT EXISTS `blookddb`.`product_purchase` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `product_id` INT NOT NULL,
  `variant_id` INT NOT NULL,
  -- eventually, an FK to a booking table; nullable to allow product purchase outside of bookings
  `booking_id` VARCHAR(255) NULL,
  -- should be an FK to a User table - which hasn't been refactored yet.
  `buyer_id` INT NOT NULL,
  -- eventually, should be FK to a promocode value in a promocode table
  `promocode` VARCHAR(255) DEFAULT '',
  `price` FLOAT NOT NULL,
  `quantity` INT NOT NULL,
  `image` VARCHAR(255) NULL,
  `status` VARCHAR(25) NULL,
  `is_deleted` TINYINT NOT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (product_id) REFERENCES product(id),
  FOREIGN KEY (variant_id) REFERENCES product_variant(id)
);

DROP TRIGGER IF EXISTS update_product_related_tables;

DELIMITER //

CREATE TRIGGER update_product_related_tables
AFTER UPDATE ON product
FOR EACH ROW
BEGIN
    IF NEW.is_deleted <> OLD.is_deleted THEN
        UPDATE product_gallery
        SET is_deleted = NEW.is_deleted
        WHERE product_id = NEW.id;

        UPDATE product_variant
        SET is_deleted = NEW.is_deleted
        WHERE product_id = NEW.id;
    END IF;
END//

DELIMITER ;


INSERT INTO product_category 
	(name, image, is_active)
VALUES 
	('Hair Care', '130871665192224.png', 1),
    ('Facial Treatments', '482531665192235.png', 1),
    ('Nails', '876541665192246.png', 1),
    ('Other', '319581669351041.png', 1);
  
  
INSERT INTO measure_unit 
	(longname, shortname, is_active)
VALUES 
	('Kilogram', 'kg', 1),
    ('Gram', 'g', 1),
    ('Liter', 'ltr', 1),
    ('Milliliter', 'mlt', 1),
    ('Gallons', 'gal', 1),
	('Ounces', 'oz', 1);
  
-- DROP TABLE IF EXISTS product, product_category, product_purchase, product_gallery;
