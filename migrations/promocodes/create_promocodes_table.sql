CREATE TABLE IF NOT EXISTS `blookddb`.`promocodes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `title` VARCHAR(255) NULL,
  `discount` FLOAT(4,2) NULL DEFAULT 0.00,
  `max_discount` FLOAT(4,2) NULL,
  `discount_type` VARCHAR(20) NULL,
  `max_radeems` INT,
  `image` VARCHAR(255) NULL,
  `description` VARCHAR(255) NULL,
  `expiration_date` DATETIME NOT NULL DEFAULT '2060-12-31 23-59-59',
  `is_deleted` TINYINT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `id_UNIQUE` (`id` ASC) VISIBLE);

CREATE TABLE `blookddb`.`promocode_owners` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `service_provider_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `service_provider_id`
    FOREIGN KEY ()
    REFERENCES `blookddb`.`tbl_service_provider` ()
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);
