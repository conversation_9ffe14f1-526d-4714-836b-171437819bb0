CREATE TABLE IF NOT EXISTS block_list (
  id VARCHAR(36) DEFAULT (UUID()) NOT NULL UNIQUE,
  blockedUserUUID VARCHAR(36) NOT NULL,
  blockedByUserUUID VARCHAR(36) NOT NULL,
  createdDate TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  isDeleted TINYINT DEFAULT FALSE
);

CREATE INDEX idx_blocked_user_id ON block_list (blockedUserUUID);
CREATE INDEX idx_blocked_by_user_id ON block_list (blockedByUserUUID);
