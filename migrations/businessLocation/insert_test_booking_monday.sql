insert into tbl_appointment_booking
(
  booking_id,
  service_provider_id,
  business_location_id,
  customer_id,
  client_id,
  user_id,
  customer_name,
  date,
  slot_time,
  end_datetime,
  total_duration,
  booking_status,
  booking_type,
  sub_total,
  total_amount,
  aftertip_payment_status,
  payment_status
)
values
(
  'ORDER-Test-1',
  2,
  4,
  0,
  6,
  72,
  'Parish',
  '2024-05-06',
  '18:00:00',
  '2024-05-06 18:30:00',
  30,
  'Accepted',
  'online',
  25.00,
  25.00,
  'Unpaid',
  'paid'
);
