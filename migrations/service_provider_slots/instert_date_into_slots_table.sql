ALTER TABLE tbl_service_provider_available_slot
ADD COLUMN date DATE DEFAULT NULL,
ADD COLUMN updated_at TIMESTAMP DEFAULT NOW();

DROP TABLE IF EXISTS cleanup_slot_availability_queue;

CREATE TABLE cleanup_slot_availability_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    query TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    executed_at TIMESTAMP NULL,
    status ENUM('pending', 'completed', 'error') DEFAULT 'pending'
);

drop EVENT IF EXISTS process_cleanup_queue_event;

CREATE EVENT IF NOT EXISTS process_cleanup_queue_event
ON SCHEDULE EVERY 1 SECOND
DO BEGIN
    DECLARE query_id INT;
    DECLARE query_text TEXT;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        UPDATE cleanup_slot_availability_queue
        SET status = 'error',
            executed_at = NOW()
        WHERE id = query_id;
    END;

    SELECT id, query INTO query_id, query_text
    FROM cleanup_slot_availability_queue
    WHERE status = 'pending'
    ORDER BY created_at
    LIMIT 1;

    IF query_id IS NOT NULL THEN
        SET @sql = query_text;
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;

        UPDATE cleanup_slot_availability_queue
        SET status = 'completed',
            executed_at = NOW()
        WHERE id = query_id;
    END IF;
END;

DROP TRIGGER IF EXISTS after_availability_slot_insert;

CREATE TRIGGER after_availability_slot_insert
    AFTER INSERT ON tbl_service_provider_available_slot
    FOR EACH ROW
BEGIN
    IF NEW.date IS NOT NULL THEN
        INSERT INTO cleanup_slot_availability_queue (query)
        VALUES (CONCAT(
            'UPDATE tbl_service_provider_available_slot ',
            'SET is_deleted = 1, updated_at = NOW() ',
            'WHERE service_provider_id = ', NEW.service_provider_id,
            ' AND business_location_id = ', NEW.business_location_id,
            ' AND date = ''', NEW.date, '''',
            ' AND id != ', NEW.id,
            ' AND (',
            ' CONCAT(DATE_FORMAT(''', NEW.date, ''', ''%Y-%m-%d''), '' '', ''', NEW.from_time, ''')',
            ' BETWEEN ',
            ' CONCAT(DATE_FORMAT(''', NEW.date, ''', ''%Y-%m-%d''), '' '', from_time)',
            ' AND ',
            ' CONCAT(DATE_FORMAT(''', NEW.date, ''', ''%Y-%m-%d''), '' '', to_time)',
            ' OR ',
            ' CONCAT(DATE_FORMAT(''', NEW.date, ''', ''%Y-%m-%d''), '' '', ''', NEW.to_time, ''')',
            ' BETWEEN ',
            ' CONCAT(DATE_FORMAT(''', NEW.date, ''', ''%Y-%m-%d''), '' '', from_time)',
            ' AND ',
            'CONCAT(DATE_FORMAT(date, ''%Y-%m-%d''), '' '', to_time)',
            ')',
            ' AND NOT is_deleted',
            ' AND insertdate < ''', NEW.insertdate, ''''
        ));
    ELSE
        INSERT INTO cleanup_slot_availability_queue (query)
        VALUES (CONCAT(
            'UPDATE tbl_service_provider_available_slot ',
            'SET is_deleted = 1, updated_at = NOW() ',
            'WHERE service_provider_id = ', NEW.service_provider_id,
            ' AND business_location_id = ', NEW.business_location_id,
            ' AND day = ''', NEW.day, ''' ',
            ' AND date IS NULL',
            ' AND id != ', NEW.id,
            ' AND NOT is_deleted',
            ' AND insertdate < ''', NEW.insertdate, ''''
        ));
    END IF;
END;
