CREATE TABLE IF NOT EXISTS service_provider_cards
(
    service_provider_id INT(50)     NOT NULL,
    stripe_card_id      VARCHAR(50) NOT NULL,
    last4               VARCHAR(4)  NOT NULL,
    exp_month           INT         NOT NULL,
    exp_year            INT         NOT NULL,
    brand               VARCHAR(50) NOT NULL,
    created_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_deleted          TINYINT   DEFAULT FALSE,
    PRIMARY KEY (service_provider_id, stripe_card_id),
    FOREIGN KEY (service_provider_id) REFERENCES tbl_service_provider (id) ON DELETE CASCADE
);