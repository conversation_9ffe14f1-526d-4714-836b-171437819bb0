# Style Guide

## General Principles

1. Write self-documenting code without unnecessary comments
2. Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError)
3. Keep functions small and focused
4. Use early returns to avoid nesting
5. Use TypeScript for all code
6. Follow consistent naming conventions

## Naming Conventions

1. Use PascalCase for classes and interfaces
2. Use camelCase for variables, functions, and methods
3. Use UPPERCASE for constants
4. Use descriptive names that indicate purpose
5. Use auxiliary verbs for boolean variables (is, has, can, etc.)

## Code Structure

1. One class/interface per file
2. Group related functionality together
3. Keep files under 300 lines
4. Use meaningful directory structure
5. Separate concerns (business logic, data access, UI)

## TypeScript Usage

1. Use interfaces for object shapes
2. Use type aliases for union types
3. Avoid using 'any' type
4. Use generics when appropriate
5. Define return types for all functions

## Switch Statements

1. No empty lines between cases
2. Use block scopes for each case
3. Include default case
4. Keep cases aligned
5. Use early returns within cases

## Error Handling

1. Use try/catch for async operations
2. Log errors with context
3. Return meaningful error messages
4. Use custom error types when appropriate
5. Handle edge cases explicitly

## Testing

1. Write unit tests for all business logic
2. Use meaningful test descriptions
3. Follow AAA pattern (Arrange, Act, Assert)
4. Mock external dependencies
5. Test edge cases and error conditions

## Documentation

1. Document public APIs
2. Use JSDoc for complex functions
3. Keep documentation up to date
4. Document breaking changes
5. Include examples for complex usage

## Git Practices

1. Write meaningful commit messages
2. Keep commits focused and atomic
3. Use feature branches
4. Review code before merging
5. Keep PRs small and focused