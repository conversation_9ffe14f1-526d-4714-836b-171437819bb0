FROM node:21-bullseye-slim as builder
WORKDIR /home/<USER>
COPY package*.json ./
RUN chown -R node:node .
USER node
RUN npm ci
COPY --chown=node:node . .
RUN npm run swagger

FROM mcr.microsoft.com/playwright:v1.41.0-jammy as production
WORKDIR /home/<USER>
COPY --chown=node:node --from=builder /home/<USER>
ARG DOTENV_KEY
ENV DOTENV_KEY=${DOTENV_KEY} PUPPETEER_EXECUTABLE_PATH=/ms-playwright/chromium-1097/chrome-linux/chrome
EXPOSE 8080
CMD ["npm", "run", "serve:prod"]
