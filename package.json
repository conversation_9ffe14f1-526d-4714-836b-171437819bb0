{"name": "blookd", "version": "2.2.1", "description": "Blookd app", "main": "index.js", "scripts": {"swagger": "ts-node swagger.ts", "dev": "nodemon", "serve:dev": "set NODE_ENV=dev && npx ts-node server.ts", "serve:prod": "NODE_OPTIONS=--max_old_space_size=4096 npx ts-node server.ts", "serve:legacy": "node --trace-uncaught server.js", "build": "tsc --build", "test": "jest"}, "author": "hyperlink", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.645.0", "@types/swagger-ui-express": "^4.1.7", "body-parser": "^1.20.2", "cls-hooked": "^4.2.2", "cryptlib": "^1.0.3", "dotenv": "^16.3.1", "dotenv-expand": "^11.0.6", "ejs": "^3.1.10", "exceljs": "^4.4.0", "express": "^4.18.2", "express-promise-router": "^4.1.1", "express-session": "^1.17.2", "express-winston": "^4.2.0", "generic-pool": "^3.9.0", "geo-tz": "^8.0.2", "handlebars": "^4.7.8", "install": "^0.13.0", "joi": "^17.13.3", "localizify": "^1.2.4", "lodash": "^4.17.21", "module-alias": "^2.2.3", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mysql": "^2.18.1", "mysql2": "^3.9.4", "node-async-loop": "^1.2.2", "node-cron": "^3.0.2", "node-datetime": "^2.1.2", "node-pushnotifications": "^1.1.12", "nodemailer": "^6.7.2", "nodemon": "^3.0.1", "npm": "^10.9.2", "openapi-types": "^12.1.3", "puppeteer": "^24.1.1", "rand-token": "^1.0.1", "randomstring": "^1.3.1", "request": "^2.88.2", "sequelize": "^6.37.3", "short-unique-id": "^5.2.0", "socket.io": "^4.7.2", "stripe": "^16.10.0", "swagger-autogen": "^2.23.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^4.8.0", "typescript": "^5.7.3", "uuid": "^9.0.1", "Validator": "^1.1.4", "winston": "^3.13.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/nodemailer": "^6.4.15", "@types/socket.io": "^3.0.2", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "^7.5.0", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2"}, "_moduleAliases": {"@/": "./", "@/modules/v2": "./modules/v2", "@/src": "./src", "~/tests/": "./tests/modules/v2"}}