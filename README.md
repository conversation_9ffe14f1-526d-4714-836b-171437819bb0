## Blookd Api

This is the api server powered by NodeJS

## Tech Stack
- RDS
- Express
- Twilio Api
- Stipe Api
- S3

## Running App

```bash
# build typescript
npm run build
# run dev code
npm run serve:dev
# run prod code
pnpm run serve:prod
```
## Manage Secrets

Our secrets are powered by [dotenv-vault](https://www.dotenv.org/). 
To pull the secrets into local make sure to get DOTENV_KEY from your admin.
Supported .env postfixes:
1. staging (dev)
2. production

```bash
# use to pull [staging, production] environment .env file
npx dotenv-vault@latest pull [staging, production]
# use to update [staging, production] environment .env file
# do that after modifying your env
npx dotenv-vault@latest push [staging, production]
```

You should only need to pull/push remote .env files
to manage them. NodeJS and dotenv are pulling the .env
file from the dotenv-vault on startup, so you should not
need to have a local version of .env. Just make sure your 
DOTENV_KEY is set for that to work.