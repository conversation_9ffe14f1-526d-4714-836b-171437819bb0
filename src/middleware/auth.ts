import { NextFunction, Request, Response } from 'express'
import { Global } from '@/src/constants'
import { logger } from '@/src/utils'
import { getUserDeviceByToken, tryDecryptingRaw } from '@/src/service'

const allowedWithoutToken = [
  'document',
  'webhooks',
  'forgot_password',
  'verify_otp',
]

async function validateToken(req: Request, res: Response, next: NextFunction) {
  const pathData = new URL(
    req.protocol + '://' + req.get('host') + req.originalUrl
  ).pathname.split('/')
  if (
    !pathData.includes('v2') ||
    pathData.some((path) => allowedWithoutToken.includes(path))
  ) {
    logger.info({
      message: `Skip new validation`,
      meta: { path: req.originalUrl },
    })
    return next()
  }

  logger.info({
    message: 'Running new validation',
    meta: { path: req.originalUrl },
  })
  const apiKey = tryDecryptingRaw(
    req.headers['api-key'] ?? req.headers['API-KEY'],
    'api-key'
  )

  if (apiKey !== Global.API_KEY) {
    return res.status(401).send('Invalid api_key')
  }

  const token = tryDecryptingRaw(
    req.headers['token'] ?? req.headers.authorization?.replace('Bearer ', ''),
    'token'
  )
  if (!token) {
    return res.status(401).send('Missing auth header')
  }

  const device = await getUserDeviceByToken(token)
  if (!device) {
    return res.status(401).send('Unknown user')
  }

  req.user_id = device.user_id
  req.user_uuid = device.user_uuid
  req.user_type = device.user_type

  return next()
}

export { validateToken }
