import swaggerJsdoc from 'swagger-jsdoc'

const options = {
  definition: {
    openapi: '3.1.0',
    info: {
      title: 'API Documentation',
      version: '2.2.1',
      description: 'API serves as a backend for the Blookd App',
      license: {
        name: 'MIT',
        url: 'https://spdx.org/licenses/MIT.html',
      },
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        }
      }
    },
    security: [{
      bearerAuth: []
    }],
    servers: [
      {
        url: 'https://dev.api.blookd.com',
        description: 'Development Server',
      },
      {
        url: 'https://api.blookd.com',
        description: 'Production Server',
      },
    ],
  },
  apis: [
    './modules/v1/**/*js',
    './modules/v1/*/*.ts',
    './modules/v2/**/*.ts',
    './modules/v2/**/*.js',
  ],
};

export const specs = swaggerJsdoc(options)
