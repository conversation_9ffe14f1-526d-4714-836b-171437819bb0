import { Request, Response } from 'express'
import { decryption } from '@/src/utils'


/* Decrypt Request body and save decrepted string back into request.body. */
function decrypt_body(req: Request, res: Response, next) {
    if (req.body.length === 0 || typeof(req.body) == 'object') {
        next()
        return
    }

    try {
        const decrypted = decryption(req.body)
        req.body = JSON.parse(decrypted)
    } catch (error) {
        return res.status(401).send('Decryption failed')
    }
} // authenticate


export { decrypt_body }
