import { db_conn, named_db_con } from '@/src/db'
import {
  dateStringInTz,
  logger,
  SIMPLE_DATE_FORMAT,
  SIMPLE_DATE_FORMAT_WITH_12HR_TIME,
  SIMPLE_24HR_TIME_FORMAT,
} from '@/src/utils'
import moment, { Moment } from 'moment'
import {
  getBusinessLocations,
  addBusinessLocation,
} from '@/src/service'
import { ResultSetHeader } from 'mysql2'
import { map, omit, entries, filter, reduce, keys } from 'lodash'
import { find as geoFind } from 'geo-tz'
import { Break } from '@/src/db/models/break'
import { v4 as uuidv4 } from 'uuid'


interface ISlotRange {
  from: string
  to: string
  day:
    | 'Monday'
    | 'Tuesday'
    | 'Wednesday'
    | 'Thursday'
    | 'Friday'
    | 'Saturday'
    | 'Sunday'
}

interface IAvailabilityProps {
  id: number | string
  availability: Array<ISlotRange>
}

export interface BusySlot {
  block_slot_id: number | null
  appointment_id: number | null
  date: string
  start_date: Moment
  end_date: Moment
  total_duration: number
  longitude: string
  latitude: string
  timezone_code: string
  date_format: string
}

export interface Schedule {
  slot_id: number
  slot_uuid?: string
  date: string
  latitude: string
  longitude: string
  start_date: Moment
  end_date: Moment
  day: string
  timezone_code: string
  date_format: string
}

export interface ScheduleBreak {
  id?: string
  availableSlotId: string
  startTime: string
  endTime: string
}

export interface ScheduleSlot {
  id?: number
  uuid?: string
  date: string | null
  day: string
  fromTime: string
  toTime: string
  serviceProviderId: number
  businessLocationId: number
  breaks?: ScheduleBreak[]
}

export interface TimeOffBlock {
  block_date_id: number
  block_slot_id: number
  start_date: string
  end_date: string
  longitude: string
  latitude: string
  timezone_code: string
}

export interface TimeOff {
  fromTime: string;
  toTime: string;
  date: string;
  serviceProviderId?: number;
  businessLocationId?: number;
}

export async function getScheduleForDates(
  serviceProviderId: number,
  businessLocationId: number,
  fromDate: string, //expected format YYYY-MM-DD
  toDate: string //expected format YYYY-MM-DD
): Promise<Schedule[]> {
  const params = {
    serviceProviderId,
    businessLocationId,
    fromDate,
    toDate,
  }

  try {
    const [rows] = await named_db_con.query(`
      WITH RECURSIVE calendar(date) AS (
        SELECT :fromDate AS date
        UNION
        SELECT DATE_ADD(calendar.date, INTERVAL 1 DAY)
        FROM calendar
        WHERE DATE_ADD(calendar.date, INTERVAL 1 DAY) <= :toDate
      ), schedule AS (
        SELECT
          slot.id,
          slot.uuid,
          slot.day,
          slot.date as specific_date,
          slot.from_time,
          slot.to_time,
          bl.latitude,
          bl.longitude,
          bl.timezone_code,
          slot.business_location_id,
          slot.service_provider_id
        FROM tbl_service_provider_available_slot slot
        JOIN tbl_business_location bl ON bl.id = slot.business_location_id
          AND bl.id = :businessLocationId
          AND bl.is_deleted = 0
        JOIN tbl_service_provider sp ON sp.id = slot.service_provider_id
          AND sp.id = :serviceProviderId
          AND sp.is_deleted = 0
        WHERE slot.is_deleted = 0
        AND slot.from_time IS NOT NULL
        AND slot.from_time != ''
        AND slot.to_time IS NOT NULL
        AND slot.to_time != ''
      ) SELECT
        s.id AS slot_id,
        s.uuid AS slot_uuid,
        DATE_FORMAT(COALESCE(s.specific_date, c.date), '%Y-%m-%d') AS date,
        s.latitude,
        s.longitude,
        CONCAT(COALESCE(s.specific_date, c.date), ' ', s.from_time) AS start_date,
        CONCAT(COALESCE(s.specific_date, c.date), ' ', s.to_time) AS end_date,
        s.day,
        s.timezone_code,
        'YYYY-MM-DD HH:mm' AS date_format
      FROM schedule s
      LEFT JOIN calendar c ON s.specific_date IS NULL
        AND s.day = DAYNAME(c.date)
      WHERE (
        s.specific_date IS NOT NULL
        AND s.specific_date BETWEEN :fromDate AND :toDate
      )
      OR (
        c.date IS NOT NULL
        AND s.specific_date IS NULL
        AND NOT EXISTS (
          SELECT 1 FROM schedule s2
          WHERE s2.specific_date = c.date
          AND s2.business_location_id = s.business_location_id
          AND s2.service_provider_id = s.service_provider_id
        )
      )
      ORDER BY CONCAT(COALESCE(s.specific_date, c.date), ' ', s.from_time)
    `, params)
    return map(rows as any[], (it: any) => {
      const tz =
        it.timezone_code ??
        geoFind(parseFloat(it.latitude), parseFloat(it.longitude))[0]
      return {
        ...it,
        start_date: dateStringInTz(it.start_date, it.date_format, tz),
        end_date: dateStringInTz(it.end_date, it.date_format, tz),
        timezone_code: tz,
      }
    })
  } catch (e) {
    logger.error(`Failed to get a schedule for ${JSON.stringify(params)}`, e)
    return []
  }
}

export async function getBusySlots(
  serviceProviderId: number,
  businessLocationId: number,
  fromDate: string,
  toDate: string
) {
  if (!serviceProviderId || !businessLocationId) {
    return []
  }

  const params = {
    serviceProviderId,
    businessLocationId,
    fromDate: fromDate ?? moment().format(SIMPLE_DATE_FORMAT),
    toDate: toDate ?? moment().format(SIMPLE_DATE_FORMAT),
  }

  try {
    const [rows] = await named_db_con.query(
      `
    SELECT
      NULL AS block_slot_id,
      b.id AS appointment_id,
      DATE_FORMAT(b.date, '%Y-%m-%d') AS date,
      CONCAT(b.date, ' ', b.slot_time) AS start_date,
      DATE_FORMAT(
      CONCAT(b.date, ' ', b.slot_time) + INTERVAL b.total_duration MINUTE + INTERVAL b.additional_duration MINUTE,
      '%Y-%m-%d %H:%i'
      ) AS end_date,
      b.total_duration,
      b.additional_duration,
      bl.longitude,
      bl.latitude,
      bl.timezone_code,
      'YYYY-MM-DD HH:mm' AS date_format
    FROM tbl_appointment_booking b
    JOIN tbl_service_provider sp ON sp.id = b.service_provider_id
      AND sp.id = :serviceProviderId
      AND sp.is_deleted = 0
    JOIN tbl_business_location bl ON bl.id = b.business_location_id
      AND bl.id = :businessLocationId
      AND bl.is_deleted = 0
    WHERE b.date BETWEEN :fromDate AND :toDate
      AND LOWER(b.booking_status) IN ('accepted', 'in progress')
      AND b.is_deleted = 0
    UNION
    SELECT
      ts.id AS block_slot_id,
      NULL AS appointment_id,
      DATE_FORMAT(bd.date, '%Y-%m-%d') AS date,
      CONCAT(bd.date, ' ', ts.from_time) as start_date,
      CONCAT(bd.date, ' ', ts.to_time) as end_date,
      TIMESTAMPDIFF(
        MINUTE,
        CONCAT(bd.date, ' ', ts.from_time),
        CONCAT(bd.date, ' ', ts.to_time)
      ) AS total_duration,
      bl.longitude,
      bl.latitude,
      bl.timezone_code,
      'YYYY-MM-DD HH:mm' AS date_format
    FROM tbl_service_provider_block_date AS bd
    JOIN tbl_service_provider_block_time_slot ts ON bd.id = ts.block_date_id
    JOIN tbl_business_location bl ON bl.id = bd.business_location_id
    WHERE bd.is_deleted = 0
    AND bd.service_provider_id = :serviceProviderId
    AND bd.business_location_id = :businessLocationId
    AND bd.date BETWEEN :fromDate AND :toDate
    ORDER BY start_date, end_date
    `,
      params
    )

    return map(rows as any[], (it: any) => {
      const tz =
        it.timezone_code ??
        geoFind(parseFloat(it.latitude), parseFloat(it.longitude))[0]
      return {
        ...it,
        start_date: dateStringInTz(it.start_date, it.date_format, tz),
        end_date: dateStringInTz(it.end_date, it.date_format, tz),
        timezone_code: tz,
      }
    })
  } catch (e) {
    logger.error(`Failed to retrieve busy slots ${JSON.stringify(params)}`, e)
    return []
  }
}

export async function getBlackoutDates(
  serviceProviderId: number,
  businessLocationId: number,
  startDate: string,
  endDate: string
): Promise<TimeOffBlock[]> {
  try {
    const [rows] = await named_db_con.query(
      `
      SELECT
        bd.id AS block_date_id,
        ts.id AS block_slot_id,
        CONCAT(bd.date, ' ', ts.from_time) as start_date,
        CONCAT(bd.date, ' ', ts.to_time) as end_date,
        bl.longitude,
        bl.latitude,
        bl.timezone_code
      FROM tbl_service_provider_block_date AS bd
      JOIN tbl_service_provider_block_time_slot ts ON bd.id = ts.block_date_id
      JOIN tbl_business_location bl ON bl.id = bd.business_location_id
      WHERE bd.is_deleted = 0
      AND bd.service_provider_id = :serviceProviderId
      AND bd.business_location_id = :businessLocationId
      AND bd.date BETWEEN :startDate AND :endDate
    `,
      { serviceProviderId, businessLocationId, startDate, endDate }
    )

    return (rows as TimeOffBlock[]) ?? []
  } catch (e) {
    logger.error('Error getting', e)
    return []
  }
}

export async function upsertServiceProviderBlock(data: any) {
  try {
    await named_db_con.query(
      `
      UPDATE tbl_service_provider_block_date bd
      JOIN tbl_service_provider_block_time_slot ts ON ts.block_date_id = bd.id
      SET bd.is_deleted = 1, ts.is_deleted = 1
      WHERE bd.service_provider_id = :serviceProviderId
      AND bd.business_location_id = :businessLocationId
      AND (
        DATE_FORMAT(bd.date, '%Y-%m-%d') > DATE_FORMAT(CURRENT_TIMESTAMP, '%Y-%m-%d')
        OR bd.date = :date
      )
      AND bd.is_deleted = 0
    `,
      {
        serviceProviderId: data.service_provider_id,
        businessLocationId: data.business_location_id,
        date: data.date,
      }
    )

    const [result] = await db_conn.query(
      `INSERT INTO tbl_service_provider_block_date SET ?`,
      data
    )
    return (result as ResultSetHeader).insertId
  } catch (e) {
    logger.error('Failed to upsert block availability', e)
  }
}

export async function deleteFutureServiceProviderBlockAvailabilitySlots(
  serviceProviderId: number,
  businessLocationId: number
) {
  try {
    await named_db_con.query(
      `
      UPDATE tbl_service_provider_block_time_slot ts
      JOIN tbl_service_provider_block_date bd ON bd.id = ts.block_date_id
      SET ts.is_deleted = 1
      WHERE ts.service_provider_id = :serviceProviderId
      AND ts.business_location_id = :businessLocationId
      AND (
        CONCAT(bd.date, ' ', ts.from_time) > CURRENT_TIMESTAMP
        OR bd.is_deleted = 1
      )
      AND ts.is_deleted = 0
    `,
      { serviceProviderId, businessLocationId }
    )
  } catch (e) {
    logger.error('Failed to soft delete future block availability slots', e)
  }
}

export async function createServiceProviderBlockAvailabilitySlot(data: any) {
  try {
    await named_db_con.query(`
      UPDATE tbl_service_provider_block_time_slot ts
        SET ts.is_deleted = 1
      WHERE ts.service_provider_id = :serviceProviderId
      AND ts.business_location_id = :businessLocationId
      AND ts.block_date_id = :blockDateId
      AND NOT ts.is_deleted
      AND (
        CONCAT('1999-01-01', ' ', :fromTime)
          BETWEEN CONCAT('1999-01-01', ' ', ts.from_time)
          AND CONCAT('1999-01-01', ' ', ts.to_time)
        OR CONCAT('1999-01-01', ' ', :toTime)
          BETWEEN CONCAT('1999-01-01', ' ', ts.from_time)
          AND CONCAT('1999-01-01', ' ', ts.to_time)
      )
    `, {
      serviceProviderId: data.service_provider_id,
      businessLocationId: data.business_location_id,
      blockDateId: data.block_date_id,
      fromTime: data.from_time,
      toTime: data.to_time
    });

    // Then create the new slot
    const [result] = await db_conn.query(
      `INSERT INTO tbl_service_provider_block_time_slot SET ?`,
      data
    );

    return (result as ResultSetHeader).insertId;
  } catch (e) {
    logger.error('Failed to create block availability slots', e);
  }
}

export async function getBlockAvailability(
  serviceProviderId: number,
  businessLocationId: number
) {
  try {
    const [rows] =
      serviceProviderId && businessLocationId
        ? await named_db_con.query(
            `
        SELECT
          bd.id,
          DATE_FORMAT(bd.date, '%Y-%m-%d') AS date,
          IFNULL(
            (
              SELECT JSON_ARRAYAGG(
                JSON_OBJECT('id', ts.id, 'from_time', ts.from_time, 'to_time', ts.to_time)
              )
              FROM tbl_service_provider_block_time_slot ts
              WHERE ts.block_date_id = bd.id
              AND ts.is_deleted = 0
            ),
            '[]'
          ) AS booked_slot_json
        FROM tbl_service_provider_block_date bd
        WHERE bd.service_provider_id = :serviceProviderId
        AND bd.business_location_id = :businessLocationId
        AND bd.is_deleted = 0
      `,
            { serviceProviderId, businessLocationId }
          )
        : [[]]
    return ((rows as any) ?? []).map((it: any) => ({
      ...omit(it, 'booked_slot_json'),
      booked_slot: JSON.parse(it.booked_slot_json),
    }))
  } catch (e) {
    logger.error('Failed to retrieve block availability', e)
    return []
  }
}

export async function createBusinessLocationScheduleBlock(location: any) {
  logger.debug(location)
  if (!location?.id || !location?.service_provider_id) {
    logger.warn(
      `Missing required params to create schedule block. check [business_location_id: ${location?.id}, service_provider_id: ${location?.service_provider_id}]`
    )
    return
  }

  await Promise.all(
    location?.block_availability?.map(async (block: any) => {
      const blockId = await upsertServiceProviderBlock({
        service_provider_id: location.service_provider_id,
        business_location_id: location.id,
        date: block.date,
        insertdate: moment().format('YYYY-MM-DD HH:mm:ss'),
      })

      if (blockId) {
        await deleteFutureServiceProviderBlockAvailabilitySlots(
          location.service_provider_id,
          location.id
        )
        await Promise.all(
          block?.booked_slot?.map((slot: any) =>
            createServiceProviderBlockAvailabilitySlot({
              service_provider_id: location.service_provider_id,
              business_location_id: location.id,
              block_date_id: blockId,
              from_time: slot.from_time,
              to_time: slot.to_time,
              is_deleted: 0,
              insertdate: moment().format('YYYY-MM-DD HH:mm:ss'),
            })
          ) ?? []
        )
      }
    })
  )
}

export async function updateAvailability(
  serviceProviderId: string | number,
  locations: Array<any>
) {
  const existingLocations = await getBusinessLocations(serviceProviderId as number)
  const { newLocations, oldLocations } = filterNewAndOldLocations(locations, existingLocations)
  await updateOldLocationsAvailability(serviceProviderId, oldLocations)
  await createNewLocationsAndSetAvailability(serviceProviderId, newLocations)
}

function filterNewAndOldLocations(
  locations: Array<any>,
  existingLocations: Array<any>
) {
  const newLocations: Array<any> = []
  const oldLocations: Array<any> = []
  const existingNameMap = new Map(
    existingLocations.map((loc: any) => [loc?.name?.trim().toLowerCase(), loc])
  )
  const existingIdMap = new Map(
    existingLocations.map((loc: any) => [parseInt(loc?.id || -1, 10), loc])
  )

  locations.forEach((location) => {
    const existingByName = existingNameMap.get(
      location?.name?.trim().toLowerCase()
    )
    const existingById = existingIdMap.get(parseInt(location?.id || -1, 10))

    const existingLocation = existingByName || existingById

    if (existingLocation) {
      existingLocation.slot_available = location.slot_available
      oldLocations.push(existingLocation)
    } else {
      newLocations.push(location)
    }
  })

  return { newLocations, oldLocations }
}

async function updateOldLocationsAvailability(
  serviceProviderId: string | number,
  oldLocations: Array<any>
) {
  return await Promise.all(
    oldLocations.map((location: any) => upsertSlots(location?.slot_available?.map((it: any) => ({
      ...it,
      fromTime: it.from_time,
      toTime: it.to_time,
      businessLocationId: location.id,
      serviceProviderId: serviceProviderId as number,
    }))))
  )
}

async function createNewLocationsAndSetAvailability(
  serviceProviderId: string | number,
  newLocations: Array<any>
) {
  return await Promise.all(
    newLocations.map(async (location: any) => {
      const newLocationId = await addBusinessLocation({
        service_provider_id: serviceProviderId,
        name: location.name,
        latitude: location.latitude,
        longitude: location.longitude,
        country_name: location.country_name,
        country_code: location.country_code || '',
        state_name: location.state_name,
        city_name: location.city_name,
        postal_code: location.postal_code,
        status: 'Active',
        is_deleted: 0,
        insertdate: moment().format('YYYY-MM-DD HH:mm:ss'),
      })

      if (!newLocationId) {
        throw new Error(`Failed to create the new location ${ location.name }`)
      }

      if (location.slot_available[0] === null) {
        location.slot_available = populateDefaultAvailabilitySlots(
          newLocationId
        )
      }

      await createBusinessLocationScheduleBlock({
        ...(location ?? {}),
        id: newLocationId,
        service_provider_id: serviceProviderId,
      })

      await upsertSlots(
        location?.slot_available?.map((it: any) => ({
          ...it,
          fromTime: it.from_time,
          toTime: it.to_time,
          businessLocationId: newLocationId,
          serviceProviderId: serviceProviderId as number,
        }))
      )
    })
  )
}

export async function upsertSlots(slots: ScheduleSlot[]) {
  return Promise.all(
    slots?.map((it: ScheduleSlot) => createAvailabilitySlot(it))
  )
}

function populateDefaultAvailabilitySlots(locationId: number) {
  return [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ].map((day: string) => ({
    from_time: '',
    to_time: '',
    business_location_id: locationId,
    day,
  }))
}

async function createAvailabilitySlot(slot: ScheduleSlot): Promise<number> {
  if (!slot) {
    return 0;
  }

  const fromTime = slot.fromTime
    ? moment(`1990-10-10 ${slot.fromTime}`, SIMPLE_DATE_FORMAT_WITH_12HR_TIME).format(SIMPLE_24HR_TIME_FORMAT)
    : '';
  const toTime = slot.toTime
    ? moment(`1990-10-10 ${slot.toTime}`, SIMPLE_DATE_FORMAT_WITH_12HR_TIME).format(SIMPLE_24HR_TIME_FORMAT)
    : '';

  const query = `
    INSERT INTO tbl_service_provider_available_slot
    (business_location_id, service_provider_id, day, from_time, to_time, date)
    VALUES (:locationId, :serviceProviderId, :day, :fromTime, :toTime, :date)
    ON DUPLICATE KEY UPDATE
      from_time = :fromTime,
      to_time = :toTime,
      updated_at = NOW()
  `;

  const payload = {
    locationId: slot.businessLocationId,
    serviceProviderId: slot.serviceProviderId,
    day: slot.date ? moment(slot.date).format('dddd') : slot.day,
    fromTime,
    toTime,
    date: slot.date ? moment(slot.date).format(SIMPLE_DATE_FORMAT) : null
  }

  const [result] = await named_db_con.query(query, payload);
  const insertId = (result as ResultSetHeader).insertId;

  await cleanupScheduleConflicts(
    payload.serviceProviderId as number,
    payload.locationId,
    payload.day,
    payload.date,
    payload.fromTime,
    payload.toTime,
    insertId,
  );

  const [slotResult] = await named_db_con.query(`
    SELECT uuid FROM tbl_service_provider_available_slot WHERE id = :insertId
  `, { insertId })

  const slotUuid = slotResult[0]?.uuid

  await upsertBreaks(slotUuid, slot.breaks)

  return insertId;
}

async function upsertBreaks(availableSlotId: string, breaks?: ScheduleBreak[]): Promise<void> {
  if (!availableSlotId) {
    return
  }

  try {
    await Break.update(
      {
        isDeleted: true,
      },
      {
        where: {
          availableSlotId,
          isDeleted: false
        }
      }
    )

    if (!breaks) {
      return
    }

    const createdBreaks: any[] = []
    for (let index = 0; index < breaks.length; index++) {
      const breakItem = breaks[index]
      const uuid = uuidv4()

      await named_db_con.query(`
        INSERT INTO break (id, availableSlotId, startTime, endTime)
        VALUES (:id, :availableSlotId, :startTime, :endTime)
      `, {
        id: uuid,
        availableSlotId,
        startTime: breakItem.startTime,
        endTime: breakItem.endTime,
      })

      createdBreaks.push({
        id: uuid,
        availableSlotId,
        startTime: breakItem.startTime,
        endTime: breakItem.endTime,
      })
    }

    await cleanupConflictingBreaks(availableSlotId, createdBreaks)
  } catch (error) {
    logger.error({
      message: 'Failed to upsert breaks',
      error,
      meta: {
        availableSlotId,
        breaks,
      }
    })
    throw error
  }
}

async function cleanupConflictingBreaks(availableSlotId: string, newBreaks: any[]): Promise<void> {
  if (!newBreaks) {
    return
  }

  try {
    for (const newBreak of newBreaks) {
      const [timestampResult] = await named_db_con.query(`
        SELECT createdAt 
        FROM break 
        WHERE id = :excludeBreakId
      `, { excludeBreakId: newBreak.id })

      const createdAt = timestampResult[0]?.createdAt

      if (!createdAt) {
        continue
      }

      await named_db_con.query(`
        UPDATE break
        SET isDeleted = 1
        WHERE availableSlotId = :availableSlotId
        AND id != :excludeBreakId
        AND isDeleted = 0
        AND createdAt < :createdAt
        AND (
          CONCAT('2000-01-01 ', :startTime) BETWEEN CONCAT('2000-01-01 ', startTime) AND CONCAT('2000-01-01 ', endTime)
          OR CONCAT('2000-01-01 ', :endTime) BETWEEN CONCAT('2000-01-01 ', startTime) AND CONCAT('2000-01-01 ', endTime)
          OR CONCAT('2000-01-01 ', startTime) BETWEEN CONCAT('2000-01-01 ', :startTime) AND CONCAT('2000-01-01 ', :endTime)
          OR CONCAT('2000-01-01 ', endTime) BETWEEN CONCAT('2000-01-01 ', :startTime) AND CONCAT('2000-01-01 ', :endTime)
        )
      `, {
        availableSlotId,
        excludeBreakId: newBreak.id,
        createdAt,
        startTime: newBreak.startTime,
        endTime: newBreak.endTime
      })
    }
  } catch (error) {
    logger.error({
      message: 'Failed to cleanup conflicting breaks',
      error,
      meta: {
        availableSlotId,
        newBreaksCount: newBreaks.length,
      }
    })
  }
}

export async function cleanupBreaksBySlotIds(availableSlotIds: string[]): Promise<void> {
  if (!availableSlotIds) {
    return
  }

  try {
    await Break.update(
      { isDeleted: true },
      {
        where: {
          availableSlotId: availableSlotIds
        }
      }
    )
  } catch (error) {
    logger.error({
      message: 'Failed to cleanup breaks by slot IDs',
      error,
      meta: {
        availableSlotIds
      }
    })
  }
}

export async function getBreaksBySlotId(availableSlotId?: string): Promise<ScheduleBreak[]> {
  if (!availableSlotId) {
    return []
  }

  try {
    const breaks = await Break.findAll({
      where: {
        availableSlotId,
        isDeleted: false
      },
      order: [['startTime', 'ASC']],
      raw: true
    })

    return breaks as ScheduleBreak[]
  } catch (error) {
    logger.error({
      message: 'Failed to get breaks by slot ID',
      error,
      meta: {
        availableSlotId
      }
    })
    return []
  }
}

export async function getWorkSchedule(
  serviceProviderId: string,
  locationIds: Array<number>
) {
  let filterLocations = ''
  if (locationIds && locationIds.length > 0) {
    filterLocations = `and business_location_id in (${locationIds.join(', ')})`
  }

  const query = `
    SELECT *
    FROM tbl_service_provider_available_slot
    WHERE service_provider_id = ${serviceProviderId}
      ${filterLocations}
      and is_deleted = 0
  `
  const result = await named_db_con.query(query)
  return result[0]
}

async function cleanupScheduleConflicts(
  serviceProviderId: number,
  businessLocationId: number,
  day: string,
  date: string | null,
  fromTime: string,
  toTime: string,
  excludeSlotId: number
): Promise<void> {
  try {
    const [insertDateResult] = await named_db_con.query(`
      SELECT insertdate
      FROM tbl_service_provider_available_slot
      WHERE id = :excludeSlotId
    `, { excludeSlotId })

    const insertdate = insertDateResult[0]?.insertdate

    if (!insertdate) {
      return
    }

    const [conflictingSlots] = await named_db_con.query(`
      SELECT uuid
      FROM tbl_service_provider_available_slot
      WHERE service_provider_id = :serviceProviderId
      AND business_location_id = :businessLocationId
      AND day = :day
      AND (
        (:date IS NULL AND date IS NULL)
        OR date = :date
      )
      AND id != :excludeSlotId
      AND NOT is_deleted
      AND insertdate < :insertdate
      AND IF(
        :date IS NULL,
        TRUE,
        CONCAT(DATE_FORMAT(:date, '%Y-%m-%d'), ' ', :fromTime)
          BETWEEN CONCAT(DATE_FORMAT(date, '%Y-%m-%d'), ' ', from_time)
          AND CONCAT(DATE_FORMAT(date, '%Y-%m-%d'), ' ', to_time)
        OR CONCAT(DATE_FORMAT(:date, '%Y-%m-%d'), ' ', :toTime)
          BETWEEN CONCAT(DATE_FORMAT(date, '%Y-%m-%d'), ' ', from_time)
          AND CONCAT(DATE_FORMAT(date, '%Y-%m-%d'), ' ', to_time)
      )
    `, {
      serviceProviderId,
      businessLocationId,
      day,
      date,
      fromTime,
      toTime,
      excludeSlotId,
      insertdate,
    })

    await cleanupBreaksBySlotIds(map(conflictingSlots, (slot: any) => slot.uuid).filter(Boolean))

    await named_db_con.query(`
      UPDATE tbl_service_provider_available_slot
      SET is_deleted = 1,
          updated_at = NOW()
      WHERE service_provider_id = :serviceProviderId
      AND business_location_id = :businessLocationId
      AND day = :day
      AND (
        (:date IS NULL AND date IS NULL)
        OR date = :date
      )
      AND id != :excludeSlotId
      AND NOT is_deleted
      AND insertdate < :insertdate
      AND IF(
        :date IS NULL,
        TRUE,
        CONCAT(DATE_FORMAT(:date, '%Y-%m-%d'), ' ', :fromTime)
          BETWEEN CONCAT(DATE_FORMAT(date, '%Y-%m-%d'), ' ', from_time)
          AND CONCAT(DATE_FORMAT(date, '%Y-%m-%d'), ' ', to_time)
        OR CONCAT(DATE_FORMAT(:date, '%Y-%m-%d'), ' ', :toTime)
          BETWEEN CONCAT(DATE_FORMAT(date, '%Y-%m-%d'), ' ', from_time)
          AND CONCAT(DATE_FORMAT(date, '%Y-%m-%d'), ' ', to_time)
      )`, {
        serviceProviderId,
        businessLocationId,
        day,
        date,
        fromTime,
        toTime,
        excludeSlotId,
        insertdate,
      })
  } catch (error: any) {
    logger.error({
      message: 'Failed to cleanup schedule conflicts',
      error,
      meta: {
        serviceProviderId,
        businessLocationId,
        day,
        date,
        fromTime,
        toTime,
        excludeSlotId,
      },
    })
  }
}

export async function getScheduleSlots(
  serviceProviderId: number,
  businessLocationId: number,
  custom: boolean,
  month: string
): Promise<ScheduleSlot[]> {
  const query = `
    SELECT
      id,
      uuid,
      day,
      IF(date IS NOT NULL, DATE_FORMAT(date, '%Y-%m-%d'), NULL) as date,
      from_time as fromTime,
      to_time as toTime
    FROM tbl_service_provider_available_slot
    WHERE service_provider_id = :serviceProviderId
    AND business_location_id = :businessLocationId
    AND NOT is_deleted
    AND IF(
      :custom,
      date IS NOT NULL AND DATE_FORMAT(date, '%Y-%m') = DATE_FORMAT(:month, '%Y-%m'),
      date IS NULL
    )
    ORDER BY
      IF(
        :custom,
        date,
        FIELD(day, 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday')
      ),
      from_time
  `

  try {
    const [rows] = await named_db_con.query(query, {
      serviceProviderId,
      businessLocationId,
      custom,
      month
    })

    const slots = rows as ScheduleSlot[]

    for (const slot of slots) {
      slot.breaks = await getBreaksBySlotId(slot.uuid)
    }

    return slots
  } catch (error) {
    logger.error('Failed to get schedule slots', {
      error,
      serviceProviderId,
      businessLocationId,
      custom,
      month
    })
    return []
  }
}

export async function upsertTimeOffs(timeOffs: TimeOff[]) {
  try {
    if (!timeOffs[0]?.serviceProviderId || !timeOffs[0]?.businessLocationId) {
      return [];
    }

    const timeOffsByDate = reduce(timeOffs, (acc, timeOff) => {
      const date = moment.utc(timeOff.date).format(SIMPLE_DATE_FORMAT);
      const fromTime = moment.utc(`${timeOff.date} ${timeOff.fromTime}`).format('HH:mm');
      const toTime = moment.utc(`${timeOff.date} ${timeOff.toTime}`).format('HH:mm');

      return {
        ...acc,
        [date]: [
          ...(acc[date] || []),
          { fromTime, toTime }
        ]
      };
    }, {} as Record<string, { fromTime: string; toTime: string; }[]>);

    if (!keys(timeOffsByDate).length) {
      return [];
    }

    const timeOffIds = await Promise.all(
      map(entries(timeOffsByDate), async ([date, slots]) => {
        if (!date || !slots) {
          return [];
        }

        const blockId = await upsertServiceProviderBlock({
          service_provider_id: timeOffs[0].serviceProviderId,
          business_location_id: timeOffs[0].businessLocationId,
          date,
          insertdate: moment().format('YYYY-MM-DD HH:mm:ss'),
        });

        if (!blockId) {
          return [];
        }

        const slotIds = await Promise.all(
          map(slots, (slot: { fromTime: string; toTime: string }) =>
            createServiceProviderBlockAvailabilitySlot({
              service_provider_id: timeOffs[0].serviceProviderId,
              business_location_id: timeOffs[0].businessLocationId,
              block_date_id: blockId,
              from_time: slot.fromTime,
              to_time: slot.toTime,
              is_deleted: 0,
              insertdate: moment().format('YYYY-MM-DD HH:mm:ss'),
            })
          )
        );

        return filter(slotIds);
      })
    );

    return filter(timeOffIds.flat());
  } catch (e) {
    logger.error('Failed to upsert time-offs', e);
    throw e;
  }
}

export async function getTimeOffs(serviceProviderId: number, businessLocationId: number, month: string) {
  try {
    const [rows] = await named_db_con.query(`
      SELECT
        ts.id,
        DATE_FORMAT(bd.date, '%Y-%m-%d') AS date,
        ts.from_time AS fromTime,
        ts.to_time AS toTime
      FROM tbl_service_provider_block_date bd
      JOIN tbl_service_provider_block_time_slot ts
        ON bd.id = ts.block_date_id
      WHERE bd.service_provider_id = :serviceProviderId
      AND bd.business_location_id = :businessLocationId
      AND bd.is_deleted = 0
      AND ts.is_deleted = 0
      AND DATE_FORMAT(bd.date, '%Y-%m') = DATE_FORMAT(:month, '%Y-%m')
      ORDER BY bd.date, ts.from_time
    `, {
      serviceProviderId,
      businessLocationId,
      month
    });

    return rows;
  } catch (e) {
    logger.error('Failed to get time-offs', e);
    return [];
  }
}

export async function deleteCustomScheduleSlot(
  slotId: number,
  serviceProviderId: number,
  businessLocationId: number
): Promise<void> {
  const [rows] = await named_db_con.query(`
    SELECT date, uuid
    FROM tbl_service_provider_available_slot
    WHERE id = :slotId
    AND service_provider_id = :serviceProviderId
    AND business_location_id = :businessLocationId
    AND NOT is_deleted
  `, {
    slotId,
    serviceProviderId,
    businessLocationId
  });

  const slot = rows[0];

  if (!slot) {
    return;
  }

  if (!slot.date) {
    throw new Error('Cannot delete recurring schedule. To modify recurring schedule, update the time slots instead.');
  }

  await cleanupBreaksBySlotIds([slot.uuid])

  await named_db_con.query(`
    UPDATE tbl_service_provider_available_slot
    SET is_deleted = 1
    WHERE id = :slotId
    AND service_provider_id = :serviceProviderId
    AND business_location_id = :businessLocationId
  `, {
    slotId,
    serviceProviderId,
    businessLocationId
  });
}

export async function deleteTimeOffSlot(
  slotId: number,
  serviceProviderId: number,
  businessLocationId: number
): Promise<void> {
  await named_db_con.query(`
    UPDATE tbl_service_provider_block_time_slot
    SET is_deleted = 1
    WHERE id = :slotId
    AND service_provider_id = :serviceProviderId
    AND business_location_id = :businessLocationId
  `, {
    slotId,
    serviceProviderId,
    businessLocationId
  });

  const [rows] = await named_db_con.query(`
    SELECT ts.block_date_id
    FROM tbl_service_provider_block_time_slot ts
    WHERE ts.block_date_id = (
      SELECT block_date_id
      FROM tbl_service_provider_block_time_slot
      WHERE id = :slotId
    )
    AND ts.is_deleted = 0
  `, { slotId });

  if (!rows[0]?.block_date_id) {
    await named_db_con.query(`
      UPDATE tbl_service_provider_block_date
      SET is_deleted = 1
      WHERE id = (
        SELECT block_date_id
        FROM tbl_service_provider_block_time_slot
        WHERE id = :slotId
      )
    `, { slotId });
  }
}
