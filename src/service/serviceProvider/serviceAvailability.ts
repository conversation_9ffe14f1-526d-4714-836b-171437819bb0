import { named_db_con } from '@/src/db'
import moment, { Moment } from 'moment-timezone'
import {
  BusySlot,
  Schedule,
  ScheduleBreak,
  getBlackoutDates,
  getBookings,
  getBusySlots,
  getScheduleForDates,
  getBreaksBySlotId,
} from '@/src/service'
import {
  momentToTimeslots,
  logger,
  dateStringInTz,
  SIMPLE_DATE_FORMAT,
  SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS,
} from '@/src/utils'
import { find as geoFind } from 'geo-tz'
import { isEmpty, identity, map, groupBy } from 'lodash'

interface ProviderTimeSlotsParams {
  service_provider_id: number | string
  business_location_id: number | string
  date: string
}

interface ProviderTimeSlot {
  from_time: string
  to_time: string
  day: string
  timezone_code: string | null
  longitude: number
  latitude: number
  booking_buffer_interval_minutes: number
  max_future_booking_window_minutes: number
}

export async function getDatesForBooking(body: any) {
  const schedule = await getScheduleForDates(
    body.service_provider_id,
    body.business_location_id,
    body.date,
    body.end_date ?? body.date,
  )
  const busySlots = groupBy(await getBusySlots(
    body.service_provider_id,
    body.business_location_id,
    body.date,
    body.end_date ?? body.date
  ), (it: BusySlot) => it.date)

  return map(
    schedule,
    (it: Schedule) => {
     const slots = generateTimeSlots(
        it.start_date,
        it.end_date,
        parseInt(body.total_duration || 30),
        it.timezone_code,
        busySlots[it.date],
        true
      )

      return slots.length > 0 ? it.date : null
    }).filter(identity)
}

export async function getSlotsForBooking(body: any) {
  const schedule = (await getScheduleForDates(
    body.service_provider_id,
    body.business_location_id,
    body.date,
    body.date,
  ))[0]

  if (!schedule) {
    return []
  }

  const busySlots = await getBusySlots(
    body.service_provider_id,
    body.business_location_id,
    body.date,
    body.date,
  )

  const breaks = await getBreaksBySlotId(schedule.slot_uuid)

  return momentToTimeslots(generateTimeSlots(
    schedule.start_date,
    schedule.end_date,
    parseInt(body.total_duration || 30),
    schedule.timezone_code,
    busySlots,
    false,
    breaks
  ))
}

export async function getProviderTimeSlot(data: ProviderTimeSlotsParams): Promise<ProviderTimeSlot | null> {
  try {
    const [provider_slots] = await named_db_con.query(`
      SELECT
        from_time,
        to_time,
        day,
        bl.longitude,
        bl.latitude,
        bl.timezone_code,
        bl.booking_buffer_interval_minutes,
        bl.max_future_booking_window_minutes
      FROM tbl_service_provider_available_slot spas
      JOIN tbl_business_location bl on bl.id = spas.business_location_id
      WHERE spas.service_provider_id = :sp
      AND spas.business_location_id = :bl
      AND spas.is_deleted = 0
      AND spas.from_time != ''
      AND spas.to_time != ''
      AND (
        spas.date = :date
        OR (
          spas.date IS NULL
          AND spas.day = DAYNAME(:date)
          AND NOT EXISTS (
            SELECT 1 FROM tbl_service_provider_available_slot s2
            WHERE s2.service_provider_id = spas.service_provider_id
            AND s2.business_location_id = spas.business_location_id
            AND s2.date = :date
            AND s2.is_deleted = 0
          )
        )
      )
      ORDER BY spas.date IS NULL
      LIMIT 1
    `, { sp: data.service_provider_id, bl: data.business_location_id, date: data.date })
    return provider_slots[0]
  } catch (e) {
    logger.error('Failed to retrieve provider slot', e)
    return null
  }
}

function hasBreakConflict(pointer: Moment, duration: number, breaks: ScheduleBreak[], tz: string): boolean {
  if (!breaks?.length) return false

  const slotStart = moment(pointer)
  const slotEnd = moment(pointer).add(duration, 'minutes')

  for (const breakItem of breaks) {
    const breakStart = dateStringInTz(
      `${slotStart.format('YYYY-MM-DD')} ${breakItem.startTime}`,
      'YYYY-MM-DD HH:mm',
      tz
    )
    const breakEnd = dateStringInTz(
      `${slotStart.format('YYYY-MM-DD')} ${breakItem.endTime}`,
      'YYYY-MM-DD HH:mm',
      tz
    )

    if (datesHaveConflict(slotStart, slotEnd, breakStart, breakEnd)) {
      return true
    }
  }

  return false
}

export function generateTimeSlots(
  shiftStart: Moment,
  shiftEnd: Moment,
  slotDuration: number,
  tz: string,
  busySlots: BusySlot[],
  returnEarly: boolean = false,
  breaks: ScheduleBreak[] = []
): Moment[] {
  const busySlotsStack = [...(busySlots ?? [])]
  const timeSlots: Moment[] = []
  const pointer = moment(shiftStart)
  const now = moment().tz(tz)
  const duration = slotDuration || 30

  while (pointer.isBefore(shiftEnd)) {
    if (!pointer.isSame(shiftEnd, 'day')) {
      return timeSlots
    }

    if (pointer.isSameOrBefore(now)) {
      pointer.add(duration, 'minutes')
      continue
    }

    let hasConflict = false
    let conflictIndex = -1

    for (let i = 0; i < busySlotsStack.length; i++) {
      const endPointer = moment(pointer).add(duration, 'minutes')
      hasConflict = datesHaveConflict(pointer, endPointer, busySlotsStack[i].start_date, busySlotsStack[i].end_date)

      if (hasConflict) {
        conflictIndex = busySlotsStack[i].end_date.isSameOrBefore(endPointer) ? i : -1
        break
      }
    }

    if (conflictIndex >= 0) {
      busySlotsStack.splice(conflictIndex, 1)
    }

    if (!hasConflict) {
      hasConflict = hasBreakConflict(pointer, duration, breaks, tz)
    }

    if (!hasConflict) {
      timeSlots.push(moment(pointer))

      if (returnEarly) {
        return timeSlots
      }
    }

    pointer.add(duration, 'minutes')
  }

  return timeSlots
}

export function createTimeSlots(
  targetDate: Moment,
  targetDuration: number,
  providerSlot: ProviderTimeSlot
): Moment[] {
  const now = moment.tz(providerSlot.timezone_code as string)
  const minStartTime = moment(now).add(providerSlot.booking_buffer_interval_minutes, 'minutes')
  const safeFutureBookingWindow = parseInt(String(providerSlot.max_future_booking_window_minutes))
  const maxStartTime = safeFutureBookingWindow ?
    moment(now).add(safeFutureBookingWindow, 'minutes') :
    null
  const fromTime = moment(providerSlot.from_time, 'hh:mm A').format('HH:mm')
  const toTime = moment(providerSlot.to_time, 'hh:mm A').format('HH:mm')
  const slotFromTime = dateStringInTz(
    `${ targetDate.format(SIMPLE_DATE_FORMAT) } ${ fromTime }`,
    SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS,
    providerSlot.timezone_code as string,
  )
  const slotToTime = dateStringInTz(
    `${ targetDate.format(SIMPLE_DATE_FORMAT) } ${ toTime }`,
    SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS,
    providerSlot.timezone_code as string,
  )
  const timeSlots: Moment[] = []
  const pointer = moment(slotFromTime)
  while (pointer < slotToTime) {
    if (!pointer.isSame(slotToTime, 'day')) {
      return timeSlots
    }

    const timeSlot = dateStringInTz(
      pointer.format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS),
      SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS,
      providerSlot.timezone_code as string,
    )
    pointer.add(targetDuration, 'minutes')

    if (timeSlot.isBefore(minStartTime) || (maxStartTime && timeSlot.isAfter(maxStartTime))) {
      continue
    }

    timeSlots.push(timeSlot)
  }

  return timeSlots
}

export function getConflictSlots(timeslots: Moment[], start: Moment, end: Moment, slotDuration: number): Moment[] {
  return map(timeslots, (startSlot: Moment) =>
    datesHaveConflict(
      startSlot,
      moment(startSlot).add(slotDuration, 'minutes'),
      start,
      end
    ) ? startSlot : null
  ).filter(identity)
}

function datesHaveConflict(slotStart: Moment, slotEnd: Moment, start: Moment, end: Moment) {
  if (!slotStart || !slotEnd || !start || !end) {
    return false
  }

  return slotStart.isBetween(start, end) ||
    slotEnd.isBetween(start, end) ||
    start.isBetween(slotStart, slotEnd) ||
    end.isBetween(slotStart, slotEnd) ||
    slotStart.isSame(start) ||
    slotEnd.isSame(end)
}

export async function getAvailableSlots(props: any): Promise<string[]> {
  const providerSlot = await getProviderTimeSlot(props)
  if (!providerSlot) {
    return []
  }

  providerSlot.timezone_code = providerSlot.timezone_code || geoFind(providerSlot.latitude, providerSlot.longitude)[0]
  const targetDate = moment.tz(props.date, providerSlot.timezone_code)
  const targetDuration = parseInt(props.total_duration || 30) || 30

  let timeslots: Moment[] = createTimeSlots(targetDate, targetDuration, providerSlot)

  if (isEmpty(timeslots)) {
    return []
  }

  const [bookings] = ((await getBookings(props)) as any[]) ?? []
  bookings.forEach((item: any) => {
    const startDate = moment(item.date).format(SIMPLE_DATE_FORMAT)
    const bookedStart = dateStringInTz(
      `${ startDate } ${ item.slot_time }`,
      SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS,
      providerSlot.timezone_code as string
    )
    const bookedEnd = moment(bookedStart).add(targetDuration, 'minutes')
    const blockedSlots = getConflictSlots(timeslots, bookedStart, bookedEnd, targetDuration)
    timeslots = timeslots.filter(slot => blockedSlots?.indexOf(slot) < 0)
  })

  const blackouts = await getBlackoutDates(props.service_provider_id, props.business_location_id, props.date, props.date)
  blackouts.forEach((blackout: any) => {
    const blockedSlots = getConflictSlots(
      timeslots,
      dateStringInTz(blackout.start_date, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS, providerSlot.timezone_code as string),
      dateStringInTz(blackout.end_date, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS, providerSlot.timezone_code as string),
      targetDuration,
    )
    timeslots = timeslots.filter(slot => blockedSlots?.indexOf(slot) < 0)
  })

  return momentToTimeslots(timeslots)
}
