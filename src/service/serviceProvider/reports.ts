import { named_db_con } from '@/src/db'
import {
  dateStringInTz,
  logger,
  SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS
} from '@/src/utils'
import momentTz, { Moment } from 'moment-timezone'
import { groupBy, keyBy, keys, map, omit, pick, reduce, first, values, toNumber, isFinite, uniq, forEach } from 'lodash'
import { Global } from '@/src/constants'

async function getClientReport(
  serviceProviderId: number,
  fromDate: string, //expected format YYYY-MM-DD
  toDate: string, //expected format YYYY-MM-DD,
  scope: string
): Promise<any[]> {
  const params = {
    serviceProviderId,
    scope,
    fromDate,
    toDate,
  }

  try {
    const [rows] = await named_db_con.query(`
      WITH RECURSIVE calendar(date) AS (
        SELECT GET_DATE_FORMATTED_BY_SCOPE(:fromDate, :scope) AS date
        UNION
        SELECT GET_DATE_FORMATTED_BY_SCOPE(ADD_DATE_BY_SCOPE(calendar.date, :scope), :scope)
        FROM calendar
        WHERE GET_DATE_FORMATTED_BY_SCOPE(ADD_DATE_BY_SCOPE(calendar.date, :scope), :scope) <= GET_DATE_FORMATTED_BY_SCOPE(:toDate, :scope)
      ), result AS(
        WITH sub_result AS(
          SELECT
            COUNT(b.user_id) user_count,
            b.user_id,
            GET_DATE_FORMATTED_BY_SCOPE(CONCAT(b.date, ' ', b.slot_time), :scope) AS booking_date
          FROM tbl_appointment_booking b
          WHERE b.service_provider_id = :serviceProviderId
          AND b.date BETWEEN :fromDate AND :toDate
          AND NOT b.is_deleted
          AND LOWER(b.booking_status) = 'paid'
          GROUP BY GET_DATE_FORMATTED_BY_SCOPE(CONCAT(b.date, ' ', b.slot_time), :scope), b.user_id
          ORDER BY GET_DATE_FORMATTED_BY_SCOPE(CONCAT(b.date, ' ', b.slot_time), :scope)
        )
        SELECT
          COUNT(user_id) AS user_count,
          SUM(IF(
            EXISTS(
              SELECT id
              FROM tbl_appointment_booking
              WHERE NOT is_deleted
              AND LOWER(booking_status) = 'paid'
              AND user_id = sub_result.user_id
              AND service_provider_id = :serviceProviderId
              AND date BETWEEN (DATE_FORMAT(booking_date, '%Y-%m-%d') - INTERVAL 3 month) AND DATE_FORMAT(booking_date, '%Y-%m-%d')
            ),
            1,
            0
          )) AS returning_user_count,
          SUM(IF(
            NOT EXISTS(
              SELECT id
              FROM tbl_appointment_booking
              WHERE NOT is_deleted
              AND LOWER(booking_status) = 'paid'
              AND user_id = sub_result.user_id
              AND service_provider_id = :serviceProviderId
              AND date < DATE_FORMAT(booking_date, '%Y-%m-%d')
            ),
            1,
            0
          )) AS new_user_count,
          (
            SELECT COUNT(DISTINCT bl.user_id)
            FROM tbl_appointment_booking bl
            LEFT JOIN tbl_appointment_booking br ON bl.user_id = br.user_id
              AND br.service_provider_id = bl.service_provider_id
              AND LOWER(br.booking_status) = LOWER(bl.booking_status)
              AND br.is_deleted = bl.is_deleted
              AND br.date BETWEEN (DATE_FORMAT(booking_date, '%Y-%m-%d') - INTERVAL 3 month) AND DATE_FORMAT(booking_date, '%Y-%m-%d')
            WHERE NOT bl.is_deleted
            AND LOWER(bl.booking_status) = 'paid'
            AND bl.user_id > 0
            AND bl.service_provider_id = :serviceProviderId
            AND bl.date < (DATE_FORMAT(booking_date, '%Y-%m-%d') - INTERVAL 3 month)
            AND br.id IS NULL
          ) AS slipping_user_count,
          booking_date
        FROM sub_result
        GROUP BY booking_date
        ORDER BY booking_date
      )
      SELECT
        c.date AS booking_date,
        IFNULL(r.slipping_user_count, 0) AS slipping_user_count,
        IFNULL(r.user_count, 0) AS user_count,
        IFNULL(r.returning_user_count, 0) AS returning_user_count,
        IFNULL(r.new_user_count, 0) AS new_user_count,
        IFNULL(
          ROUND((r.returning_user_count / IF(r.user_count > 0, r.user_count, 1)) * 100),
          0
        ) AS user_retention_percentage
      FROM calendar c
      LEFT JOIN result r ON r.booking_date = c.date
    `, params)
    return rows as any[]
} catch (e) {
    logger.error(`Failed to get client report for ${ JSON.stringify(params) }`, e)
    return []
  }
}

async function getRevenueReport(
  serviceProviderId: number,
  fromDate: string, //expected format YYYY-MM-DD
  toDate: string, //expected format YYYY-MM-DD,
  scope: string
): Promise<any[]> {
  const params = {
    serviceProviderId,
    scope,
    fromDate,
    toDate,
    serviceImgPrefix: `${ Global.S3_BUCKET_ROOT }${ Global.PRODUCT_IMAGE}`,
    productImgPrefix: `${ Global.S3_BUCKET_ROOT }${ Global.PRODUCT_IMAGE}`,
  }

  try {
    const [rows] = await named_db_con.query(`
      WITH RECURSIVE calendar(date) AS (
        SELECT GET_DATE_FORMATTED_BY_SCOPE(:fromDate, :scope) AS date
        UNION
        SELECT GET_DATE_FORMATTED_BY_SCOPE(ADD_DATE_BY_SCOPE(calendar.date, :scope), :scope)
        FROM calendar
        WHERE GET_DATE_FORMATTED_BY_SCOPE(ADD_DATE_BY_SCOPE(calendar.date, :scope), :scope) <= GET_DATE_FORMATTED_BY_SCOPE(:toDate, :scope)
      ), result AS (
        SELECT
          SUM(IF(
            LOWER(b.booking_status) = 'paid',
            b.total_amount - b.tax,
            0
          )) AS total_revenue,
          SUM(IF(LOWER(b.booking_status) = 'paid', b.tip_amount, 0)) AS tip_total,
          SUM(IF(
            LOWER(b.booking_status) = 'paid',
            b.refund_amount,
            IF(LOWER(b.booking_status) IN ('no show', 'cancelled'), b.total_amount, 0))
          ) AS lost_revenue,
          TRUNCATE(
            AVG(IF(LOWER(b.booking_status) = 'paid', b.total_amount, 0)),
            2
          ) AS average_ticket,
          GET_DATE_FORMATTED_BY_SCOPE(CONCAT(b.date, ' ', b.slot_time), :scope) AS booking_date,
          SUM(IF(
            LOWER(b.booking_status) = 'paid',
            IFNULL(
              (
                SELECT SUM(price)
                FROM tbl_appointment_booking_detail
                WHERE appointment_id = b.id
                AND LOWER(type) = 'service'
                AND NOT is_deleted
              ),
              0
            ),
            0
          )) AS service_total,
          SUM(IF(
            LOWER(b.booking_status) = 'paid',
            IFNULL(
              (
                SELECT SUM(price * quantity)
                FROM product_purchase
                WHERE booking_id = b.id
                AND NOT is_deleted
              ),
              0
            ),
            0
          )) AS new_product_total,
          SUM(IF(
            LOWER(b.booking_status) = 'paid',
            (
              SELECT SUM(price * quantity)
              FROM tbl_appointment_booking_detail
              WHERE appointment_id = b.id
              AND LOWER(type) = 'product'
              AND NOT is_deleted
            ),
            0
          )) AS old_product_total
        FROM tbl_appointment_booking b
        WHERE b.service_provider_id = :serviceProviderId
        AND b.date BETWEEN :fromDate AND :toDate
        GROUP BY GET_DATE_FORMATTED_BY_SCOPE(CONCAT(b.date, ' ', b.slot_time), :scope)
        ORDER BY GET_DATE_FORMATTED_BY_SCOPE(CONCAT(b.date, ' ', b.slot_time), :scope)
      )
      SELECT
        c.date AS booking_date,
        IFNULL(total_revenue, 0) AS total_revenue,
        IFNULL(tip_total, 0) AS tip_total,
        IFNULL(lost_revenue, 0) AS lost_revenue,
        IFNULL(average_ticket, 0) AS average_ticket,
        IFNULL(
          (
            SELECT JSON_OBJECT(
              'visits', visits,
              'id', id,
              'name', service_name,
              'image_url', CONCAT(:serviceImgPrefix, '', (
                SELECT s.service_image
                FROM tbl_service s
                WHERE s.id = b.service_id
              ))
             ) AS json
            FROM (
              SELECT COUNT(DISTINCT id.appointment_id) AS visits, id.service_id
              FROM tbl_appointment_booking_detail id
              JOIN tbl_appointment_booking ib ON id.appointment_id = ib.id
              WHERE GET_DATE_FORMATTED_BY_SCOPE(CONCAT(ib.date, ' ', ib.slot_time), :scope) = r.booking_date
              AND LOWER(id.type) = 'service'
              AND LOWER(ib.booking_status) = 'paid'
              GROUP BY id.service_id
              ORDER BY visits DESC
              LIMIT 1
            ) b
            JOIN tbl_service s ON s.id = b.service_id
          ),
          '{}'
        ) AS top_service_json,
        IFNULL(service_total, 0) AS service_total,
        IFNULL(
          (
            SELECT JSON_OBJECT(
              'quantity', sum,
              'id', IFNULL(p.id, tp.id),
              'name', IFNULL(p.name, tp.product_name),
              'label', table_ref,
              'image_url', CONCAT(:productImgPrefix, '', IFNULL(
                (
                  SELECT g.image
                  FROM product_gallery g
                  WHERE g.product_id = p.id
                  ORDER BY g.id DESC
                  LIMIT 1
                ),
                (
                  SELECT product_image
                  FROM tbl_product
                  WHERE id = p.id
                )
              ))) AS json
            FROM (
              SELECT
                SUM(IFNULL(p.quantity, d.quantity)) AS sum,
                IFNULL(p.product_id, d.product_id) AS product_id,
                IF(p.id IS NOT NULL, 'new', 'old') AS table_ref
              FROM tbl_appointment_booking ab
              LEFT JOIN product_purchase p ON p.booking_id = ab.id
                AND NOT p.is_deleted
              LEFT JOIN tbl_appointment_booking_detail d ON d.appointment_id = ab.id
              AND LOWER(d.type) = 'product'
              AND NOT d.is_deleted
              WHERE GET_DATE_FORMATTED_BY_SCOPE(CONCAT(ab.date, ' ', ab.slot_time), :scope) = r.booking_date
              AND (
                p.id IS NOT NULL
                OR d.id IS NOT NULL
              )
              AND LOWER(ab.booking_status) = 'paid'
              GROUP BY IFNULL(p.product_id, d.product_id), table_ref
              ORDER BY sum DESC
              LIMIT 1
            ) b
            LEFT JOIN product p ON p.id = b.product_id
              AND b.table_ref = 'new'
            LEFT JOIN tbl_product tp ON tp.id = b.product_id
              AND b.table_ref = 'old'
          ),
          '{}'
        ) AS top_product_json,
        IFNULL(new_product_total, 0) + IFNULL(old_product_total, 0) AS product_total,
        IFNULL(ROUND(((IFNULL(new_product_total, 0) + IFNULL(old_product_total, 0)) / IF(total_revenue != 0, total_revenue, 1)) * 100), 0) AS retail_percentage
      FROM calendar c
      LEFT JOIN result r ON c.date = r.booking_date
    `, params)
    return map(rows, (it: any) => ({
      ...omit(it, ['top_service_json', 'top_product_json']),
      top_product: JSON.parse(it.top_product_json),
      top_service: JSON.parse(it.top_service_json),
    }))
  } catch (e) {
    logger.error(`Failed to get revenue report for ${ JSON.stringify(params) }`, e)
    return []
  }
}

async function getBookingStatusCounts(
  serviceProviderId: number,
  fromDate: string, //expected format YYYY-MM-DD
  toDate: string, //expected format YYYY-MM-DD,
  scope: string
): Promise<any[]> {
  const params = {
    serviceProviderId,
    scope,
    fromDate,
    toDate,
    userImgPrefix: `${ Global.S3_BUCKET_ROOT }${ Global.USER_IMAGE }`,
  }

  try {
    const [rows] = await named_db_con.query(`
      WITH RECURSIVE calendar(date) AS (
        SELECT GET_DATE_FORMATTED_BY_SCOPE(:fromDate, :scope) AS date
        UNION
        SELECT GET_DATE_FORMATTED_BY_SCOPE(ADD_DATE_BY_SCOPE(calendar.date, :scope), :scope)
        FROM calendar
        WHERE GET_DATE_FORMATTED_BY_SCOPE(ADD_DATE_BY_SCOPE(calendar.date, :scope), :scope) <= GET_DATE_FORMATTED_BY_SCOPE(:toDate, :scope)
      ), sub_result AS (
        SELECT
          COUNT(booking_status) AS booking_status_count,
          LOWER(booking_status) AS booking_status,
          GET_DATE_FORMATTED_BY_SCOPE(CONCAT(date, ' ', slot_time), :scope) AS booking_date,
          IFNULL(
            (
              SELECT JSON_OBJECT(
                'visit_count', visits,
                'id', id,
                'first_name', first_name,
                'last_name', last_name,
                'image_url', CONCAT(:userImgPrefix, '', profile_image)
              ) AS client_json
              FROM (
                SELECT COUNT(DISTINCT id) AS visits, user_id
                FROM tbl_appointment_booking
                WHERE service_provider_id = :serviceProviderId
                AND LOWER(booking_status) = 'paid'
                AND GET_DATE_FORMATTED_BY_SCOPE(CONCAT(date, ' ', slot_time), :scope) = booking_date
                GROUP BY user_id
                ORDER BY visits DESC
                LIMIT 1
              ) b
              JOIN tbl_user u ON u.id = b.user_id
            ),
            '{}'
          ) AS top_client_json
        FROM tbl_appointment_booking
        WHERE date BETWEEN :fromDate AND :toDate
        AND service_provider_id = :serviceProviderId
        AND NOT is_deleted
        GROUP BY GET_DATE_FORMATTED_BY_SCOPE(CONCAT(date, ' ', slot_time), :scope), LOWER(booking_status)
        ORDER BY GET_DATE_FORMATTED_BY_SCOPE(CONCAT(date, ' ', slot_time), :scope), LOWER(booking_status)
      ) SELECT
        c.date AS booking_date,
        IFNULL(booking_status_count, 0) AS booking_status_count,
        booking_status,
        IFNULL(top_client_json, '{}') AS top_client_json
      FROM calendar c
      LEFT JOIN sub_result r ON c.date = r.booking_date
    `, params)
    return map(rows, (it: any) => ({
      ...omit(it, 'top_client_json'),
      top_client: JSON.parse(it.top_client_json),
    }))
  } catch (e) {
    logger.error(`Failed to get status counts for ${ JSON.stringify(params) }`, e)
    return []
  }
}

async function getBookingDurationsAndCounts(
  serviceProviderId: number,
  fromDate: string, //expected format YYYY-MM-DD
  toDate: string, //expected format YYYY-MM-DD,
  scope: string
): Promise<any[]> {
  const params = {
    serviceProviderId,
    scope,
    fromDate,
    toDate,
  }

  try {
    const [rows] = await named_db_con.query(`
      WITH RECURSIVE calendar(date) AS (
        SELECT GET_DATE_FORMATTED_BY_SCOPE(:fromDate, :scope) AS date
        UNION
        SELECT GET_DATE_FORMATTED_BY_SCOPE(ADD_DATE_BY_SCOPE(calendar.date, :scope), :scope)
        FROM calendar
        WHERE GET_DATE_FORMATTED_BY_SCOPE(ADD_DATE_BY_SCOPE(calendar.date, :scope), :scope) <= GET_DATE_FORMATTED_BY_SCOPE(:toDate, :scope)
      ), sub_result AS (
        SELECT
          SUM(total_duration) AS total_duration,
          SUM(additional_duration) AS additional_duration,
          COUNT(id) AS bookings_count,
          GET_DATE_FORMATTED_BY_SCOPE(CONCAT(date, ' ', slot_time), :scope) AS booking_date
        FROM tbl_appointment_booking
        WHERE date BETWEEN :fromDate AND :toDate
        AND service_provider_id = :serviceProviderId
        AND NOT is_deleted
        GROUP BY GET_DATE_FORMATTED_BY_SCOPE(CONCAT(date, ' ', slot_time), :scope)
        ORDER BY GET_DATE_FORMATTED_BY_SCOPE(CONCAT(date, ' ', slot_time), :scope)
      ) SELECT
        c.date AS booking_date,
        IFNULL(total_duration, 0) AS total_duration,
        IFNULL(additional_duration, 0) AS additional_duration,
        IFNULL(bookings_count, 0) AS bookings_count
      FROM calendar c
      LEFT JOIN sub_result r ON r.booking_date = c.date
    `, params)
    return rows as any[]
  } catch (e) {
    logger.error(`Failed to get booking counts/durations for ${ JSON.stringify(params) }`, e)
    return []
  }
}

export async function generateBookingsReport(body: any): Promise<BookingReportRecord[]> {
  const scope = body.scope ?? getScopeByDates(body.startDate, body.endDate, body.timezone)
  const breakdown = convertScopeToBreakdown(scope)
  const timeFrame = getStartEndDateAsMoment(scope, body.startDate, body.endDate, body.timezone)
  const [statusCounts, bookingCounts] = await Promise.all([
    getBookingStatusCounts(body.serviceProviderId, timeFrame.startDate.format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS), timeFrame.endDate.format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS), breakdown),
    getBookingDurationsAndCounts(body.serviceProviderId, timeFrame.startDate.format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS), timeFrame.endDate.format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS), breakdown)
  ])
  const statusMap = groupBy(statusCounts, 'booking_date')
  const durationCounterMap = keyBy(bookingCounts, 'booking_date')
  const results = values(reduce(keys(statusMap), (agg: any, key: string) => ({
    ...agg,
    [key]: {
      scope_point: key,
      top_client: first(statusMap[key])?.top_client || null,
      statuses: reduce(
        statusMap[key],
        (agg: any, it: any) => it.booking_status ?
          {
            ...agg,
            [it.booking_status]: it.booking_status_count,
          } :
          agg,
        {},
      ),
      ...pick(durationCounterMap[key], ['bookings_count', 'total_duration', 'additional_duration'])
    },
  }), {}))
    .sort((it: any) => it.scope_point)
    .map((it: any) => ({ ...it, scope_point: formatDateForBreakdown(it.scope_point, breakdown, body.timezone) }))

  for (let i = 1; i < results.length; i++) {
    const currentTotal = toSafeNumber(results[i].bookings_count)
    const prevTotal = toSafeNumber(results[i - 1].bookings_count)

    results[i].bookings_count_delta_percentage = computeDeltaPercentage(currentTotal, prevTotal)

    const statusKeys = uniq([...keys(results[i].statuses), ...keys(results[i - 1].statuses)])
      .filter((it: string) => !it.endsWith('_delta_percentage'))
    forEach(statusKeys, (key: any) => {
      const currentCount = toSafeNumber(results[i].statuses[key])
      const prevCount = toSafeNumber(results[i - 1].statuses[key])

      results[i].statuses[`${ key }_delta_percentage`] = computeDeltaPercentage(currentCount, prevCount)
    })
  }

  return results
}

export async function generateRevenueReport(body: any): Promise<RevenueReportRecord[]> {
  const scope = body.scope ?? getScopeByDates(body.startDate, body.endDate, body.timezone)
  const breakdown = convertScopeToBreakdown(scope)
  const timeFrame = getStartEndDateAsMoment(scope, body.startDate, body.endDate, body.timezone)
  const result = await getRevenueReport(body.serviceProviderId, timeFrame.startDate.format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS), timeFrame.endDate.format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS), breakdown)
  const results = values(keyBy(result.map((it: any) => ({ ...it, scope_point: it.booking_date })), 'scope_point'))
    .sort((it: any) => it.scope_point)
    .map((it: any) => ({ ...it, scope_point: formatDateForBreakdown(it.scope_point, breakdown, body.timezone) }))

  for (let i = 1; i < results.length; i++) {
    const currentTotal = toSafeNumber(results[i].total_revenue)
    const currentServiceTotal = toSafeNumber(results[i].service_total)
    const currentProductTotal = toSafeNumber(results[i].product_total)
    const currentTipTotal = toSafeNumber(results[i].tip_total)
    const currentLost = toSafeNumber(results[i].lost_revenue)
    const currentAverageTicket = toSafeNumber(results[i].average_ticket)
    const currentRetailPercentage = toSafeNumber(results[i].retail_percentage)

    const prevTotal = toSafeNumber(results[i - 1].total_revenue)
    const prevServiceTotal = toSafeNumber(results[i - 1].service_total)
    const prevProductTotal = toSafeNumber(results[i - 1].product_total)
    const prevTipTotal = toSafeNumber(results[i - 1].tip_total)
    const prevLost = toSafeNumber(results[i - 1].lost_revenue)
    const prevAverageTicket = toSafeNumber(results[i - 1].average_ticket)
    const prevRetailPercentage = toSafeNumber(results[i - 1].retail_percentage)

    results[i].total_revenue_delta_percentage = computeDeltaPercentage(currentTotal, prevTotal)
    results[i].service_total_delta_percentage = computeDeltaPercentage(currentServiceTotal, prevServiceTotal)
    results[i].product_total_delta_percentage = computeDeltaPercentage(currentProductTotal, prevProductTotal)
    results[i].tip_total_delta_percentage = computeDeltaPercentage(currentTipTotal, prevTipTotal)
    results[i].lost_revenue_delta_percentage = computeDeltaPercentage(currentLost, prevLost)
    results[i].average_ticket_delta_percentage = computeDeltaPercentage(currentAverageTicket, prevAverageTicket)
    results[i].retail_percentage_delta_percentage = computeDeltaPercentage(currentRetailPercentage, prevRetailPercentage)
  }

  return results
}

export interface ClientReportRecord {
  booking_date: string
  slipping_user_count: number
  user_count: number
  returning_user_count: number
  new_user_count: number
  user_retention_percentage: number
  scope_point: string
  slipping_user_delta_percentage: number
  returning_user_delta_percentage: number
  new_user_delta_percentage: number
  user_retention_delta_percentage: number
}

export interface RevenueReportRecord {
  booking_date: string
  total_revenue: number
  tip_total: number
  lost_revenue: number
  average_ticket: number
  service_total: number
  product_total: number
  retail_percentage: number
  top_product: {
    id: number
    name: string
    label: string
    quantity: number
    image_url: string | null
  }
  top_service: {
    id: number
    name: string
    visits: number
    image_url: string | null
  }
  scope_point: string
  total_revenue_delta_percentage: number
  service_total_delta_percentage: number
  product_total_delta_percentage: number
  tip_total_delta_percentage: number
  lost_revenue_delta_percentage: number
  average_ticket_delta_percentage: number
  retail_percentage_delta_percentage: number
}

export interface BookingReportRecord {
  scope_point: string
  top_client: {
    id: number
    last_name: string
    first_name: string
    visit_count: number
    image_url: string | null
  }
  statuses: Map<string, number>
  bookings_count: number
  total_duration: number
  additional_duration: number
  bookings_count_delta_percentage: number
}

export async function generateClientReport(body: any): Promise<ClientReportRecord[]> {
  const scope = body.scope ?? getScopeByDates(body.startDate, body.endDate, body.timezone)
  const breakdown = convertScopeToBreakdown(scope)
  const timeFrame = getStartEndDateAsMoment(scope, body.startDate, body.endDate, body.timezone)
  const result = await getClientReport(body.serviceProviderId, timeFrame.startDate.format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS), timeFrame.endDate.format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS), breakdown)
  const results = values(keyBy(result.map((it: any) => ({ ...it, scope_point: it.booking_date })), 'scope_point'))
    .sort((it: any) => it.scope_point)
    .map((it: any) => ({ ...it, scope_point: formatDateForBreakdown(it.scope_point, breakdown, body.timezone) }))

  for (let i = 1; i < results.length; i++) {
    const currentSlipping = toSafeNumber(results[i].slipping_user_count)
    const currentReturning = toSafeNumber(results[i].returning_user_count)
    const currentNew = toSafeNumber(results[i].new_user_count)
    const currentRetention = toSafeNumber(results[i].user_retention_percentage)

    const prevSlipping = toSafeNumber(results[i - 1].slipping_user_count)
    const prevReturning = toSafeNumber(results[i - 1].returning_user_count)
    const prevNew = toSafeNumber(results[i - 1].new_user_count)
    const prevRetention = toSafeNumber(results[i - 1].user_retention_percentage)

    results[i].slipping_user_delta_percentage = computeDeltaPercentage(currentSlipping, prevSlipping)
    results[i].returning_user_delta_percentage = computeDeltaPercentage(currentReturning, prevReturning)
    results[i].new_user_delta_percentage = computeDeltaPercentage(currentNew, prevNew)
    results[i].user_retention_delta_percentage = computeDeltaPercentage(currentRetention, prevRetention)
  }

  return results
}

function toSafeNumber(value: any) {
  const result = toNumber(value)
  return isFinite(result) ? result : 0
}

function computeDeltaPercentage(value1: number, value2: number) {
  return Number(((((value1 || 0) - (value2 || 0)) / (value1 || 1)) * 100).toFixed(1))
}

function getScopeByDates(startDate: string, endDate: string, timeZone: string = 'America/Denver'): REPORT_DATE_SCOPE {
  if (!startDate || !endDate) {
    return REPORT_DATE_SCOPE.TODAY
  }

  const safeStartDate = dateStringInTz(startDate, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS, timeZone)
  const safeEndDate = dateStringInTz(endDate, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS, timeZone)

  const monthsDiff = Math.abs(safeStartDate.diff(safeEndDate, 'months'))
  if (monthsDiff > 24) {
    return REPORT_DATE_SCOPE.MULTI_YEAR
  } else if (monthsDiff >= 2 && monthsDiff <= 24) {
    return REPORT_DATE_SCOPE.YEAR
  } else if (safeStartDate.isSame(safeEndDate, 'date')) {
    return REPORT_DATE_SCOPE.TODAY
  } else {
    return REPORT_DATE_SCOPE.MONTH
  }
}

function getStartEndDateAsMoment(scope: string, startDate: string | null = null, endDate: string | null = null, timeZone: string = 'America/Denver') {
  let result: { startDate: Moment, endDate: Moment }
  if (startDate && endDate) {
    const safeStartDate = dateStringInTz(startDate, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS, timeZone)
    const safeEndDate = dateStringInTz(endDate, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS, timeZone)
    result = {
      startDate: (safeEndDate.isBefore(safeStartDate) ? momentTz(safeEndDate) : safeStartDate).startOf('day'),
      endDate: (safeEndDate.isBefore(safeStartDate) ? momentTz(safeStartDate) : safeEndDate).endOf('day'),
    }

    validateDates(result)
    return result
  }

  switch (scope?.toLowerCase()) {
    case REPORT_DATE_SCOPE.TODAY:
      result = {
        startDate: momentTz.tz(timeZone).startOf('day'),
        endDate: momentTz.tz(timeZone).endOf('day'),
      }
      break
    case REPORT_DATE_SCOPE.WEEK:
      result = {
        startDate: momentTz.tz(timeZone).startOf('day').subtract(6, 'days'),
        endDate: momentTz.tz(timeZone).endOf('day'),
      }
      break
    case REPORT_DATE_SCOPE.MONTH:
      result = {
        startDate: momentTz.tz(timeZone).startOf('day').subtract(1, 'month'),
        endDate: momentTz.tz(timeZone).endOf('day'),
      }
      break
    case REPORT_DATE_SCOPE.YEAR:
      result = {
        startDate: momentTz.tz(timeZone).startOf('month').subtract(12, 'months'),
        endDate: momentTz.tz(timeZone).endOf('month'),
      }
      break
    case REPORT_DATE_SCOPE.MULTI_YEAR:
      result = {
        startDate: momentTz.tz(timeZone).startOf('year').subtract(5, 'years'),
        endDate: momentTz.tz(timeZone).endOf('year'),
      }
      break
    default: throw new Error('Unknown report scope')
  }

  validateDates(result)
  return result
}

function validateDates(data: { startDate: Moment, endDate: Moment }) {
  if (!data.startDate.isValid()) {
    throw new Error('Start date is not valid')
  }

  if (!data.endDate.isValid()) {
    throw new Error('End date is not valid')
  }
}

function formatDateForBreakdown(date: string, breakdown: REPORT_BREAKDOWN, timeZone: string = 'America/Denver') {
  switch (breakdown) {
    case REPORT_BREAKDOWN.BY_HOUR: return dateStringInTz(date, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS, timeZone).format('ha')
    case REPORT_BREAKDOWN.BY_DAY: return dateStringInTz(date, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS, timeZone).format('MMM D')
    case REPORT_BREAKDOWN.BY_MONTH: return dateStringInTz(date, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS, timeZone).format('MMM')
    case REPORT_BREAKDOWN.BY_YEAR: return dateStringInTz(date, SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS, timeZone).format('YYYY')
    default: throw new Error('Unknown report scope')
  }
}

function convertScopeToBreakdown(scope: REPORT_DATE_SCOPE): REPORT_BREAKDOWN {
  switch (scope) {
    case REPORT_DATE_SCOPE.TODAY: return REPORT_BREAKDOWN.BY_HOUR
    case REPORT_DATE_SCOPE.WEEK:
    case REPORT_DATE_SCOPE.MONTH: return REPORT_BREAKDOWN.BY_DAY
    case REPORT_DATE_SCOPE.YEAR: return REPORT_BREAKDOWN.BY_MONTH
    case REPORT_DATE_SCOPE.MULTI_YEAR: return REPORT_BREAKDOWN.BY_YEAR
    default: return REPORT_BREAKDOWN.BY_DAY
  }
}

export enum REPORT_DATE_SCOPE {
  TODAY = 'today',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
  MULTI_YEAR = 'multi-year',
}

enum REPORT_BREAKDOWN {
  BY_HOUR = 'by_hour',
  BY_DAY = 'by_day',
  BY_MONTH = 'by_month',
  BY_YEAR = 'by_year',
}
