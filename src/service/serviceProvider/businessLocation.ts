import { named_db_con } from '@/src/db'
import { ResultSetHeader } from 'mysql2'
import { logger } from '@/src/utils/logger'

export async function getBusinessLocationById(
  serviceProviderId: number,
  businessLocationId: number
): Promise<any> {
  try {
    const [rows] = await named_db_con.query(`
      SELECT *
      FROM tbl_business_location
      WHERE id = :businessLocationId
      AND service_provider_id = :serviceProviderId
      AND is_deleted = 0
    `, {
      businessLocationId,
      serviceProviderId
    });

    return rows[0];
  } catch (error) {
    logger.error({
      message: 'Failed to get business location from database',
      error,
      meta: { serviceProviderId, businessLocationId },
    });
    return null;
  }
}

export async function createBusinessLocation(data: {
  service_provider_id: number
  name: string
  latitude: string
  longitude: string
  country_name: string
  country_code: string
  state_name: string
  city_name: string
  postal_code: string
  timezone_code: string
  product_tax_percent: number
  service_tax_percent: number
  time_slot_duration_minutes: number
  booking_buffer_interval_minutes: number
  max_future_booking_window_minutes: number
  status: string
}): Promise<number> {
  try {
    const [result] = await named_db_con.query(`
      INSERT INTO tbl_business_location (
        service_provider_id,
        name,
        latitude,
        longitude,
        country_name,
        country_code,
        state_name,
        city_name,
        postal_code,
        timezone_code,
        product_tax_percent,
        service_tax_percent,
        time_slot_duration_minutes,
        booking_buffer_interval_minutes,
        max_future_booking_window_minutes,
        status
      ) VALUES (
        :service_provider_id,
        :name,
        :latitude,
        :longitude,
        :country_name,
        :country_code,
        :state_name,
        :city_name,
        :postal_code,
        :timezone_code,
        :product_tax_percent,
        :service_tax_percent,
        :time_slot_duration_minutes,
        :booking_buffer_interval_minutes,
        :max_future_booking_window_minutes,
        :status)
    `, data);

    return (result as ResultSetHeader).insertId;
  } catch (error) {
    logger.error({
      message: 'Failed to create business location in database',
      error,
      meta: { serviceProviderId: data.service_provider_id, locationName: data.name },
    });
    throw new Error('Failed to create the business location');
  }
}

export async function updateBusinessLocation(data: {
  id: number
  name: string
  latitude: string
  longitude: string
  country_name: string
  country_code: string
  state_name: string
  city_name: string
  postal_code: string
  timezone_code: string
  product_tax_percent: number
  service_tax_percent: number
  time_slot_duration_minutes: number
  booking_buffer_interval_minutes: number
  max_future_booking_window_minutes: number
}) {
  try {
    await named_db_con.query(`
      UPDATE tbl_business_location
        SET name = :name,
            latitude = :latitude,
            longitude = :longitude,
            country_name = :country_name,
            country_code = :country_code,
            state_name = :state_name,
            city_name = :city_name,
            postal_code = :postal_code,
            timezone_code = :timezone_code,
            product_tax_percent = :product_tax_percent,
            service_tax_percent = :service_tax_percent,
            time_slot_duration_minutes = :time_slot_duration_minutes,
            booking_buffer_interval_minutes = :booking_buffer_interval_minutes,
            max_future_booking_window_minutes = :max_future_booking_window_minutes
      WHERE id = :id
    `, data)
  } catch (error) {
    logger.error({
      message: 'Failed to update business location',
      error,
      meta: { ...data },
    })
    throw new Error(`Failed to update the business location ${ data.id }`)
  }
}

export async function deleteBusinessLocation(
  serviceProviderId: number,
  businessLocationId: number
): Promise<void> {
  try {
    await named_db_con.query(`
      UPDATE tbl_business_location
      SET is_deleted = 1
      WHERE id = :businessLocationId
      AND service_provider_id = :serviceProviderId
    `, {
      businessLocationId,
      serviceProviderId
    });
  } catch (error) {
    logger.error({
      message: 'Failed to delete business location',
      error,
      meta: { serviceProviderId, businessLocationId },
    });
  }
}
