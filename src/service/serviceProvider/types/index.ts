export type BankDetails = {
  service_provider_id: string
  bank_image: string
  bank_name: string
  account_holder_name: string
  account_number: string
  routing_number: string
  SSN: string
  tos_ip: string
  tax_number?: string | number
}

export type ShallowServiceProvider = {
  id: number
  first_name: string
  last_name: string
  email: string
  country_code: string
  phone: string
  dob: string
  merchant_account_id: string
  stripe_bank_id: string
  profile_image: string
  merchant_fees: string
  app_fees: string
  merchant_fees_cents: string
  token: string
  device_token: string
  device_type: string
}

export type BusinessLocation = {
  id: number
  name: string
  service_provider_id: number
  latitude: string
  longitude: string
  country_name: string
  country_code: string
  state_name: string
  city_name: string
  postal_code: string
  status: 'Active' | 'Inactive'
  is_deleted: boolean
  insertdate: Date
  timezone_code?: string
}

export type ProviderWithLocation = ShallowServiceProvider & {
  business_locations: BusinessLocation[]
}

export type CreateAccountLink = {
  accountId: string
  refreshUrl: string
}

export type StripeAccountLinkReqBody = {
  accountId: string
  returnUrl: string
}

export type IServiceProviderAccData = {
  name: string
  payoutsStatus: boolean | number
  stripeVerification: boolean | number
  dateOfBirth: string
  ssnProvided: boolean | number
}

export interface IServiceBookingDetails {
  customerName: string
  startTime: string
  endTime: string
  totalDuration: number
  bookingStatus: string
  serviceProviderId: string
  businessLocationId: string
  totalAmount: number
  clientId: string
  userId: string
  timezone: string
  locationId: string
  newClient: boolean | number
}

export interface ServiceProviderMetadata {
  id: number
  rating: number
  reviewCount: number
}
