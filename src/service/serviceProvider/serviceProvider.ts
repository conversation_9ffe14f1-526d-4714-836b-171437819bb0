import { db_conn, named_db_con } from '@/src/db'
import { logger, utcStringToTz } from '@/src/utils'
import { Global } from '@/src/constants'
import {
  createAccount,
  encryptString,
  generateUniqCode,
  getHomeScreenAppointmentsForServiceProvider,
  getOTPTemplate,
  getSetting,
  getUnreadUserNotificationCount,
  removeTwilioBindings,
  sendEmail,
  sendSMS,
  updateUserDeviceInfoForUser,
  upsertUserDeviceInfo,
  USER_LOGIN_STATUS,
  USER_OTP_STATUS,
  USER_STATUS,
  USER_TYPE,
} from '@/src/service'
import datetime from 'node-datetime'
import tokenGenerator from 'rand-token'
import { ResultSetHeader, RowDataPacket } from 'mysql2'
import { first, map, omit, reduce } from 'lodash'
import moment from 'moment-timezone'
import { find as geoFind } from 'geo-tz'
import Stripe from 'stripe'
import {
  BusinessLocation,
  IServiceBookingDetails,
  IServiceProviderAccData,
  ProviderWithLocation, ServiceProviderMetadata,
  ShallowServiceProvider,
} from './types'
import { StripeCard } from '@/src/helper/types/stripe/stripe-card'
import { StripeBankAccount } from '@/src/helper/types/stripe/stripe-bank-account'
import { generateOTP } from '@/src/utils/otp'
import { AppError } from '@/src/helper/errors/app-error'

export interface IServiceProvider {
  id: number
  unique_id: string
  twilio_user_sid: string
  first_name: string
  last_name: string
  email: string
  country_code: string
  phone: string
  password: string
  profile_image: string
  account_type: string
  bio: string
  tax: number
  experience: number
  professional_license: string
  state_issue_id: string
  signed_lease: string
  is_complete_profile: number
  gender: string
  otp: string
  otp_verify: string
  email_verify: string
  forgot_otp_verify: string
  forgot_otp: string
  forgotpwd_datetime: string
  dob: string
  social_id: string
  login_type: string
  merchant_account_id: string
  stripe_bank_id: string
  bank_name: string
  account_number: string
  account_holder_name: string
  SSN: string
  rounting_number: string
  bank_image: string
  latitude: string
  longitude: string
  timezone: string
  timezone_diff: string
  chat_notify: string
  incomingcall_notify: string
  newappointment_notify: string
  newreview_notify: string
  beforeshitreminder_notify: string
  beforeshitreminder_time: string
  beforeappointmentreminder_notify: string
  beforeappointmentreminder_time: string
  customize_cancellation_charges: string
  cancellationwithin_hours: number
  cancellation_charges: number
  autocancellation_charges: number
  tip_amount1: number
  tip_amount2: number
  tip_amount3: number
  merchant_fees: number
  merchant_fees_cents: number
  app_fees: number
  booth_merchant_fees: number
  booth_merchant_fees_cents: number
  booth_app_fees: number
  app_version: string
  login_status: string
  status: string
  is_recommended: string
  is_deleted: number
  last_login: string
  updatetime: string
  insertdate: string
  stripe_verified: boolean
  payouts_enabled: boolean
  ssn_provided: boolean
  stripe_account_id: string
}

export interface IAddBusinessLocation {
  service_provider_id: string | number
  name: string
  latitude: number
  longitude: number
  insertdate: string
  country_name?: string
  country_code?: string
  state_name?: string
  city_name?: string
  postal_code?: number
  status?: string
  is_deleted?: number
}

export async function getServiceProviderUUID(id: number): Promise<string | null> {
  try {
    const [rows] = id ? (await named_db_con.query(`
      SELECT uuid
      FROM tbl_service_provider
      WHERE id = :id
      AND NOT is_deleted
    `, { id })) : [[]]
    return rows[0]?.uuid ?? null
  } catch (error) {
    logger.error({ message: `Failed to retrieve service provider UUID`, error, meta: { id } })
    return null
  }
}

export async function updateServiceProviderPassword(id: number, password: string): Promise<void> {
  if (!password?.trim()) {
    throw new Error('Cannot update password to an empty value')
  }

  await updateServiceProvider(id, { password: encryptString(password.trim()) })
}

export async function getServiceProviderForBookingDetail(
  serviceProviderId: number,
  userId: number
) {
  try {
    const [rows] = await named_db_con.query(
      `
      SELECT
        s.*,
        s.id AS service_provider_id,
        CONCAT('${Global.S3_BUCKET_ROOT}${Global.SP_IMAGE}', s.profile_image) AS profile_image,
        IFNULL(
          (
            SELECT b.status
            FROM tbl_bookmark b
            WHERE b.service_provider_id = s.id
            AND b.user_id = :userId
          ),
          '0'
        ) AS bookmark_status,
        GET_SERVICE_PROVIDER_RATING(s.id, NULL) AS ratting,
        GET_SERVICE_PROVIDER_REVIEW_COUNT(s.id, NULL) AS review,
        CONCAT('${Global.BASE_URL_WITHOUT_API}home/share-provider/', s.id) AS share_url
      FROM tbl_service_provider s
      WHERE s.id = :serviceProviderId
      AND s.is_deleted = 0
    `,
      { serviceProviderId, userId }
    )

    return rows[0] ?? {}
  } catch (e) {
    logger.error(
      `Failed to retrieve service provider ${serviceProviderId} details for a booking`,
      e
    )
    return {}
  }
}

export async function getServiceProviderTargetForHomeScreen(id: number) {
  try {
    const [rows] = id
      ? await named_db_con.query(
          `
      SELECT
        IF(
          show_home_target = 'Yearly',
          yearly_amount,
          IF(
            show_home_target = 'Monthly',
            monthly_amount,
            IF(
              show_home_target = 'Weekly',
              weekly_amount,
              hourly_amount
            )
          )
        ) AS amount,
        spt.show_home_target,
        spt.service_provider_id
      FROM tbl_service_provider_target spt
      JOIN tbl_service_provider sp ON sp.id = spt.service_provider_id
        AND sp.id = :id
        AND sp.is_deleted = 0
        AND LOWER(sp.status) = :status
      WHERE spt.is_deleted = 0
  `,
          { id, status: USER_STATUS.ACTIVE.toLowerCase() }
        )
      : [[{}]]
    return rows[0] ?? {}
  } catch (e) {
    logger.error(
      `Failed to retrieve a target for the service provider ${id}`,
      e
    )
    return null
  }
}

export async function findClosestServiceProviderPage(data: any) {
  const page = data.page - 1

  let orderBy = ' ORDER BY tsp.id DESC'
  if (data.alphabetical) {
    orderBy = `ORDER BY CONCAT(tsp.first_name, tsp.last_name) ${data.alphabetical}`
  }

  if (data.ratting) {
    orderBy = `ORDER BY ratting ${data.ratting}`
  }

  if (data.distance_sort) {
    orderBy = 'ORDER BY distance ASC'
  }

  if (data.price_sort) {
    orderBy = `ORDER BY ts.price ${data.price_sort}, tv.price ${data.time_sort}`
  }

  if (data.time_sort) {
    orderBy = `ORDER BY tspas.from_time ${data.time_sort}`
  }

  const groupBy = data.screen_type === 'Map' ?
    'GROUP BY tbl.id' :
    'GROUP BY tbl.service_provider_id'

  const setting = await getSetting('recommended_max_avgrating')
  const params = {
    startRadius: data.start_radius ?? null,
    endRadius: data.end_radius || null,
    startPrice: data.start_price ?? null,
    endPrice: data.end_price || null,
    minDiscount: data.min_discount ?? null,
    maxDiscount: data.max_discount || null,
    avgRating: setting.value ?? 3.5,
    limit: page * Global.PER_PAGE,
    perPage: Global.PER_PAGE,
    latitude: data.latitude,
    longitude: data.longitude,
    word: data.word ? `%${data.word.toLowerCase()}%` : null,
    categoryId: data.category_id || null,
    timeZoneDiff: data.timezone_diff,
    time: data.time ? utcStringToTz(
      `${moment().format('YYYY-MM-DD')} ${data.time}`,
      'YYYY-MM-DD HH:mm:ss',
      data.timezone,
    ).format('HH:mm') : null,
    userId: data.user_id,
    imgPrefix: `${Global.S3_BUCKET_ROOT}${Global.SP_IMAGE}`,
  }
  const [rows] = (await named_db_con.query(`
    SELECT DISTINCT
      tsp.id AS service_provider_id,
      tsp.*,
      tbl.id AS business_id,
      tbl.name AS business_address,
      tbl.time_slot_duration_minutes AS business_time_slot_duration_minutes,
      tbl.timezone_code AS business_timezone_code,
      IF(
        :minDiscount IS NOT NULL AND :maxDiscount IS NOT NULL,
        IFNULL(
          (
            SELECT discount_amount
            FROM tbl_occational_promo
            WHERE is_deleted = 0
            AND service_provider_id = tsp.id
            AND valid_till >= CURDATE()
            AND LOWER(discount_type) = 'percentage'
            AND (
              is_clientspecific_promo = 0
              OR (
                is_clientspecific_promo = 1
                AND id IN (
                  SELECT promo_id
                  FROM tbl_occational_promo_clients
                  WHERE user_id = :userId
                  GROUP BY promo_id
                )
              )
            )
            AND discount_amount BETWEEN :minDiscount AND :maxDiscount
            GROUP BY service_provider_id
          ),
          0
        ),
        0
      ) AS discount_amount,
      IFNULL(
        (
          SELECT status
          FROM tbl_bookmark
          WHERE service_provider_id = tsp.id
          AND user_id = :userId
        ),
        0
      ) AS bookmark_status,
      CONCAT(:imgPrefix, tsp.profile_image) AS vendor_image,
      COMPUTE_DISTANCE_IN_MILES(:latitude, :longitude, tbl.latitude, tbl.longitude) AS distance,
      GET_SERVICE_PROVIDER_RATING(tsp.id, NULL) AS ratting,
      GET_SERVICE_PROVIDER_REVIEW_COUNT(tsp.id, NULL) AS review,
      tbl.latitude,
      tbl.longitude,
      IFNULL(
        (
          SELECT COUNT(id)
          FROM tbl_service
          WHERE service_provider_id = tbl.service_provider_id
          AND is_deleted = 0
          GROUP BY service_provider_id
          LIMIT 1
        ),
        0
      ) AS total_services,
      IFNULL(
        (
          SELECT COUNT(id)
          FROM product
          WHERE service_provider_id = tbl.service_provider_id
          AND tp.is_active
          AND NOT is_deleted
          GROUP BY service_provider_id
          LIMIT 1
        ),
        0
      ) AS total_products,
      IF(
        tsp.is_recommended = 'No',
        IF(
          GET_SERVICE_PROVIDER_RATING(tsp.id, NULL) >= :avgRating,
          'Yes',
          'No'
        ),
        tsp.is_recommended
      ) AS is_recommended
    FROM tbl_business_location AS tbl
    JOIN tbl_service_provider AS tsp ON tsp.id = tbl.service_provider_id
      AND NOT tsp.is_deleted
      AND LOWER(tsp.status) = 'active'
    LEFT JOIN tbl_service_provider_available_slot AS tspas ON tsp.id = tspas.service_provider_id
    LEFT JOIN tbl_service AS ts ON tsp.id = ts.service_provider_id
      AND ts.is_deleted = 0
      AND IF(
       :categoryId IS NOT NULL,
       ts.category_id = :categoryId,
       TRUE
      )
    LEFT JOIN product AS tp ON tsp.id = tp.service_provider_id
      AND NOT tp.is_deleted
      AND tp.is_active
      AND IF(
       :categoryId IS NOT NULL,
       tp.category_id = :categoryId,
       TRUE
      )
    LEFT JOIN product_variant tv ON tv.product_id = tsp.id
      AND tv.is_active
      AND NOT tv.is_deleted
    WHERE NOT tbl.is_deleted
    AND NOT EXISTS (
      SELECT bl.id FROM block_list bl
      JOIN tbl_user u ON u.uuid = bl.blockedByUserUUID
      AND u.id = :userId
      WHERE bl.blockedUserUUID = tsp.uuid
      AND NOT bl.isDeleted
    )
    AND IF(
      :word IS NOT NULL,
      LOWER(tsp.first_name) LIKE :word
      OR LOWER(tsp.last_name) LIKE :word
      OR LOWER(CONCAT(tsp.first_name, ' ', tsp.last_name)) LIKE :word
      OR LOWER(CONCAT(tsp.last_name, ' ', tsp.first_name)) LIKE :word
      OR LOWER(tsp.email) LIKE :word
      OR LOWER(ts.service_name) LIKE :word
      OR LOWER(tp.name) LIKE :word,
      TRUE
    )
    AND IF(
      :categoryId IS NOT NULL,
      tp.category_id = :categoryId
      OR ts.category_id = :categoryId,
      TRUE
    )
    AND IF(
      :startPrice IS NOT NULL AND :endPrice IS NOT NULL,
      (ts.price BETWEEN :startPrice AND :endPrice)
      OR (tv.price BETWEEN :startPrice AND :endPrice),
      TRUE
    )
    AND IF(
      :time IS NOT NULL,
      tsp.id IN (
        SELECT service_provider_id
        FROM tbl_service_provider_available_slot tspas
        WHERE NOT tspas.is_deleted
        AND tbl.id = tspas.business_location_id
        AND (
          (
            date IS NOT NULL
              AND tspas.date = CURRENT_DATE()
              AND :time BETWEEN DATE_FORMAT(
                CONVERT_TZ(CONCAT(current_date(), ' ', tspas.from_time), IFNULL(tbl.timezone_code, 'UTC'), :timeZoneDiff),
                '%H:%i'
              ) AND DATE_FORMAT(
                CONVERT_TZ(CONCAT(current_date(), ' ', tspas.to_time), IFNULL(tbl.timezone_code, 'UTC'), :timeZoneDiff),
                '%H:%i'
              )
            )
          OR (
            tspas.date IS NULL
            AND NOT EXISTS (
              SELECT 1
              FROM tbl_service_provider_available_slot s2
              WHERE s2.service_provider_id = tspas.service_provider_id
              AND s2.date = CURRENT_DATE()
              AND NOT s2.is_deleted
            )
            AND :time BETWEEN DATE_FORMAT(
              CONVERT_TZ(CONCAT(current_date(), ' ', from_time), IFNULL(tbl.timezone_code, 'UTC'), :timeZoneDiff),
              '%H:%i'
            ) AND DATE_FORMAT(
              CONVERT_TZ(CONCAT(current_date(), ' ', to_time), IFNULL(tbl.timezone_code, 'UTC'), :timeZoneDiff),
              '%H:%i'
            )
          )
        )
      ),
      TRUE
    )
    ${groupBy}
    HAVING (total_services >= 1 OR total_products >= 1)
    AND IF(
      :startRadius IS NOT NULL AND :endRadius IS NOT NULL,
      distance between :startRadius AND :endRadius,
      distance <= 50
    )
    ${orderBy}
    LIMIT :limit, :perPage`,
    params,
  )) as any[]

  return Promise.all((rows ?? []).map(async (it: any) => ({
    ...it,
    sliderimages: await getSliderImagesForServiceProvider(it.id)
  })))
}

export async function getServiceProviderClients(
  serviceProviderId: number,
  searchWord: string,
  page: number
) {
  const params = {
    limit: (page - 1) * Global.PER_PAGE,
    perPage: Global.PER_PAGE,
    serviceProviderId,
    searchWord: searchWord ? `%${searchWord.toLowerCase()}%` : null,
    imgPrefix: `${Global.S3_BUCKET_ROOT}${Global.USER_IMAGE}`,
  }

  try {
    const [rows] = await named_db_con.query(
      `
      SELECT
        c.id,
        c.is_user_app AS user_id,
        TRIM(SUBSTR(c.customer_name, LOCATE(' ', c.customer_name))) AS last_name,
        SUBSTRING_INDEX(SUBSTRING_INDEX(c.customer_name, ' ', 1), ' ', -1) AS first_name,
        CONCAT(:imgPrefix, c.profile_image) AS profile_image,
        c.country_code,
        c.phone,
        c.avg_rating AS rating,
        (
          SELECT COUNT(r.id)
          FROM tbl_client_reviews r
          WHERE r.service_provider_id = c.service_provider_id
          AND r.client_id = c.id
          AND r.is_deleted = '0'
        ) AS reviews_count
      FROM tbl_service_provider_client c
      WHERE c.service_provider_id = :serviceProviderId
      AND c.is_deleted = '0'
      AND IF(:searchWord IS NULL, TRUE, LOWER(c.customer_name) LIKE :searchWord)
      ORDER BY
        NULLIF(last_name, '') IS NULL,
        last_name,
        NULLIF(first_name, '') IS NULL,
        first_name
      LIMIT :limit, :perPage
    `,
      params
    )
    return rows
  } catch (error) {
    logger.error({ message: 'Failed to retrieve clients', error, meta: params })
    return []
  }
}

export async function getSliderImagesForServiceProvider(id: number) {
  try {
    const [rows] = await named_db_con.query(
      `
        SELECT
          ti.id AS image_id,
          CONCAT('${Global.S3_BUCKET_ROOT}${Global.SP_IMAGE}', ti.image_name) AS image
        FROM tbl_image AS ti
        WHERE ti.is_deleted = 0
        AND ti.type_id = :id
        AND ti.type = 'serviceprovider_sliderimage'
        ORDER BY ti.id ASC
    `,
      { id }
    )
    return rows
  } catch (e) {
    logger.error(`Failed to retrieve slider images for service provider ${id}`)
    return []
  }
}

export async function serviceProviderExists(
  email: string,
  phone: string = '',
  socialId: string = ''
): Promise<boolean> {
  try {
    const [rows] = await db_conn.query(`
        SELECT
            COUNT(*) AS count
        FROM tbl_service_provider
        WHERE is_deleted = 0
          AND LOWER(status) = '${USER_STATUS.ACTIVE.toLowerCase()}'
          AND (
            LOWER(email) = '${email?.toLowerCase()}'
            OR IF(${!!phone}, LOWER(phone) = '${
      phone?.toLowerCase() ?? ''
    }', FALSE)
            OR IF(${!!socialId}, LOWER(social_id) = '${
      socialId?.toLowerCase() ?? ''
    }', FALSE)
          )
    `)
    return (rows[0]?.count ?? 0) > 0
  } catch (e) {
    logger.error(
      `Failed to check for existing service provider. email=${email}, phone=${phone}, socialId=${socialId}`,
      e
    )
    return false
  }
}

export async function getServiceProvider(
  id: number
): Promise<IServiceProvider | null> {
  try {
    const [rows] = await named_db_con.query(
      `
      SELECT *
      FROM tbl_service_provider
      WHERE id = :id
      AND is_deleted = 0
    `,
      { id }
    )
    return (rows[0] as IServiceProvider) ?? null
  } catch (e) {
    logger.error(`Failed to retrieve service provider ${id}`, e)
    return null
  }
}

export async function getServiceProviderAccData(
  id: number
): Promise<IServiceProviderAccData | null> {
  try {
    const [rows] = await named_db_con.query(
      `SELECT
      CONCAT(first_name, ' ', last_name) AS name,
      payouts_enabled AS payoutsStatus,
      stripe_verified AS stripeVerification,
      dob AS dateOfBirth,
      ssn_provided AS ssnProvided
    FROM tbl_service_provider WHERE id = :id AND is_deleted = 0`,
      { id }
    )
    if (rows[0]) {
      return {
        name: rows[0].name,
        payoutsStatus: Boolean(rows[0].payoutsStatus),
        stripeVerification: Boolean(rows[0].stripeVerification),
        dateOfBirth: rows[0].dateOfBirth,
        ssnProvided: Boolean(rows[0].ssnProvided),
      }
    }
    return null
  } catch (e) {
    logger.error(`Failed to retrieve service provider ${id}`, e)
    return null
  }
}

async function getServiceProviderBaseStatus(id: number): Promise<string> {
  try {
    const [results] = await named_db_con.query(`
      SELECT status
      FROM tbl_service_provider
      WHERE id = :id
      AND NOT is_deleted
    `, { id })
    return (results as any[])[0]?.status || 'Inactive'
  } catch (error) {
    logger.error({ message: 'Failed to get service provider base status', error, meta: { serviceProviderId: id } })
    return 'Inactive'
  }
}

async function getServiceProviderServicesStatus(id: number): Promise<string> {
  try {
    const [results] = await named_db_con.query(`
      SELECT IF(
        EXISTS(
          SELECT s.id
          FROM tbl_service s
          JOIN tbl_service_provider sp ON sp.id = s.service_provider_id
            AND NOT sp.is_deleted
          WHERE s.service_provider_id = :id
          AND NOT s.is_deleted
          AND s.status = 'Active'
        ),
        'Active',
        'Inactive'
      ) AS status
    `, { id })
    return (results as any[])[0]?.status || 'Inactive'
  } catch (error) {
    logger.error({ message: 'Failed to get service provider services status', error, meta: { serviceProviderId: id } })
    return 'Inactive'
  }
}

async function getServiceProviderProductsStatus(id: number): Promise<string> {
  try {
    const [results] = await named_db_con.query(`
      SELECT IF(
        EXISTS(
          SELECT p.id
          FROM product p
          JOIN tbl_service_provider sp ON sp.id = p.service_provider_id
            AND NOT sp.is_deleted
          WHERE p.service_provider_id = :id
          AND NOT p.is_deleted
          AND p.is_active
        ),
        'Active',
        'Inactive'
      ) AS status
    `, { id })
    return (results as any[])[0]?.status || 'Inactive'
  } catch (error) {
    logger.error({ message: 'Failed to get service provider products status', error, meta: { serviceProviderId: id } })
    return 'Inactive'
  }
}

export async function getServiceProviderProfileStatus(id: number): Promise<{
  profileStatus: string
  servicesStatus: string
  productsStatus: string
}> {
  const [profileStatus, servicesStatus, productsStatus] = await Promise.all([
    getServiceProviderBaseStatus(id),
    getServiceProviderServicesStatus(id),
    getServiceProviderProductsStatus(id)
  ])

  return {
    profileStatus,
    servicesStatus,
    productsStatus
  }
}

export async function getServiceProviderForLogin(
  email: string,
  phone: string,
  socialId: string,
  loginType: string,
  encryptedPassword: string
): Promise<any> {
  const params = {
    email: email?.toLowerCase() ?? null,
    phone: phone?.toLowerCase() ?? null,
    socialId: socialId?.toLowerCase() ?? null,
    loginType: loginType?.toLowerCase() ?? null,
    password: encryptedPassword ?? null,
    userStatus: USER_STATUS.ACTIVE.toLowerCase(),
  }

  logger.info({ message: 'Checking service provider for login', meta: params })
  try {
    const [rows] = await named_db_con.query(
      `
      SELECT *, id AS service_provider_id
      FROM tbl_service_provider
      WHERE is_deleted = 0
      AND LOWER(status) = :userStatus
      AND (
        (
          (
            LOWER(email) = :email
            OR LOWER(phone) = :phone
          )
          AND password = :password
        )
        OR (
          IFNULL(social_id, '') != ''
          AND LOWER(social_id) = :socialId
          AND LOWER(login_type) = :loginType
        )
      )
    `,
      params
    )
    const result = omit(rows[0], 'password')
    return result?.id ? result : null
  } catch (e) {
    logger.error(
      `Failed to check a service provider login. ${JSON.stringify(params)}`,
      e
    )
    throw Error('Failed to validate user login')
  }
}

export async function getServiceProviderWithDetails(
  id: number
): Promise<any | null> {
  try {
    const [rows] = await db_conn.query(`
      SELECT
        sp.*,
        sp.id AS service_provider_id,
        CONCAT('${Global.S3_BUCKET_ROOT}${Global.SP_IMAGE}', sp.profile_image) AS profile_image,
        CONCAT('${Global.S3_BUCKET_ROOT}${Global.DOCUMENT_IMAGE}', sp.state_issue_id) AS state_issue_id,
        CONCAT('${Global.S3_BUCKET_ROOT}${Global.DOCUMENT_IMAGE}', sp.signed_lease) AS signed_lease,
        CONCAT('${Global.S3_BUCKET_ROOT}${Global.DOCUMENT_IMAGE}', sp.bank_image) AS bank_image,
        sp.profile_image AS image_name,
        device.token,
        GET_SERVICE_PROVIDER_RATING(sp.id, NULL) AS ratting,
        GET_SERVICE_PROVIDER_REVIEW_COUNT(sp.id, NULL) AS review,
        IFNULL(
          (
            SELECT value
            FROM tbl_settings
            WHERE title = 'booth_rent_tax'
          ),
          0
        ) AS booth_rent_tax,
        IFNULL(
          (
            SELECT value
            FROM tbl_settings
            WHERE title = 'booth_rent_cleaning_fees'
          ),
          0
        ) AS booth_rent_cleaning_fees,
        IFNULL(
          (
            SELECT COUNT(DISTINCT b.user_id)
            FROM tbl_bookmark AS b
            JOIN tbl_user u ON b.user_id = u.id
            WHERE b.service_provider_id = sp.id
            AND b.status
            AND u.status = 'Active'
            AND NOT u.is_deleted
          ),
          0
        ) AS total_followers,
        0 AS following,
        IFNULL(
          (
            SELECT JSON_ARRAYAGG(
              JSON_OBJECT(
                'id', id,
                'name', name,
                'latitude', latitude,
                'longitude', longitude,
                'country_name', country_name,
                'country_code', country_code,
                'state_name', state_name,
                'city_name', city_name,
                'postal_code', postal_code,
                'service_provider_id', service_provider_id
              )
            )
            FROM tbl_business_location
            WHERE service_provider_id = sp.id
            AND status = 'Active'
            AND is_deleted = 0
          ),
          '[]'
        ) AS business_locations
      FROM tbl_service_provider sp
      LEFT JOIN tbl_user_deviceinfo device ON sp.id = device.user_id
        AND LOWER(device.user_type) = '${USER_TYPE.SERVICE_PROVIDER}'
      WHERE sp.id = ${id}
      AND sp.is_deleted = 0
    `)
    const business_locations = JSON.parse(rows[0]?.business_locations ?? '[]')
    const result = omit(
      {
        ...rows[0],
        business_locations,
        business_location: business_locations,
      },
      'password'
    )
    return result?.id ? result : null
  } catch (e) {
    logger.error(`Failed to get service provider by id ${id} with details`, e)
    throw `Failed to get service provider by id ${id} with details`
  }
}

export async function getServiceProviderWithDetailsByEmail(
  email: string
): Promise<any | null> {
  try {
    const [rows] = await db_conn.query(`
      SELECT
        sp.*,
        sp.id AS service_provider_id,
        CONCAT('${Global.S3_BUCKET_ROOT}${Global.SP_IMAGE}', sp.profile_image) AS profile_image,
        CONCAT('${Global.S3_BUCKET_ROOT}${Global.DOCUMENT_IMAGE}', sp.state_issue_id) AS state_issue_id,
        CONCAT('${Global.S3_BUCKET_ROOT}${Global.DOCUMENT_IMAGE}', sp.signed_lease) AS signed_lease,
        CONCAT('${Global.S3_BUCKET_ROOT}${Global.DOCUMENT_IMAGE}', sp.bank_image) AS bank_image,
        sp.profile_image AS image_name,
        device.token,
        GET_SERVICE_PROVIDER_RATING(sp.id, NULL) AS ratting,
        GET_SERVICE_PROVIDER_REVIEW_COUNT(sp.id, NULL) AS review,
        IFNULL(
          (
            SELECT value
            FROM tbl_settings
            WHERE title = 'booth_rent_tax'
          ),
          0
        ) AS booth_rent_tax,
        IFNULL(
          (
            SELECT value
            FROM tbl_settings
            WHERE title = 'booth_rent_cleaning_fees'
          ),
          0
        ) AS booth_rent_cleaning_fees,
        GET_SERVICE_PROVIDER_FOLLOWERS_COUNT(sp.id) AS total_followers,
        0 AS following,
        IFNULL(
          (
            SELECT JSON_ARRAYAGG(
              JSON_OBJECT(
                'id', id,
                'name', name,
                'latitude', latitude,
                'longitude', longitude,
                'country_name', country_name,
                'country_code', country_code,
                'state_name', state_name,
                'city_name', city_name,
                'postal_code', postal_code,
                'service_provider_id', service_provider_id
              )
            )
            FROM tbl_business_location
            WHERE service_provider_id = sp.id
            AND status = 'Active'
          ),
          '[]'
        ) AS business_locations
      FROM tbl_service_provider sp
      LEFT JOIN tbl_user_deviceinfo device ON sp.id = device.user_id
        AND device.user_type = 'service_provider'
      WHERE LOWER(sp.email) = '${email.toLowerCase()}'
      AND sp.is_deleted = 0
    `)
    const business_locations = JSON.parse(rows[0]?.business_locations ?? '[]')
    return rows[0]
      ? {
          ...rows[0],
          business_locations,
          business_location: business_locations,
        }
      : null
  } catch (e) {
    logger.error(
      `Failed to get service provider by email ${email} with details`,
      e
    )
    throw `Failed to get service provider by email ${email} with details`
  }
}

export async function updateServiceProvider(
  id: number | string,
  fields: Record<string, unknown>
): Promise<void> {
  if (!id) {
    logger.error('No service provider id provided for update')
    return
  }

  try {
    await db_conn.query(
      'UPDATE tbl_service_provider SET ? WHERE id = ' + id,
      fields
    )
  } catch (error) {
    logger.error({ message: `Failed to update service provider`, error, meta: { id, fields } })
    throw Error(`Failed to update service provider ${id}.`)
  }
}

export async function updateServiceProviderStipeId(
  serviceProviderId: number,
  stipeAccId: string
): Promise<void> {
  try {
    await named_db_con.query(
      `UPDATE tbl_service_provider SET merchant_account_id = '${stipeAccId}' WHERE id = ${serviceProviderId}`
    )
  } catch (e) {
    logger.error(`Failed to update service provider ${serviceProviderId}.`, e)
    throw Error(`Failed to update service provider ${serviceProviderId}.`)
  }
}

export async function getServiceProviderShallow(
  id: string
): Promise<ShallowServiceProvider | null> {
  try {
    const [rows] = id
      ? await db_conn.query(`
        SELECT
            sp.id,
            sp.first_name,
            sp.last_name,
            sp.email,
            sp.country_code,
            sp.phone,
            sp.dob,
            sp.merchant_account_id,
            sp.stripe_bank_id,
            sp.profile_image,
            sp.merchant_fees,
            sp.app_fees,
            sp.merchant_fees_cents,
            ud.token,
            ud.device_token,
            ud.device_type
        FROM tbl_service_provider sp
        LEFT JOIN tbl_user_deviceinfo ud on sp.id = ud.user_id
          AND ud.user_type = 'service_provider'
        WHERE sp.id = ${id}
        AND sp.is_deleted = 0
    `)
      : [[]]

    return rows[0]
  } catch (e) {
    logger.error(`Failed to get service provider ${id}.`, e)
    return null
  }
}

export async function getServiceProviderByEmailShallow(email: string) {
  try {
    const [rows] = email
      ? await named_db_con.query(`
        SELECT
          sp.id,
          sp.first_name,
          sp.last_name,
          sp.email,
          sp.country_code,
          sp.phone,
          sp.dob,
          sp.merchant_account_id,
          sp.stripe_bank_id,
          sp.profile_image,
          sp.login_type,
          ud.token,
          ud.device_token,
          ud.device_type
        FROM tbl_service_provider sp
        LEFT JOIN tbl_user_deviceinfo ud on sp.id = ud.user_id
          AND ud.user_type = 'service_provider'
        WHERE sp.email = :email
        AND NOT sp.is_deleted
    `, { email })
      : [[]]

    return rows[0]
  } catch (e) {
    logger.error(`Failed to get service provider ${email}.`, e)
    return null
  }
}

export async function getServiceProviderWithBusinessLocations(
  serviceProviderId: string
): Promise<ProviderWithLocation> {
  try {
    const provider = await getServiceProviderShallow(serviceProviderId)

    if (!provider) {
      throw Error(`User not found ${serviceProviderId}`)
    }
    const businessLocations = await getBusinessLocations(provider.id)
    return { ...provider, business_locations: businessLocations }
  } catch (e) {
    logger.error('Failed to get service provider.', e)
    throw Error('Failed to get user details')
  }
}

export async function createStripeAccount(serviceProviderId: string) {
  try {
    const serviceProvider = await getServiceProviderWithBusinessLocations(
      serviceProviderId
    )
    if (!serviceProvider?.business_locations[0]) {
      throw Error('Failed to identify business location.')
    }
    const stripeAccount = newStripeAccount(serviceProvider)
    console.debug(
      `######### stipeAccount: ${JSON.stringify(stripeAccount, null, 2)}`
    )
    const account = await createAccount(stripeAccount)
    if (!account) {
      throw Error('Stripe returned no account after create')
    }

    const updateFields = {
      merchant_account_id: account.id,
    }
    await updateServiceProviderStipeId(serviceProvider.id, account.id)

    return { ...serviceProvider, ...updateFields }
  } catch (error) {
    logger.error(error)
    throw error
  }
}

function newStripeAccount(
  serviceProvider: ProviderWithLocation
): Stripe.AccountCreateParams {
  if (!serviceProvider) {
    logger.error(
      'Cannot create a new stripe account instance. Missing service provider info'
    )
    throw Error(
      'Cannot create a new stripe account instance. Missing service provider info'
    )
  }

  return {
    country: serviceProvider.business_locations[0]?.country_code || 'US',
    controller: {
      fees: { payer: 'application' },
      losses: { payments: 'application' },
      requirement_collection: 'application',
      stripe_dashboard: { type: 'none' },
    },
    capabilities: {
      card_payments: { requested: true },
      transfers: { requested: true },
    },
  }
}

export async function getBusinessLocations(
  serviceProviderId: number
): Promise<BusinessLocation[]> {
  try {
    const [rows] = serviceProviderId
      ? await db_conn.query(`
        SELECT *
        FROM tbl_business_location
        WHERE service_provider_id = ${serviceProviderId}
        AND is_deleted = 0
    `)
      : [[]]
    return rows as BusinessLocation[]
  } catch (e) {
    logger.error(
      `Failed to get business location for service ${serviceProviderId}.`,
      e
    )
    return []
  }
}

export async function getBusinessLocation(
  id: number,
  serviceProviderId: number
) {
  const params = { id, serviceProviderId }
  try {
    const [rows] = await named_db_con.query(
      `
      SELECT *
      FROM tbl_business_location
      WHERE service_provider_id = :serviceProviderId
      AND id = :id
      AND is_deleted = 0
    `,
      params
    )
    const bl = rows[0]
    return bl
      ? {
          ...bl,
          timezone_code:
            bl.timezone_code || geoFind(bl.latitude, bl.longitude)[0],
        }
      : null
  } catch (e) {
    logger.error(
      `Failed to get business location ${JSON.stringify(params)}.`,
      e
    )
    return null
  }
}

export async function addBusinessLocation(fields: IAddBusinessLocation): Promise<number> {
  try {
    const query = `INSERT INTO tbl_business_location SET ?`
    const [result] = await db_conn.query(query, fields)
    return (result as ResultSetHeader).insertId
  } catch (error) {
    logger.error({ message: 'Failed to add business location', error, meta: { ...fields } })
    return 0
  }
}

export async function getSPTermsSettings(id) {
  const query = `
      SELECT
          autocancellation_charges,
          cancellation_charges,
          cancellationwithin_hours,
          customize_cancellation_charges
      FROM tbl_service_provider
      WHERE id = ${id}
  `
  const [rows] = await db_conn.query(query)
  return rows[0] || []
} // getSPTermsSettings

async function createServiceProvider(fields: any) {
  try {
    const [result] = await db_conn.query(
      'INSERT INTO tbl_service_provider SET ?',
      fields
    )
    return (result as ResultSetHeader).insertId
  } catch (e) {
    logger.error('Failed to create a user', e)
    throw Error('Failed to create a user')
  }
}

export async function getSimilarInactiveServiceProviders(
  excludedId: number,
  email: string,
  phone: string = '',
  socialId: string = ''
): Promise<number[]> {
  try {
    const [rows] = await db_conn.query(`
        SELECT
            id
        FROM tbl_service_provider
        WHERE is_deleted = 0
          AND LOWER(status) = '${USER_STATUS.INACTIVE.toLowerCase()}'
          AND id != ${excludedId}
          AND (
            LOWER(email) = '${email?.toLowerCase()}'
            OR IF('${!!phone}', LOWER(phone) = '${
      phone?.toLowerCase() ?? ''
    }', FALSE)
            OR IF('${!!socialId}', LOWER(social_id) = '${
      socialId?.toLowerCase() ?? ''
    }', FALSE)
          )
    `)
    return (rows as any[])?.map((it) => it.id) ?? []
  } catch (e) {
    logger.error(
      `Failed to get similar inactive service providers. email=${email}, phone=${phone}, socialId=${socialId}`,
      e
    )
    return []
  }
}

export async function sendServiceProviderOTP(body: any) {
  let serviceProvider: any = {}
  let subject = ''
  let updateFields: any = {}
  const otp = generateOTP(6)

  switch (body.status) {
    case 'signup':
      serviceProvider = await getServiceProviderWithDetails(body.serviceProviderId)

      if (!serviceProvider) {
        throw Error(`Failed to find the service provider ${body.serviceProviderId}`)
      }

      subject = `${ Global.APP_NAME } One Time Password (OTP)`
      updateFields = {
        otp,
        otp_verify: USER_OTP_STATUS.PENDING,
      }
      break
    case 'forgotpassword':
      serviceProvider = await getServiceProviderWithDetailsByEmail(body.email)

      if (!serviceProvider) {
        throw Error(`Failed to find the service provider ${body.serviceProviderId}`)
      }

      subject = `${Global.APP_NAME} Forgot Password`
      updateFields = {
        forgot_otp: otp,
        forgot_otp_verify: USER_OTP_STATUS.PENDING,
        forgotpwd_datetime: datetime.create().format('Y-m-d H:M:S'),
      }
      break
    default: return null
  }

  const updatedServiceProvider = { ...serviceProvider, ...updateFields }
  const otpTemplate = getOTPTemplate({ ...updatedServiceProvider, otp })
  await sendEmail(subject, updatedServiceProvider.email, otpTemplate)
  await sendSMS(
    `${serviceProvider.country_code}${serviceProvider.phone}`,
    `Your verification code for ${ Global.APP_NAME } is ${ otp }`
  )
  await updateServiceProvider(updatedServiceProvider.id, updateFields)
  return updatedServiceProvider
}

export async function getServiceProviderThatForgotPassword(
  id: number,
  otp: string
) {
  try {
    const [rows] = id
      ? await db_conn.query(`
          SELECT
              u.*,
              ud.device_token,
              ud.device_type
          FROM tbl_service_provider u
          LEFT JOIN tbl_user_deviceinfo as ud ON u.id = ud.user_id
            AND ud.user_type = 'service_provider'
          WHERE u.id = ${id}
          AND u.is_deleted = 0
          AND u.forgot_otp = '${otp}'
      `)
      : [[]]
    return rows[0]
  } catch (e) {
    logger.error(
      `Error trying to retrieve a service provider ${id} that forgot password`,
      e
    )
    return null
  }
}

export async function getServiceProviderByOTP(id: number, otp: string) {
  try {
    const [rows] = id
      ? await db_conn.query(`
          SELECT
            u.*,
            ud.device_token,
            ud.device_type
          FROM tbl_service_provider u
          LEFT JOIN tbl_user_deviceinfo as ud ON u.id = ud.user_id
            AND ud.user_type = 'service_provider'
          WHERE u.id = ${id}
          AND u.is_deleted = 0
          AND u.otp = '${otp}'
      `)
      : [[]]
    return rows[0]
  } catch (e) {
    logger.error(`Error trying to retrieve a service provider ${id} by OTP`, e)
    return null
  }
}

export async function verifyServiceProviderOTP(body: {
  status: 'signup' | 'forgotpassword'
  user_id: number
  otp: string | number
}) {
  const otpStatus =
    body.status === 'signup'
      ? {
          otp: '',
          otp_verify: USER_OTP_STATUS.VERIFY,
          status: USER_STATUS.ACTIVE,
          last_login: datetime.create().format('Y-m-d H:M:S'),
        }
      : body.status === 'forgotpassword'
      ? {
          forgot_otp: '',
          forgot_otp_verify: USER_OTP_STATUS.VERIFY,
          status: USER_STATUS.ACTIVE,
          last_login: datetime.create().format('Y-m-d H:M:S'),
        }
      : {}
  const user =
    body.status === 'signup'
      ? await getServiceProviderByOTP(body.user_id, body.otp.toString())
      : body.status === 'forgotpassword'
      ? await getServiceProviderThatForgotPassword(
          body.user_id,
          body.otp.toString()
        )
      : null

  if (!user) {
    throw new AppError('Failed to verify OTP')
  }

  const updateParam = {
    token: tokenGenerator.generator().generate(64),
    user_type: 'service_provider',
  }

  await updateServiceProvider(body.user_id, otpStatus)
  await updateUserDeviceInfoForUser(
    body.user_id.toString(),
    updateParam,
    'service_provider'
  )

  return await getServiceProviderShallow(body.user_id.toString())
}

export async function addServiceProvider(body: any) {
  if (!body.social_id && ['A', 'F', 'G'].includes(body.login_type)) {
    throw Error('Missing social id')
  }

  if (body.login_type === 'S' && !body.password) {
    throw Error('Missing password')
  }

  const userId = await createServiceProvider({
    first_name: body.first_name,
    last_name: body.last_name,
    email: body.email,
    country_code: body.country_code,
    phone: body.phone,
    social_id: body.social_id ?? undefined,
    password: body.password ? encryptString(body.password) : undefined,
    latitude: body.latitude ?? '',
    longitude: body.longitude ?? '',
    otp_verify: body.social_id
      ? USER_OTP_STATUS.VERIFY
      : USER_OTP_STATUS.PENDING,
    status: USER_STATUS.ACTIVE,
    login_status: USER_LOGIN_STATUS.OFFLINE,
    login_type: body.login_type,
    account_type: body.account_type,
    unique_id: await generateUniqCode(),
    last_login: datetime.create().format('Y-m-d H:M:S'),
    updatetime: datetime.create().format('Y-m-d H:M:S'),
    insertdate: datetime.create().format('Y-m-d H:M:S'),
    profile_image: body.profile_image ?? 'default-user.png',
  })
  const userType = 'service_provider'
  await upsertUserDeviceInfo(userId, userType, {
    token: tokenGenerator.generator().generate(64),
    device_token: body.device_token ?? '',
    device_type: body.device_type ?? '',
    uuid: body.uuid ?? '',
    ip: body.ip ?? '',
    user_type: userType,
    os_version: body.os_version ?? '',
    model_name: body.model_name ?? '',
  })

  const response = !body.social_id ?
    await sendServiceProviderOTP({ serviceProviderId: userId, status: 'signup' }) :
    await getServiceProviderWithDetails(userId)

  const cleanup = await getSimilarInactiveServiceProviders(
    userId,
    body.email,
    body.phone,
    body.social_id
  )
  await Promise.all(
    cleanup.map((it) =>
      updateServiceProvider(it.toString(), {
        status: USER_STATUS.INACTIVE,
        is_deleted: 1,
        updatetime: datetime.create().format('Y-m-d H:M:S'),
        login_status: USER_LOGIN_STATUS.OFFLINE,
      })
    )
  )

  return response
}

export async function checkServiceProviderLogin(data: any) {
  if (!data.social_id && ['F', 'G', 'A'].includes(data.login_type)) {
    throw Error('Missing social id')
  }

  if (!data.password && data.login_type === 'S') {
    throw Error('Missing password')
  }

  const serviceProvider = await getServiceProviderForLogin(
    data.email,
    data.phone,
    data.social_id,
    data.login_type,
    data.password ? encryptString(data.password) : ''
  )

  if (!serviceProvider) {
    return {
      code: data.social_id ? 11 : 0,
      message: data.social_id
        ? 'User social account not found'
        : 'Username or password is incorrect. Please try again.',
    }
  }

  if (serviceProvider.is_complete_profile === 0) {
    return {
      data: serviceProvider,
      message: 'Profile is pending',
      code: 8,
    }
  }

  await updateServiceProvider(serviceProvider.id, {
    latitude: data.latitude ?? '',
    longitude: data.longitude ?? '',
    login_status: USER_LOGIN_STATUS.ONLINE,
    last_login: datetime.create().format('Y-m-d H:M:S'),
  })
  await updateUserDeviceInfoForUser(
    serviceProvider.id,
    {
      token: tokenGenerator.generator().generate(64),
      device_token: data.device_token,
      user_type: USER_TYPE.SERVICE_PROVIDER,
      device_type: data.device_type,
      uuid: data.uuid ?? '',
      ip: data.ip ?? '',
      os_version: data.os_version ?? '',
      model_name: data.model_name ?? '',
    },
    USER_TYPE.SERVICE_PROVIDER
  )
  await removeTwilioBindings(
    `${USER_TYPE.SERVICE_PROVIDER}${serviceProvider.id}`
  )

  return {
    data: await getServiceProviderWithDetails(serviceProvider.id),
    message: 'Logged in successfully',
  }
}

export async function getHomeScreenDataForServiceProvider(body: any) {
  const data = await getHomeScreenAppointmentsForServiceProvider(
    body.service_provider_id,
    body.date
  )
  const target = await getServiceProviderTargetForHomeScreen(
    body.service_provider_id
  )

  return {
    data,
    target: {
      ...(target ?? {}),
      estimate_earning: reduce(
        data,
        (acc, it) => acc + parseFloat(it.total_amount),
        0
      ),
    },
    total_unread_notification: await getUnreadUserNotificationCount(
      body.service_provider_id,
      USER_TYPE.SERVICE_PROVIDER
    ),
  }
}

export async function changeServiceProviderPassword(body: any) {
  const serviceProvider = await getServiceProvider(body.user_id)

  if (!serviceProvider) {
    throw new Error('Unknown service provider')
  }

  if (!body.new_password) {
    throw new Error('Missing new password value')
  }

  if (!body.old_password) {
    throw new Error('Missing old password value')
  }

  if (body.old_password === body.new_password) {
    throw new Error('Old password and new password can not be same.')
  }

  if (!serviceProvider.password) {
    throw new Error('Service provider does not have any password to reset')
  }

  const oldPassword = encryptString(body.old_password)
  if (oldPassword !== serviceProvider.password) {
    throw new Error('Please enter correct old password')
  }

  await updateServiceProvider(serviceProvider.id.toString(), {
    password: encryptString(body.new_password),
  })
}

async function getRating(serviceProviderId: number) {
  const params = { serviceProviderId }
  try {
    const [rows] = await named_db_con.query(
      `
        SELECT GET_SERVICE_PROVIDER_RATING(id, NULL) AS rating
        FROM tbl_service_provider
        WHERE id = :serviceProviderId
        AND NOT is_deleted
      `,
      params
    )
    return rows[0]?.rating ?? 0
  } catch (error) {
    logger.error({ message: 'Failed to get rating', meta: params, error })
    return 0
  }
}

export async function setStripeVerification(merchantId: string): Promise<void> {
  try {
    await named_db_con.query(
      `UPDATE tbl_service_provider SET stripe_verified = 1 WHERE merchant_account_id = ${merchantId}`
    )
  } catch (err) {
    logger.error(err)
    throw new Error(`Failed to set stripe verification: ${String(err)}`)
  }
}

export async function setStripeCardData(data: StripeCard): Promise<void> {
  try {
    const query = `
      INSERT INTO service_provider_cards
      (service_provider_id, stripe_card_id, last4, exp_month, exp_year, brand)
      VALUES (?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        last4 = VALUES(last4),
        exp_month = VALUES(exp_month),
        exp_year = VALUES(exp_year),
        brand = VALUES(brand)
    `

    await named_db_con.query(query, [
      data.serviceProviderId,
      data.stripeCardId,
      data.last4,
      data.expMonth,
      data.expYear,
      data.brand,
    ])
  } catch (err) {
    logger.error(`Failed to execute query: ${String(err)}`)
    throw new Error(
      `QueryError: Failed to set stripe card data: ${String(err)}`
    )
  }
}

export async function setStripeBankAccountData(
  data: StripeBankAccount
): Promise<void> {
  try {
    const query = `
      INSERT INTO service_provider_bank_accounts
      (
        service_provider_id,
        stripe_bank_account_id,
        last4,
        routing_number,
        bank_name,
        account_holder_name,
        account_holder_type,
        country,
        currency
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        last4 = VALUES(last4),
        routing_number = VALUES(routing_number),
        bank_name = VALUES(bank_name),
        account_holder_name = VALUES(account_holder_name),
        account_holder_type = VALUES(account_holder_type),
        country = VALUES(country),
        currency = VALUES(currency)
    `

    await named_db_con.query(query, [
      data.serviceProviderId,
      data.stripeBankAccountId,
      data.last4,
      data.routingNumber,
      data.bankName,
      data.accountHolderName,
      data.accountHolderType,
      data.country,
      data.currency,
    ])
  } catch (err) {
    logger.error(`Failed to execute query: ${String(err)}`)
    throw new Error(
      `QueryError: Failed to set stripe bank account data: ${String(err)}`
    )
  }
}

export async function setStripeAccountData(
  accountId: string,
  enabled: boolean,
  verified: boolean,
  ssnProvided: boolean
): Promise<void> {
  try {
    await named_db_con.query(
      `UPDATE tbl_service_provider SET payouts_enabled = ${enabled}, stripe_verified = ${verified}, ssn_provided = ${ssnProvided} WHERE merchant_account_id = "${accountId}"`
    )
  } catch (err) {
    logger.error(err)
    throw new Error(`Failed to set stripe payouts: ${String(err)}`)
  }
}

async function searchReviews(
  serviceProviderId: number,
  page: number = 1,
  searchDate: string | null = null,
  searchWord: string | null = null,
  categoryId: number | null = null,
  rating: number | null = null,
  sortDirection: string | null = 'DESC'
) {
  const params = {
    limit: (page - 1) * Global.PER_PAGE,
    perPage: Global.PER_PAGE,
    serviceProviderId,
    searchDate,
    categoryId,
    rating,
    searchWord: searchWord ? `%${searchWord.toLowerCase()}%` : null,
    userImgPrefix: `${Global.S3_BUCKET_ROOT}${Global.USER_IMAGE}`,
  }

  try {
    const [rows] = await named_db_con.query(
      `
      SELECT
        ur.id,
        ur.type,
        ur.ratting AS rating,
        ur.review,
        IFNULL(ur.review_reply, '') AS reply,
        ur.user_id AS userId,
        u.last_name AS userLastName,
        u.first_name AS userFirstName,
        CONCAT(:userImgPrefix, u.profile_image) AS userImage,
        IFNULL(
          (
            SELECT JSON_ARRAYAGG(s.service_name)
            FROM tbl_appointment_booking_detail d
            JOIN tbl_service s ON d.service_id = s.id
            WHERE d.appointment_id = ur.order_id
            AND LOWER(d.type) = 'service'
          ),
          '[]'
        ) AS services_json,
        DATE_FORMAT(ur.insertdate, '%Y-%m-%d %H:%i:%s') AS createdAt
      FROM tbl_user_review ur
      LEFT JOIN tbl_user u ON u.id = ur.user_id
      WHERE ur.service_provider_id = :serviceProviderId
      AND IF(:searchDate IS NULL, TRUE, DATE_FORMAT(ur.insertdate, '%Y-%m-%d') = :searchDate)
      AND IF(
        :categoryId IS NULL,
        TRUE,
        EXISTS(
          SELECT s.id
          FROM tbl_appointment_booking_detail d
          JOIN tbl_service s ON d.service_id = s.id
          WHERE d.appointment_id = ur.order_id
          AND LOWER(d.type) = 'service'
          AND s.category_id = :categoryId
        )
      )
      AND IF(:rating IS NULL, TRUE, ur.ratting >= :rating)
      AND IF(:searchWord IS NULL, TRUE, LOWER(CONCAT(u.first_name, ' ', u.last_name)) LIKE :searchWord OR LOWER(CONCAT(u.last_name, ' ', u.first_name)) LIKE :searchWord)
      AND ur.ratting > 0
      AND NOT ur.is_deleted
      GROUP BY ur.id, ur.insertdate
      ORDER BY ur.insertdate ${sortDirection ?? 'DESC'}
      LIMIT :limit, :perPage
    `,
      params
    )
    return map(rows, (it: any) => ({
      ...omit(it, 'services_json'),
      serviceNames: JSON.parse(it?.services_json ?? '[]'),
    }))
  } catch (error) {
    logger.error({ message: 'Failed to get reviews', error, meta: params })
    return []
  }
}

async function getReviewsCount(
  serviceProviderId: number,
  searchDate: string | null = null,
  searchWord: string | null = null,
  categoryId: number | null = null,
  rating: number | null = null
) {
  const params = {
    serviceProviderId,
    searchDate,
    categoryId,
    rating,
    searchWord: searchWord ? `%${searchWord.toLowerCase()}%` : null,
  }

  try {
    const [rows] = await named_db_con.query(
      `
      SELECT COUNT(ur.id) AS count
      FROM tbl_user_review ur
      LEFT JOIN tbl_user u ON u.id = ur.user_id
      WHERE ur.service_provider_id = :serviceProviderId
      AND IF(:searchDate IS NULL, TRUE, DATE_FORMAT(ur.insertdate, '%Y-%m-%d') = :searchDate)
      AND IF(
        :categoryId IS NULL,
        TRUE,
        EXISTS(
          SELECT id
          FROM tbl_service
          WHERE ur.type_id = id
          AND ur.type = 'Service'
          AND category_id = :categoryId
        )
      )
      AND IF(:rating IS NULL, TRUE, ur.ratting >= :rating)
      AND IF(:searchWord IS NULL, TRUE, LOWER(CONCAT(u.first_name, ' ', u.last_name)) LIKE :searchWord OR LOWER(CONCAT(u.last_name, ' ', u.first_name)) LIKE :searchWord)
      AND ur.ratting > 0
      GROUP BY ur.service_provider_id
    `,
      params
    )
    return first(rows)?.count || 0
  } catch (error) {
    logger.error({
      message: 'Failed to get reviews count',
      error,
      meta: params,
    })
    return 0
  }
}

export async function getReviewList(
  serviceProviderId: number,
  page: number = 1,
  searchDate: string | null = null,
  searchWord: string | null = null,
  categoryId: number | null = null,
  rating: number | null = null,
  sortDirection: string | null = 'DESC'
) {
  const serviceProviderRating = await getRating(serviceProviderId)
  const reviews = await searchReviews(
    serviceProviderId,
    page,
    searchDate,
    searchWord,
    categoryId,
    rating,
    sortDirection
  )
  const reviewsCount = await getReviewsCount(
    serviceProviderId,
    searchDate,
    searchWord,
    categoryId,
    rating
  )
  return {
    serviceProviderId: +serviceProviderId,
    serviceProviderRating,
    reviewsCount,
    reviews,
  }
}

export async function getServiceProvidersMetadata(serviceProviderIds: number[]): Promise<ServiceProviderMetadata[]> {
  if (!serviceProviderIds) {
    return []
  }

  try {
    const [rows] = await named_db_con.query(
      `
      SELECT
        sp.id,
        GET_SERVICE_PROVIDER_RATING(sp.id, NULL) AS rating,
        GET_SERVICE_PROVIDER_REVIEW_COUNT(sp.id, NULL) AS reviewCount
      FROM tbl_service_provider sp
      WHERE sp.id IN (:serviceProviderIds)
      AND NOT sp.is_deleted
      `,
      { serviceProviderIds }
    )

    return map(rows as ServiceProviderMetadata[] ?? [], it => ({
      ...it,
      rating: +it.rating,
      reviewCount: +it.reviewCount,
    }))
  } catch (error) {
    logger.error({
      message: 'Failed to get service providers metadata',
      error,
      meta: { serviceProviderIds },
    })
    return []
  }
}

export async function getServiceProviderSchedule(
  serviceProviderId: number,
  date: string
): Promise<IServiceBookingDetails[] | null> {
  try {
    const rows = await named_db_con.query(`
      WITH temp_table AS (
        SELECT
          COALESCE(NULLIF(ab.customer_name, ''), CONCAT(tbu.last_name, ' ', tbu.first_name), tbc.customer_name) AS customer_name,
          TIMESTAMP(ab.date, ab.slot_time) AS start_time,
          ab.end_datetime,
          ab.total_duration,
          ab.additional_duration,
          ab.booking_status,
          ab.service_provider_id,
          ab.business_location_id,
          ab.total_amount,
          ab.client_id,
          ab.user_id,
          IFNULL(lc.timezone_code, 'America/Denver') AS timezone,
          lc.id AS location_id
        FROM tbl_appointment_booking ab
        LEFT JOIN tbl_user tbu ON tbu.id = ab.user_id
        LEFT JOIN tbl_service_provider_client tbc ON tbc.id = ab.client_id
        LEFT JOIN tbl_business_location lc ON ab.business_location_id = lc.id
    ), client_prior_appointments AS (
      SELECT DISTINCT ab.client_id
      FROM tbl_appointment_booking ab
      WHERE TIMESTAMP(ab.date, ab.slot_time) < TIMESTAMP(:startTime)
      AND LOWER(ab.booking_status) NOT IN ('cancelled', 'no show')
    ) SELECT
      tt.customer_name AS customerName,
      tt.start_time AS startTime,
      tt.end_datetime AS endTime,
      tt.total_duration AS totalDuration,
      tt.additional_duration AS additionalDuration,
      tt.booking_status AS bookingStatus,
      tt.service_provider_id AS serviceProviderId,
      tt.business_location_id AS businessLocationId,
      tt.total_amount AS totalAmount,
      tt.client_id AS clientId,
      tt.user_id AS userId,
      tt.timezone,
      tt.location_id AS locationId,
      NOT EXISTS(
        SELECT client_id
        FROM client_prior_appointments
        WHERE client_id = tt.client_id
      ) AS newClient
    FROM temp_table tt
    WHERE tt.service_provider_id = :spId
    AND LOWER(tt.booking_status) NOT IN ('cancelled', 'no show')
    AND CONVERT_TZ(tt.start_time, tt.timezone, 'UTC') BETWEEN TIMESTAMP(:startTime) AND DATE_ADD(TIMESTAMP(:startTime), INTERVAL 1 DAY)
    `, { spId: serviceProviderId, startTime: date })
    const formatted = (rows[0] as IServiceBookingDetails[]).map((item) => ({
      ...item,
      newClient: Boolean(item.newClient),
    }))
    return formatted.length ? formatted : null
  } catch (err) {
    logger.error(err)
    throw err
  }
}

export async function getBookmarkedServiceProviders(
  userId: number,
  page: number,
  perPage: number = Global.PER_PAGE
) {
  try {
    const [rows] = await named_db_con.query(`
      SELECT
        sp.id,
        CONCAT(sp.first_name, ' ', sp.last_name) AS name,
        sp.first_name,
        sp.last_name,
        0 AS price,
        GET_SERVICE_PROVIDER_RATING(sp.id, NULL) AS rating,
        GET_SERVICE_PROVIDER_REVIEW_COUNT(sp.id, NULL) AS review_count,
        CONCAT(:imgPrefix, sp.profile_image) AS image,
        (
          SELECT bl.name
          FROM tbl_business_location bl
          WHERE service_provider_id = sp.id
          AND NOT bl.is_deleted
          AND bl.status = 'active'
          ORDER BY id DESC
          LIMIT 1
        ) AS address
      FROM tbl_bookmark b
      JOIN tbl_service_provider sp ON sp.id = b.service_provider_id
        AND LOWER(sp.status) = 'active'
        AND NOT sp.is_deleted
      WHERE b.user_id = :userId
      AND b.status = 1
      GROUP BY sp.id, LOWER(sp.last_name), LOWER(sp.first_name)
      ORDER BY CONCAT(sp.last_name, ' ', sp.first_name)
      LIMIT :limit, :perPage
    `, { userId, limit: (page - 1) * perPage, perPage, imgPrefix: `${ Global.S3_BUCKET_ROOT }${ Global.SP_IMAGE}` })
    return rows as any[] ?? []
  } catch (error) {
    logger.error({ error, message: 'Failed to get bookmarked services', meta: { userId, page, perPage } })
    return []
  }
}

export async function deleteServiceProviderClient(
  serviceProviderId: number,
  clientId: number
): Promise<void> {
  try {
    await named_db_con.query(`
      UPDATE tbl_service_provider_client
      SET is_deleted = 1
      WHERE id = :clientId
      AND service_provider_id = :serviceProviderId
    `, {
      clientId,
      serviceProviderId
    })
  } catch (error) {
    logger.error({
      message: 'Failed to delete service provider client',
      error,
      meta: { serviceProviderId, clientId },
    })
    throw new Error(`Failed to delete the client ${ clientId }`)
  }
}
