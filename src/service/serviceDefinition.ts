import { logger } from '@/src/utils'
import { named_db_con } from '@/src/db'
import { Global } from '@/src/constants'
import global from '@/config/constant'

export async function getServiceCategories(page: number = 1) {
  const params = {
    limit: (page - 1) * Global.PER_PAGE,
    perPage: Global.PER_PAGE,
    imgPrefix: `${Global.S3_BUCKET_ROOT}${Global.PRODUCT_IMAGE}`,
  }
  try {
    const [rows] = await named_db_con.query(`
      SELECT
        id,
        id AS category_id,
        name,
        status,
        CONCAT(:imgPrefix, image_name) AS category_image,
        'Service' AS type
      FROM tbl_service_category
      WHERE is_deleted = 0
      AND LOWER(status) = 'active'
      ORDER BY id DESC
      LIMIT :limit, :perPage
    `, params)
    return rows as any[]
  } catch (e) {
    logger.error('Failed to retrieve service categories', e)
    return []
  }
}

export async function getServices(
  serviceProviderId: number | null = null,
  categoryId: number | null = null,
  userId: number | null = null,
  page: number = 1,
): Promise<any[]> {
  const limit = ((page ?? 1) - 1) * Global.PER_PAGE
  const params = {
    productImgPrefix: `${ Global.S3_BUCKET_ROOT }${ Global.PRODUCT_IMAGE }`,
    serviceProviderId,
    categoryId,
    userId,
    limit,
    perPage: Global.PER_PAGE,
  }
  try {
    const [rows] = await named_db_con.query(`
      SELECT
        s.*,
        GET_SERVICE_PROVIDER_RATING(s.service_provider_id, NULL) AS ratting,
        GET_SERVICE_PROVIDER_REVIEW_COUNT(s.service_provider_id, NULL) AS review,
        s.id as service_id,
        IF(sb.userId IS NOT NULL, 1, 0) AS bookmark_status,
        CONCAT(:productImgPrefix, s.service_image) AS service_image,
        c.name AS category_name,
        p.first_name AS service_provider_first_name,
        p.last_name AS service_provider_last_name
      FROM tbl_service s
      LEFT JOIN service_bookmarks sb ON sb.serviceId = s.id
        AND sb.userId = (
          SELECT uuid
          FROM tbl_user
          WHERE id = :userId
        )
      LEFT JOIN tbl_service_category c ON c.id = s.category_id
      JOIN tbl_service_provider p ON p.id = s.service_provider_id
        AND NOT p.is_deleted
        AND LOWER(p.status) = 'active'
      WHERE IF(:serviceProviderId IS NULL, TRUE, s.service_provider_id = :serviceProviderId)
      AND IF(:categoryId IS NULL, TRUE, s.category_id = :categoryId)
      AND NOT s.is_deleted
      ORDER BY CONCAT(service_provider_last_name, '', service_provider_first_name), c.name, s.service_name
      LIMIT :limit, :perPage
    `, params)
    return rows as any[]
  } catch (error) {
    logger.error({ message: 'Failed to retrieve services', error, meta: params })
    return []
  }
}

export async function getServicesListForServiceProvider(
  serviceProviderId: number | null = null,
  page: number = 1,
): Promise<any[]> {
  const params = {
    imgPrefix: `${ Global.S3_BUCKET_ROOT }${ Global.PRODUCT_IMAGE }`,
    serviceProviderId,
    limit: (page - 1) * Global.PER_PAGE,
    perPage: Global.PER_PAGE,
  }
  try {
    const [rows] = await named_db_con.query(`
      SELECT
        s.id,
        IFNULL(NULLIF(c.name, ''), 'OTHER') AS category_name,
        s.service_name AS name,
        s.price,
        s.ratting AS rating,
        CONCAT(:imgPrefix, s.service_image) AS image
      FROM tbl_service s
      LEFT JOIN tbl_service_category c ON c.id = s.category_id
      JOIN tbl_service_provider p ON p.id = s.service_provider_id
        AND NOT p.is_deleted
        AND LOWER(p.status) = 'active'
        AND p.id = :serviceProviderId
      WHERE NOT s.is_deleted
      ORDER BY c.name, s.service_name
      LIMIT :limit, :perPage
    `, params)
    return rows as any[]
  } catch (error) {
    logger.error({ message: 'Failed to retrieve for the service provider', error, meta: params })
    return []
  }
}

export async function getService(serviceId: number): Promise<any> {
  const params = {
    serviceId,
    imgPrefix: `${Global.S3_BUCKET_ROOT}${Global.PRODUCT_IMAGE}`,
    serviceProviderImg: `${Global.S3_BUCKET_ROOT}${Global.SP_IMAGE}`,
  }

  try {
    const [rows] = await named_db_con.query(`
      SELECT
        ts.*,
        GET_SERVICE_PROVIDER_RATING(ts.service_provider_id, NULL) AS ratting,
        GET_SERVICE_PROVIDER_REVIEW_COUNT(ts.service_provider_id, NULL) AS review,
        IFNULL(
          (
            SELECT ROUND(AVG(ratting), 2)
            FROM tbl_user_review ur
            WHERE ur.type_id = ts.id
            AND ur.type = 'service'
            AND ur.ratting > 0
          ),
          0.0
        ) AS service_rating,
        CONCAT(:imgPrefix, ts.service_image) AS service_image,
        ts.service_image AS main_service_image,
        ts.id AS service_id,
        c.name AS category_name,
        sp.first_name AS service_provider_first_name,
        sp.last_name AS service_provider_last_name,
        CONCAT(:serviceProviderImg, sp.profile_image) AS service_provider_profile_image
      FROM tbl_service ts
      LEFT JOIN tbl_service_provider sp ON sp.id = ts.service_provider_id
      LEFT JOIN tbl_service_category c ON c.id = ts.category_id
      WHERE ts.id = :serviceId
      AND NOT ts.is_deleted
      AND NOT c.is_deleted
    `, params)

    const service = (rows as any[])?.[0] ?? null
    if (!service) {
      return null
    }

    return {
      ...service,
      image: [
        { multiple_service_image: service.service_image },
        ...(await getServiceImages(serviceId)),
      ]
    }
  } catch (error) {
    logger.error({ message: 'Failed to retrieve a service', error, meta: { serviceId } })
    return null
  }
}

async function getServiceImages(serviceId: number): Promise<{ multiple_service_image: string }[]> {
  try {
    const [rows] = await named_db_con.query(`
      SELECT CONCAT(:imgPrefix, pi.image_name) AS multiple_service_image
      FROM tbl_image pi
      WHERE type_id = :serviceId
      AND type = 'service'
      AND is_deleted = '0'
      ORDER BY pi.id DESC
    `, {
      serviceId,
      imgPrefix: `${Global.S3_BUCKET_ROOT}${Global.PRODUCT_IMAGE}`,
    })

    return rows as { multiple_service_image: string }[] ?? []
  } catch (error) {
    logger.error({ message: 'Failed to retrieve service additional images', error, meta: { serviceId } })
    return []
  }
}
