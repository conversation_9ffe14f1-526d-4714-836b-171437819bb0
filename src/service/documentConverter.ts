import path from 'path'
import fs from 'fs'
import puppeteer from 'puppeteer'
import ExcelJS from 'exceljs'
import Handlebars from 'handlebars'
import { first, forEach, keys, values } from 'lodash'

export enum FILE_FORMAT {
  PDF = 'pdf',
  EXCEL = 'excel',
}

export interface PdfTemplate {
  templateFile: string
  variables: Record<string, string>[]
}

export interface ExcelTemplate {
  pageName: string
  variables: Record<string, string>[]
}

export async function createPdfBufferFromTemplates(templates: PdfTemplate[]): Promise<Buffer> {
  const combinedHtml = templates.map((template, index) => {
    const html = fs.readFileSync(
      path.join(__dirname, '..', 'html', template.templateFile),
      'utf8'
    )
    const compiledHtml = Handlebars.compile(html)({ variables: template.variables })
    return index < templates.length - 1
      ? `${compiledHtml}<div style="page-break-after: always;"></div>`
      : compiledHtml
  }).join('')

  const browser = await puppeteer.launch({ args: ['--no-sandbox', '--disable-setuid-sandbox'] })

  try {
    const page = await browser.newPage()
    await page.setContent(combinedHtml, { waitUntil: 'networkidle0' })

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20px',
        right: '20px',
        bottom: '20px',
        left: '20px',
      },
    })

    return Buffer.from(pdfBuffer)
  } finally {
    await browser.close()
  }
}

export async function createExcelBufferFromTemplates(templates: ExcelTemplate[]): Promise<Buffer> {
  const workbook = new ExcelJS.Workbook()
  forEach(templates, (template: ExcelTemplate) => {
    const worksheet = workbook.addWorksheet(template.pageName)
    worksheet.addRow(keys(first(template.variables)))
    forEach(template.variables, (variable: Record<string, string>) => worksheet.addRow(values(variable)))
  })
  return workbook.xlsx.writeBuffer() as Promise<Buffer>
}
