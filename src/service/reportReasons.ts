import { named_db_con } from '@/src/db'
import { logger } from '@/src/utils'

type ReportReason = {
  id: number
  title: string
  description: string
  status: string
  is_deleted: number
  created_at: string
  updated_at: string
}

export async function getActiveReportReasons(): Promise<ReportReason[]> {
  try {
    const [rows] = await named_db_con.query(`
      SELECT *
      FROM tbl_report
      WHERE status
      AND NOT is_deleted
    `)

    return rows as ReportReason[]
  } catch (error) {
    logger.error({
      message: 'Failed to retrieve active report reasons',
      error
    })
    return []
  }
}
