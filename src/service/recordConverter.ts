import { ExcelTemplate, PdfTemplate } from './documentConverter'
import { BookingReportRecord, ClientReportRecord, RevenueReportRecord } from './serviceProvider/reports'
import { BOOKING_STATUS } from '@/src/service/bookings'
import { map, reduce, values } from 'lodash'

export function convertBookingReportToPdfTemplate(records: BookingReportRecord[]): PdfTemplate {
  return {
    templateFile: 'bookingReportTemplate.html',
    variables: map(records, (record: BookingReportRecord) => ({
      date: record.scope_point,
      bookingCount: (record.bookings_count || 0).toString(),
      totalDuration: ((record.total_duration || 0) + (record.additional_duration || 0)).toString(),
      bookingCountDelta: (record.bookings_count_delta_percentage || 0).toString(),
      topClientName: `${ record.top_client?.first_name || '' } ${ record.top_client?.last_name || '' }`.trim() || 'N/A',
      topClientVisits: (record.top_client?.visit_count || 0).toString(),
      statuses: reduce(values(BOOKING_STATUS), (acc: any, value: string) => ({
        ...acc,
        [value]: (record.statuses?.[value] || 0).toString(),
      }), {}),
    })),
  }
}

export function convertBookingReportToExcelTemplate(records: BookingReportRecord[]): ExcelTemplate {
  return {
    pageName: 'Booking Report',
    variables: records.map(record => ({
      'Date': record.scope_point,
      'Total Bookings': (record.bookings_count || 0).toString(),
      'Total Duration': ((record.total_duration || 0) + (record.additional_duration || 0)).toString(),
      'Booking Growth': (record.bookings_count_delta_percentage || 0).toString(),
      'Top Client': `${record.top_client?.first_name || ''} ${record.top_client?.last_name || ''}`.trim() || 'N/A',
      'Top Client Visits': (record.top_client?.visit_count || 0).toString(),
      ...reduce(values(BOOKING_STATUS), (acc: any, value: string) => ({ ...acc, [value]: (record.statuses?.[value] || 0).toString() }), {}),
    })),
  }
}

export function convertClientReportToPdfTemplate(records: ClientReportRecord[]): PdfTemplate {
  return {
    templateFile: 'clientReportTemplate.html',
    variables: records.map(record => ({
      date: record.scope_point,
      totalClients: (record.user_count || 0).toString(),
      newClients: (record.new_user_count || 0).toString(),
      returningClients: (record.returning_user_count || 0).toString(),
      slippingClients: (record.slipping_user_count || 0).toString(),
      retentionRate: (record.user_retention_percentage || 0).toString(),
      newClientsDelta: (record.new_user_delta_percentage || 0).toString(),
      returningClientsDelta: (record.returning_user_delta_percentage || 0).toString(),
      slippingClientsDelta: (record.slipping_user_delta_percentage || 0).toString(),
      retentionDelta: (record.user_retention_delta_percentage || 0).toString(),
    })),
  }
}

export function convertClientReportToExcelTemplate(records: ClientReportRecord[]): ExcelTemplate {
  const rows = records.map(record => ({
    'Date': record.scope_point,
    'Total Clients': (record.user_count || 0).toString(),
    'New Clients': (record.new_user_count || 0).toString(),
    'Returning Clients': (record.returning_user_count || 0).toString(),
    'Slipping Clients': (record.slipping_user_count || 0).toString(),
    'Retention Rate': (record.user_retention_percentage || 0).toString(),
    'New Clients Growth': (record.new_user_delta_percentage || 0).toString(),
    'Returning Clients Growth': (record.returning_user_delta_percentage || 0).toString(),
    'Slipping Clients Change': (record.slipping_user_delta_percentage || 0).toString(),
    'Retention Rate Change': (record.user_retention_delta_percentage || 0).toString(),
  }))

  return {
    pageName: 'Client Report',
    variables: rows,
  }
}

export function convertRevenueReportToPdfTemplate(records: RevenueReportRecord[]): PdfTemplate {
  return {
    templateFile: 'revenueReportTemplate.html',
    variables: records.map(record => ({
      date: record.scope_point,
      totalRevenue: (record.total_revenue || 0).toString(),
      serviceRevenue: (record.service_total || 0).toString(),
      productRevenue: (record.product_total || 0).toString(),
      tipTotal: (record.tip_total || 0).toString(),
      lostRevenue: (record.lost_revenue || 0).toString(),
      averageTicket: (record.average_ticket || 0).toString(),
      topProduct: record.top_product?.name || 'N/A',
      topProductQuantity: (record.top_product?.quantity || 0).toString(),
      topService: record.top_service?.name || 'N/A',
      topServiceVisits: (record.top_service?.visits || 0).toString(),
      retailPercentage: (record.retail_percentage || 0).toString(),
      revenueDelta: (record.total_revenue_delta_percentage || 0).toString(),
      serviceDelta: (record.service_total_delta_percentage || 0).toString(),
      productDelta: (record.product_total_delta_percentage || 0).toString(),
      tipDelta: (record.tip_total_delta_percentage || 0).toString(),
      lostRevenueDelta: (record.lost_revenue_delta_percentage || 0).toString(),
      averageTicketDelta: (record.average_ticket_delta_percentage || 0).toString(),
      retailPercentageDelta: (record.retail_percentage_delta_percentage || 0).toString(),
    })),
  }
}

export function convertRevenueReportToExcelTemplate(records: RevenueReportRecord[]): ExcelTemplate {
  const rows = records.map(record => ({
    'Date': record.scope_point,
    'Total Revenue': (record.total_revenue || 0).toString(),
    'Service Revenue': (record.service_total || 0).toString(),
    'Product Revenue': (record.product_total || 0).toString(),
    'Tips': (record.tip_total || 0).toString(),
    'Lost Revenue': (record.lost_revenue || 0).toString(),
    'Average Ticket': (record.average_ticket || 0).toString(),
    'Retail %': (record.retail_percentage || 0).toString(),
    'Top Product': record.top_product?.name || 'N/A',
    'Top Product Quantity': (record.top_product?.quantity || 0).toString(),
    'Top Service': record.top_service?.name || 'N/A',
    'Top Service Visits': (record.top_service?.visits || 0).toString(),
    'Revenue Growth': (record.total_revenue_delta_percentage || 0).toString(),
    'Service Revenue Growth': (record.service_total_delta_percentage || 0).toString(),
    'Product Revenue Growth': (record.product_total_delta_percentage || 0).toString(),
    'Tips Growth': (record.tip_total_delta_percentage || 0).toString(),
    'Lost Revenue Change': (record.lost_revenue_delta_percentage || 0).toString(),
    'Average Ticket Growth': (record.average_ticket_delta_percentage || 0).toString(),
    'Retail % Change': (record.retail_percentage_delta_percentage || 0).toString(),
  }))

  return {
    pageName: 'Revenue Report',
    variables: rows,
  }
}
