import {
  convertBookingReportToExcelTemplate,
  convertBookingReportToPdfTemplate,
  convertClientReportToExcelTemplate,
  convertClientReportToPdfTemplate,
  convertRevenueReportToExcelTemplate,
  convertRevenueReportToPdfTemplate,
  generateBookingsReport,
  generateClientReport,
  generateRevenueReport,
  createExcelBufferFromTemplates,
  createPdfBufferFromTemplates,
  FILE_FORMAT,
  REPORT_DATE_SCOPE,
} from '@/src/service'

export async function generateReportsBuffer(
  serviceProviderId: number,
  scope: REPORT_DATE_SCOPE,
  startDate: string,
  endDate: string,
  timezone: string,
  fileFormat: FILE_FORMAT,
): Promise<Buffer> {
  const [bookingReports, clientReports, revenueReports] = await Promise.all([
    generateBookingsReport({ serviceProviderId, scope, startDate, endDate, timezone }),
    generateClientReport({ serviceProviderId, scope, startDate, endDate, timezone }),
    generateRevenueReport({ serviceProviderId, scope, startDate, endDate, timezone })
  ])

  switch (fileFormat) {
    case FILE_FORMAT.PDF: {
      const templates = [
        convertBookingReportToPdfTemplate(bookingReports),
        convertClientReportToPdfTemplate(clientReports),
        convertRevenueReportToPdfTemplate(revenueReports)
      ]
      return createPdfBufferFromTemplates(templates)
    }
    case FILE_FORMAT.EXCEL: {
      const templates = [
        convertBookingReportToExcelTemplate(bookingReports),
        convertClientReportToExcelTemplate(clientReports),
        convertRevenueReportToExcelTemplate(revenueReports)
      ]
      return createExcelBufferFromTemplates(templates)
    }
    default:
      throw new Error(`Unsupported file format: ${fileFormat}. Please use either PDF or Excel format.`)
  }
}
