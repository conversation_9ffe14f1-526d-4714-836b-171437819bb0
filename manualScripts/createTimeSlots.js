
const moment = require('moment-timezone')
const SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS = 'YYYY-MM-DD HH:mm'
const SIMPLE_DATE_FORMAT = 'YYYY-MM-DD'

function dateStringInTz(dateString, format, tz) {
  return moment.tz(dateString, format, tz)
}

function createTimeSlots(
  targetDate,
  targetDuration,
  providerSlot
) {
  const now = moment.tz(providerSlot.timezone_code)
  const minStartTime = moment(now).add(providerSlot.booking_buffer_interval_minutes, 'minutes')
  const maxStartTime = providerSlot.max_future_booking_window_minutes ?
    moment(now).add(providerSlot.max_future_booking_window_minutes, 'minutes') :
    null
  const fromTime = moment(providerSlot.from_time, 'hh:mm A').format('HH:mm')
  const toTime = moment(providerSlot.to_time, 'hh:mm A').format('HH:mm')
  const slotFromTime = dateStringInTz(
    `${ targetDate.format(SIMPLE_DATE_FORMAT) } ${ fromTime }`,
    SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS,
    providerSlot.timezone_code
)
  const slotToTime = dateStringInTz(
    `${ targetDate.format(SIMPLE_DATE_FORMAT) } ${ toTime }`,
    SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS,
    providerSlot.timezone_code
)
  const timeSlots = []
  const pointer = moment(slotFromTime)
  while (pointer < slotToTime) {
    if (!pointer.isSame(slotToTime, 'day')) {
      return timeSlots
    }

    const timeSlot = dateStringInTz(
      pointer.format(SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS),
      SIMPLE_DATE_FORMAT_WITH_24HR_TIME_NO_MILLS,
      providerSlot.timezone_code
  )
    pointer.add(targetDuration, 'minutes')

    if (timeSlot.isBefore(minStartTime) || (maxStartTime && timeSlot.isAfter(maxStartTime))) {
      continue
    }

    timeSlots.push(timeSlot)
  }

  return timeSlots
}

console.log(createTimeSlots(
  moment.tz('America/Los_Angeles'),
  30,
  {
    from_time: '9:00',
    to_time: '19:00',
    day: 'Wednesday',
    timezone_code: 'America/Los_Angeles',
    booking_buffer_interval_minutes: 30,
    max_future_booking_window_minutes: 0,
  }
))