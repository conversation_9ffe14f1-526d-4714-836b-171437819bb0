const PushNotifications = require('node-pushnotifications')
const { v4: uuidv4 } = require('uuid')

const NOTIFICATION_TAG = {
  BOOKING_SCHEDULED: 'new_booking_scheduled',
  BOOKING_REMINDER: 'booking_reminder',
  BOOKING_REVIEW_REMINDER: 'booking_review_reminder',
  BOOKING_CANCELLED: 'booking_cancelled',
  BOOKING_NO_SHOW: 'booking_no_show',
  BOOKING_REFUND_ISSUED: 'booking_refund_issued',
  BOOKING_RESCHEDULED: 'booking_rescheduled',
  NEW_PROMO_RECEIVED: 'new_promo_received',
  SHIFT_START_REMINDER: 'shift_start_reminder',
  BOOKING_REVIEW: 'newratereview',
}
const USER_TYPE = {
  SERVICE_PROVIDER: 'service_provider',
  USER: 'user',
  CUSTOMER: 'customer',
  ADMIN: 'admin',
}
const BUNDLE_ID = {
  SERVICE_PROVIDER: 'com.blookd.provider',
  CUSTOMER: 'com.blookd.customer',
}

const PUSH_ID = ''
const APN_PUSH_KEY = ''
const KEYID = ''
const TEAMID = ''
const DEVICE_TOKEN = ''
const data = {
  sender_id: 0,
  sender_type: USER_TYPE.SERVICE_PROVIDER,
  receiver_id: 0,
  receiver_type: USER_TYPE.USER,
  type: USER_TYPE.USER,
  tag: NOTIFICATION_TAG.BOOKING_SCHEDULED,
  message: 'You are receiving a test notification',
  title: 'Test Notification',
  isaction_id: uuidv4(),
}

async function sendNotification(data) {
  const push = new PushNotifications({
    gcm: { id: PUSH_ID },
    apn: {
      token: { key: APN_PUSH_KEY, keyId: KEYID, teamId: TEAMID },
      production: true
    },
    isAlwaysUseFCM: false
  })

  return push.send([DEVICE_TOKEN], data)
}

(async () => {
  const notification = await sendNotification({
    topic: data.receiver_type === USER_TYPE.SERVICE_PROVIDER ? BUNDLE_ID.SERVICE_PROVIDER : BUNDLE_ID.CUSTOMER,
    title: data.title,
    body: data.message,
    alert: { title: data.title, body: data.message },
    notification: { title: data.title, body: data.message },
    custom: data,
    priority: 'high',
    badge: 0,
    sound: 'default',
  })

  console.log(notification);
})()
