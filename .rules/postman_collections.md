```mdc
---
description: Use when asking to create a postman json file or collection
globs:
alwaysApply: true
---
```

# Postman Collection Guidelines

## Collection Naming
- Use descriptive names that clearly indicate the purpose and scope
- Include both the feature and the affected user types/roles
- Format: "[Feature] - [User Types/Roles]"
- Example: "Password Reset Flows - User & Service Provider"

## Request Structure
- Include all required headers (e.g., api-key)
- Only include request body when explicitly required by the API
- Include query parameters when required by the API
- Use environment variables for dynamic values ({{variable}})
- Always include api-key header with value "BLOOKD"
- Always include token header with value "{{token}}"

## Request Naming
- Format: "[User Type] - [Action]"
- Example: "User - Forgot Password", "Service Provider - Verify OTP"

## Request Description
- Include a clear, concise description of what the request does
- Example: "Send OTP for password reset"

## Environment Variables
- Define all required environment variables in the collection
- Example: base_url
- Use :variable format for path parameters (e.g., :id, :userId)
- Use {{variable}} format only for collection-level variables (e.g., {{base_url}})

## Request Organization
- Group related requests together
- Order requests in a logical flow (e.g., forgot password before verify OTP)

## File Location Policy
- Do not create a dedicated postman folder for collection JSONs
- Collections should be provided directly in the response for copying
- No need to save or commit collection files to the repository

## API Versioning
- Always include v2 prefix in the endpoint path
- Example: /v2/service_provider/:id/profile-status