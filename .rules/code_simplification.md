```mdc
---
description: Use when asking for code simplification
globs:
alwaysApply: true
---
```

# Code Simplification Guidelines

## When Simplifying Selected Code Lines

When a user selects specific lines of code and asks to simplify them:

1. Focus ONLY on the selected lines
2. Make the changes more concise while maintaining the same functionality
3. Don't modify any surrounding code
4. Use modern JavaScript/TypeScript features like optional chaining (`?.`) and nullish coalescing (`??`) when appropriate
5. Remove unnecessary intermediate variables and combine operations where possible

## Example

Before:
```typescript
const serviceRows = rows as any[]
if (!serviceRows || serviceRows.length === 0) {
  return null
}

const service = serviceRows[0]
```

After:
```typescript
const service = (rows as any[])?.[0] ?? null
```

This helps keep responses focused and efficient, exactly matching what the user wants - just simplifying the specific lines they highlighted.