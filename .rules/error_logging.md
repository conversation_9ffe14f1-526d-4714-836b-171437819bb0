---
description: Use when implementing error logging
globs:
alwaysApply: true
---

# Error Logging Guidelines

## Error Log Structure

When logging errors, follow this structured pattern:

```typescript
logger.error({
  message: string,    // Clear description of what failed
  error: Error,      // The actual error object
  meta: {            // Additional context about the error
    // Include relevant identifiers and context
    // that would help with debugging
    [key: string]: any
  }
})
```

## Key Components

1. **Message**
   - Use clear, descriptive language
   - Include relevant IDs or identifiers
   - Example: `Failed to delete image with ID ${imageId}`

2. **Error Object**
   - Pass the actual error object
   - This preserves the stack trace and error details
   - Use `error` as the parameter name for consistency

3. **Meta Data**
   - Include relevant context in a `meta` object
   - Add identifiers, parameters, or state that led to the error
   - Example: `{ imageId, userId, action: 'delete' }`

## Best Practices

- Always include the error object to preserve stack traces
- Use structured logging for better parsing and analysis
- Include relevant context in the meta object
- Keep messages clear and specific
- Use consistent parameter naming (`error` for error objects)
- Don't return values from error handlers unless necessary
- Use `Promise<void>` for functions that only need to indicate success/failure through error handling

## Example

```typescript
try {
  await someOperation()
} catch (error) {
  logger.error({
    message: 'Failed to complete operation',
    error,
    meta: {
      operationId,
      userId,
      timestamp: new Date().toISOString()
    }
  })
}
```