<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" const="text/html;charset=UTF-8" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/3.0.4/socket.io.js"></script>
    <title>Claraworld Socket</title>
    <style>
        ul {
            list-style: none;
            word-wrap: break-word;
        }
    </style>
</head>

<body>
    <header>
        <h1>Guest</h1>
    </header>

    <section id="count">
    </section>

    <section id="action_room">
    </section>



    <section id="input_zone">
        <input id="message" class="vertical-align" type="text" placeholder="Enter message" /> <br/><br/>
        <input class="vertical-align" type="radio" name="method_type" value="send_message" checked /><label>Send Request</label>
        <button id="send_message" class="vertical-align" type="button">Send</button>
        <!-- <input class="vertical-align" type="radio" name="method_type" value="send_to_client" /><label>Send to Client</label>
        <button id="leave" class="vertical-align" type="button">Leave</button>
        <button id="room" class="vertical-align" type="button">Connect To Room</button>
        <button id="leave_room" class="vertical-align" type="button">Leave To Room</button> -->
        <ul class="type_messages"></ul>
    </section>

    <script src="http://code.jquery.com/jquery-latest.min.js"></script>
</body>

</html>
<script type="text/javascript">
    $(function() {

        var FADE_TIME = 150; // ms
        var TYPING_TIMER_LENGTH = 400; // ms
        var COLORS = [
            '#e21400', '#91580f', '#f8a700', '#f78b00',
            '#58dc00', '#287b00', '#a8f07a', '#4ae8c4',
            '#3b88eb', '#3824aa', '#a700ff', '#d300e7'
        ];
        // Initialize variables
        var $window = $(window);

        var count = $("#count")
        var message = $("#message")
        var send_message = $("#send_message")
        var leave_button = $("#leave")
        var action_room = $("#action_room")
        var room = $("#room")
        var leave_room = $("#leave_room")
        var $currentInput = message.focus();
        var typing = false;
        var lastTypingTime;

        // var socket = io.connect('http://localhost:7575/socket?user_id=6&authorization=123456')
        // var socket = io.connect('http://localhost:1212/socket?user_id=2&type=vendor')

        var SOCKET_URL = 'http://' + window.location.hostname + ':1212/socket?user_id=0&type=admin';

        var number = Math.floor(Math.random() * 1000) + 1;
        var socket = io.connect(SOCKET_URL + 'socket', {
            query: "user_id=" + 0
        });
        console.log(socket);

        //Listen on send_message
        socket.on("send_chat", (data) => {
            console.log(typeof(data));
            action_room.append("<p class='message'>" + JSON.stringify(data) + "</p>")
        })

        //Listen on delivwred message
        socket.on("deliver_message", (data) => {
            action_room.append("<p class='message'>" + JSON.stringify(data) + "</p>")
        })

        //Listen on delivwred message
        socket.on("read_message", (data) => {
            action_room.append("<p class='message'>" + JSON.stringify(data) + "</p>")
        })

        //Listen on delivwred message
        socket.on("room", (data) => {
            action_room.append("<p class='message'>Room Connets" + JSON.stringify(data) + "</p>")
        })

        //Listen on delivwred message
        socket.on("socket_err", (data) => {
            alert(data);
            action_room.append("<p class='message'>Room Leave" + JSON.stringify(data) + "</p>")
        })
        var c = 0;
        send_message.click(function() {
            var msg = message.val()
            var method_type = $('input[name=method_type]:checked').val();
            message.val('')
            if (method_type == 'send_chat') {
                //alert("Hey")
                //{"chat_room_id":"5f22d46d53254c1aacb9d6b3","user_id":"5f0c87348e0cf40de89bd0db","body":{"type":"text", "message":"new one to two msg"}}
                /*
                body: {"type":"document", "file_name":"123bxr.docx"} OR body: {"type":"image", "file_name":"abc.jpeg"} OR body: {"type":"audio", "file_name":"abc.mp3"} OR body: {"type":"location", "address":"Ganesh Meridian, Ahmedabad, Gujarat, India", "latitude":"23.075426","longitude":"72.525646", "file":"abc.png"} OR body: {"type":"contact", "phone":"+9636988666666"}
                */
                socket.emit('send_chat', {
                    "chat_id": "4",
                    "user_id": "2",
                    "message": "Hello this text message",
                    "message_type": "text"
                })

                /*if (c % 2 == 0) {
                    socket.emit('send_message', {
                        "chat_id": "1",
                        "user_id": "6",
                        "message": "Hello this text message",
                        "message_type": "text"
                    })
                } else {
                    socket.emit('send_message', {
                        "chat_id": "2",
                        "user_id": "6",
                        "message": "Hello this text message",
                        "message_type": "text"
                    })
                }
                count.append("<p class='message'>" + c + "</p>")
                c++;*/

            }
        })





        socket.on('typing', function(data) {
            addChatTyping(data);
        });

        socket.on('stop typing', function(data) {

            removeChatTyping(data);
        });

        function addChatTyping(data) {

            var typingClass = data.typing ? 'typing ... ' : '';
            $('.type_messages').html("<li>" + typingClass + "</li>")
        }

        function removeChatTyping(data) {

            var stop_typingClass = data.stop_typing ? 'stop typing' : '';
            $('.type_messages').html("<li>" + stop_typingClass + "</li>")
        }


        $("#message").keypress(function(event) {
            socket.emit('typing', {
                'receiver_id': "4"
            });
        })

        $("#message").focusout(function(event) {
            socket.emit('stop typing', {
                'receiver_id': "4"
            });
        })

    });
</script>