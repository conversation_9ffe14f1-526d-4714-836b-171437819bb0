name: Release API

on:
  release:
   types:
    - published
  workflow_dispatch:

jobs:
  Build-And-Push:
    name: Release API
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup AWS ECR Details
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-2

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
        id: ecr-login

      - name: Build and push the tagged docker image to Amazon ECR
        run: |
          docker build -t ${{ steps.ecr-login.outputs.registry }}/api-server:${{ github.sha }} --build-arg DOTENV_KEY=${{ secrets.DOTENV_KEY }} .
          docker push ${{ steps.ecr-login.outputs.registry }}/api-server:${{ github.sha }}

      - name: EC2 stop/start the release
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.AWS_EC2_HOST }}
          username: ubuntu
          key: ${{ secrets.AWS_EC2_KEY }}
          script: |
            set -e
            echo "Starting deployment..."

            # Login to ECR
            aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-2.amazonaws.com

            # Pull new image
            echo "Pulling new image..."
            docker pull ${{ steps.ecr-login.outputs.registry }}/api-server:${{ github.sha }}

            # Stop and remove old container
            echo "Stopping old container..."
            docker stop api-server 2>/dev/null || true
            echo "Removing old container..."
            docker rm api-server 2>/dev/null || true

            # Start new container
            echo "Starting new container..."
            docker run --name api-server -d \
              --restart unless-stopped \
              -p 8080:8080 \
              --log-driver=awslogs \
              --log-opt awslogs-region=us-east-2 \
              --log-opt awslogs-group=api-server \
              --log-opt awslogs-create-group=true \
              ${{ steps.ecr-login.outputs.registry }}/api-server:${{ github.sha }}

            # Verification steps
            echo "Verifying deployment..."
            sleep 10

            # Check if container exists and is running
            if ! docker ps | grep api-server; then
              echo "Container not running after deployment"
              docker logs api-server
              exit 1
            fi

            # Clean up old images
            echo "Cleaning up old images..."
            docker image prune -a -f

            echo "Deployment completed successfully"
            docker ps
            true
