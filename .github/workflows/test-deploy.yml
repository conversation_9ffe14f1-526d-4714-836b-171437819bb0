name: Deploy Test API

on:
  workflow_dispatch:

jobs:
  Build-And-Push:
    name: Build API
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup AWS ECR Details
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-2

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
        id: ecr-login

      - name: Build and push the tagged docker image to Amazon ECR
        run: |
          docker build -t ${{ steps.ecr-login.outputs.registry }}/test-api-server:${{ github.sha }} --build-arg DOTENV_KEY=${{ secrets.DEV_DOTENV_KEY }} .
          docker push ${{ steps.ecr-login.outputs.registry }}/test-api-server:${{ github.sha }}

      - name: EC2 stop/start the release
        uses: fifsky/ssh-action@master
        with:
          user: ubuntu
          key: ${{ secrets.AWS_EC2_KEY }}
          host: ${{ secrets.AWS_TEST_EC2_HOST }}
          command: |
            aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-2.amazonaws.com
            echo Printing container status...
            docker ps
            echo Pulling image...
            docker pull ${{ steps.ecr-login.outputs.registry }}/test-api-server:${{ github.sha }}
            echo Stopping old version container...
            docker stop api-server > deployment.info 2>&1
            echo Removing old version container...
            docker rm api-server >> deployment.info 2>&1
            echo Printing errors if exist...
            cat deployment.info 2> /dev/null
            echo Starting new version container...
            docker run --name api-server --detach -p 8080:8080 -d --restart unless-stopped --log-driver=awslogs --log-opt awslogs-region=us-east-2 --log-opt awslogs-group=test-api-server --log-opt awslogs-create-group=true ${{ steps.ecr-login.outputs.registry }}/test-api-server:${{ github.sha }}
            echo Printing container status...
            echo Removing unused images
            docker image prune -a -f
            echo Process completed
            docker ps
