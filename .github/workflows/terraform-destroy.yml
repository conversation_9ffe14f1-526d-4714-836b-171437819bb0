name: "Terraform Destroy Infrastructure"

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        default: test
        description: Select the environment
        options:
          - test
          - dev
          - production

jobs:
  terraform_destroy:
    name: "Terraform Destroy"
    runs-on: ubuntu-latest
    env:
      TF_LOG: INFO
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_DEFAULT_REGION: us-east-2
    defaults:
      run:
        working-directory: ./terraform/environments/${{ github.event.inputs.environment }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}
          role-duration-seconds: 1200

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3

      - name: Terraform Init
        id: init
        run: terraform init

      - name: Terraform Destroy
        id: destroy
        run: terraform destroy -auto-approve
