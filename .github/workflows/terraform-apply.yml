name: "Terraform Create Infrastructure"

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        default: test
        description: Select the environment
        options:
          - test
          - dev
          - production

jobs:
  terraform:
    name: "Terraform Infrastructure Change Management"
    runs-on: ubuntu-latest
    env:
      TF_LOG: INFO
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_DEFAULT_REGION: us-east-2
    defaults:
      run:
        shell: bash
        working-directory: ./terraform/environments/${{ github.event.inputs.environment }}

    steps:
      - name: Checkout the repository to the runner
        uses: actions/checkout@v3

      - name: Configure A<PERSON> credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}
          role-duration-seconds: 1200

      - name: Setup Terraform with specified version on the runner
        uses: hashicorp/setup-terraform@v1
        with:
          terraform_version: 1.2.2

      - name: Deploy Terraform
        run: |
          aws sts get-caller-identity
          terraform -version
          terraform init
          terraform validate
          terraform apply -auto-approve
