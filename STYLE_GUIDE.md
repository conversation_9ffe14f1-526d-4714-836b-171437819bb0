# Code Style Guide

## General
- Use single quotes for strings unless using string interpolation
- Use 2-space indentation
- Remove all comments except TODO comments that explain non-obvious workarounds or business logic

## Code Cleanup
- Remove unused imports
- Remove unused variables
- Remove unused functions
- Remove unused parameters
- Remove unused types and interfaces
- Remove dead code paths

## Error Handling
Use consistent error logging format:
```typescript
logger.error({
  message: 'Descriptive error message',
  error,
  meta: { relevantContextData }
})
```

## TypeScript
- Remove redundant type annotations when TypeScript can infer them
- Remove unnecessary type assertions
- Remove unnecessary parentheses in conditions

## Examples

### ❌ Don't
```typescript
// Regular comment explaining what the code does
import { unused } from './unused'
const unusedVar = 'never used'

// Function to handle error
function handleError(err: Error, extraParam: string) { // extraParam is never used
  logger.error('Error occurred: ' + err.message)
}
```

### ✅ Do
```typescript
/* TODO: this endpoint needs to be refactored once the mobile app
   is updated to handle the new response format */
import { required } from './required'

function handleError(err: Error) {
  logger.error({
    message: 'Failed to process request',
    error: err,
    meta: { endpoint: '/api/v1/process' }
  })
}
```