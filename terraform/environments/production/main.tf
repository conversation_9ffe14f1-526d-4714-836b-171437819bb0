provider "aws" {
  region = var.region
}

data "aws_caller_identity" "identity" {}

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
      version = "~> 4.16"
    }
  }

  backend "s3" {
    bucket = "blookd-terraform-states"
    key = "api/terraform.tfstate"
    region = "us-east-1"
  }

  required_version = ">= 1.2.0"
}

### ECR
resource "aws_ecr_repository" "ecr" {
  name = var.ecr_name
  image_tag_mutability = "MUTABLE"
  force_delete = true
  tags = {
    project = var.project
  }
}

### EC2
resource "aws_default_vpc" "default_vpc" {
  tags = {
    Name = "Default"
    project = "blookd"
  }
}

resource "aws_security_group" "ec2_sg" {
  name = var.ec2_sg_name
  description = "Security group for api ec2"
  ingress {
    from_port = 22
    to_port = 22
    protocol = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port = 80
    to_port = 80
    protocol = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port = 443
    to_port = 443
    protocol = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port = 0
    to_port = 0
    protocol = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  vpc_id = aws_default_vpc.default_vpc.id

  tags = {
    Name = var.ec2_sg_name
    project = var.project
  }
}

data "aws_ami" "ec2_ubuntu" {
  most_recent = true
  owners = ["amazon"]
  filter {
    name = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-*"]
  }
  filter {
    name = "virtualization-type"
    values = ["hvm"]
  }
}

resource "aws_iam_role" "ec2_role" {
  name = var.ec2_role_name
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
  tags = {
    project = var.project
  }
}

resource "aws_iam_instance_profile" "ec2_api_profile" {
  name = var.ec2_profile_name
  role = aws_iam_role.ec2_role.name
}

resource "aws_iam_role_policy" "ec2_policy" {
  name = var.ec2_policy_name
  role = aws_iam_role.ec2_role.id
  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "ecr:GetAuthorizationToken",
        "ecr:BatchGetImage",
        "ecr:GetDownloadUrlForLayer",
        "ecr:DescribeImages",
        "ecr:DescribeRepositories",
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents",
        "logs:DescribeLogStreams",
        "s3:*"
      ],
      "Effect": "Allow",
      "Resource": "*"
    }
  ]
}
EOF
}

data "template_file" "user_data" {
  template ="${ file("install.sh") }"
  vars = {
    region = var.region
    accountId = data.aws_caller_identity.identity.account_id
    host = var.host
    email = var.email
  }
}

resource "aws_instance" "ec2" {
  ami = data.aws_ami.ec2_ubuntu.id
  instance_type = "t2.small"
  key_name = "github-actions"

  user_data = data.template_file.user_data.rendered

  vpc_security_group_ids = [aws_security_group.ec2_sg.id]
  iam_instance_profile = aws_iam_instance_profile.ec2_api_profile.name

  tags = {
    Name = var.ec2_name
    project = var.project
  }

  monitoring = true
}

resource "aws_eip" "ec2_eip" {
  instance = aws_instance.ec2.id
}

data "aws_route53_zone" "hosted_zone" {
  name = var.hosted_zone
  private_zone = false
}

resource "aws_route53_record" "host" {
  zone_id = data.aws_route53_zone.hosted_zone.zone_id
  name = var.host
  type = "A"
  ttl = 300
  records = [aws_eip.ec2_eip.public_ip]
}
