#!/bin/bash
sudo apt-add-repository -r ppa:certbot/certbot -y
sudo apt-get update -y
sudo apt-get install awscli -y
sudo apt-get install docker.io -y
sudo apt-get install nginx -y
sudo apt-get install certbot python3-certbot-nginx -y

sudo chmod 666 /var/run/docker.sock
sudo getent group docker || sudo groupadd docker && sudo usermod -a -G docker $(whoami)
sudo getent group nginx || sudo groupadd nginx && sudo usermod -a -G nginx $(whoami)

sudo echo "server {
  listen 80;

  server_name ${host};

  location / {
    proxy_pass http://localhost:8080/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade \$http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host \$host;
    proxy_cache_bypass \$http_upgrade;
  }
}" > /etc/nginx/sites-available/default

# in case of nginx errors
#sudo fuser -k 443/tcp
#sudo fuser -k 80/tcp
#sudo service nginx restart
#sudo certbot --nginx
sudo systemctl stop nginx
sudo certbot --nginx -d ${host} --email ${email} -n --agree-tos

sudo systemctl start docker
sudo systemctl enable docker
sudo systemctl start nginx
sudo systemctl enable nginx

aws ecr get-login-password --region ${region} | docker login --username AWS --password-stdin ${accountId}.dkr.ecr.${region}.amazonaws.com
