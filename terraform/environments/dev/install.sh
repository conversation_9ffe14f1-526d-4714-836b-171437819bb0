#!/bin/bash
set -e

# Log output to a file for debugging
exec 1> >(logger -s -t $(basename $0)) 2>&1

# Install required packages
install_requirements() {
    sudo apt-get update
    sudo apt-get install -y nginx docker.io awscli net-tools certbot python3-certbot-nginx || exit 1
}

# Configure AWS and ECR
setup_aws() {
    aws configure set region ${region}
    aws ecr get-login-password --region ${region} | sudo docker login --username AWS --password-stdin ${accountId}.dkr.ecr.${region}.amazonaws.com
}

# Setup SSL certificates
setup_ssl() {
    if [ ! -d "/etc/letsencrypt/live/${host}" ]; then
        sudo certbot --nginx -d ${host} --non-interactive --agree-tos --email ${email}
    fi

    sudo mkdir -p /etc/letsencrypt/renewal-hooks/deploy
    sudo tee /etc/letsencrypt/renewal-hooks/deploy/nginx-reload.sh > /dev/null << 'EOF'
#!/bin/bash
systemctl reload nginx
EOF
    sudo chmod +x /etc/letsencrypt/renewal-hooks/deploy/nginx-reload.sh

    sudo tee /etc/cron.d/certbot > /dev/null << 'EOF'
0 */12 * * * root certbot renew --quiet --deploy-hook "systemctl reload nginx"
EOF
    sudo chmod 644 /etc/cron.d/certbot
}

# Configure Docker
setup_docker() {
    sudo systemctl start docker
    sudo systemctl enable docker

    sudo docker pull ${accountId}.dkr.ecr.${region}.amazonaws.com/${ecrName}:latest

    sudo docker stop ${project}-server 2>/dev/null || true
    sudo docker rm ${project}-server 2>/dev/null || true

    sudo docker run -d \
        --name ${project}-server \
        --restart unless-stopped \
        -p 8080:8080 \
        ${accountId}.dkr.ecr.${region}.amazonaws.com/${ecrName}:latest
}

# Configure Nginx
setup_nginx() {
    sudo tee /etc/nginx/sites-available/default > /dev/null << EOF
server {
    listen 80;
    listen [::]:80;
    server_name ${host};
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name ${host};

    ssl_certificate /etc/letsencrypt/live/${host}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/${host}/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_stapling on;
    ssl_stapling_verify on;

    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF

    sudo nginx -t
    sudo systemctl restart nginx
}

# Main installation process
main() {
    if ! install_requirements; then
        echo "Failed to install requirements"
        exit 1
    fi

    if ! setup_aws; then
        echo "Failed to setup AWS"
        exit 1
    fi

    if ! setup_ssl; then
        echo "Failed to setup SSL"
        exit 1
    fi

    if ! setup_docker; then
        echo "Failed to setup Docker"
        exit 1
    fi

    if ! setup_nginx; then
        echo "Failed to setup Nginx"
        exit 1
    fi

    echo "Installation completed successfully"
}

# Run main and capture any errors
if ! main; then
    echo "Installation failed"
    exit 1
fi
