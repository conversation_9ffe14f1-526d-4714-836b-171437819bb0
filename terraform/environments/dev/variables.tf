variable "region" {
  type = string
  default = "us-east-2"
}

variable "email" {
  type = string
  default = "<EMAIL>"
}

variable "host" {
  type = string
  default = "dev.api.blookd.com"
}

variable "hosted_zone" {
  type = string
  default = "blookd.com"
}

variable "ec2_name" {
  type = string
  default = "api-dev"
}

variable "project" {
  type = string
  default = "api"
}

variable "ecr_name" {
  type = string
  default = "dev-api-server"
}

variable "ec2_sg_name" {
  type = string
  default = "api_ec2_dev_sg"
}

variable "ec2_role_name" {
  type = string
  default = "ec2_api_dev_role"
}

variable "ec2_profile_name" {
  type = string
  default = "ec2_api_dev_profile"
}

variable "ec2_policy_name" {
  type = string
  default = "ec2_api_dev_policy"
}
